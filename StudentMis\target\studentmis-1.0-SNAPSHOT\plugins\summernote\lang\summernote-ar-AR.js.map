{"version": 3, "file": "lang/summernote-ar-AR.js", "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAT,EAAY;AACXA,EAAAA,CAAC,CAACC,MAAF,CAASD,CAAC,CAACE,UAAF,CAAaC,IAAtB,EAA4B;AAC1B,aAAS;AACPC,MAAAA,IAAI,EAAE;AACJC,QAAAA,IAAI,EAAE,MADF;AAEJC,QAAAA,MAAM,EAAE,MAFJ;AAGJC,QAAAA,SAAS,EAAE,SAHP;AAIJC,QAAAA,KAAK,EAAE,aAJH;AAKJC,QAAAA,MAAM,EAAE,cALJ;AAMJC,QAAAA,IAAI,EAAE,MANF;AAOJC,QAAAA,aAAa,EAAE,YAPX;AAQJC,QAAAA,SAAS,EAAE,QARP;AASJC,QAAAA,WAAW,EAAE,UATT;AAUJC,QAAAA,IAAI,EAAE;AAVF,OADC;AAaPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,MADF;AAELC,QAAAA,MAAM,EAAE,YAFH;AAGLC,QAAAA,UAAU,EAAE,eAHP;AAILC,QAAAA,UAAU,EAAE,aAJP;AAKLC,QAAAA,aAAa,EAAE,aALV;AAMLC,QAAAA,SAAS,EAAE,cANN;AAOLC,QAAAA,UAAU,EAAE,cAPP;AAQLC,QAAAA,SAAS,EAAE,OARN;AASLC,QAAAA,YAAY,EAAE,cATT;AAULC,QAAAA,WAAW,EAAE,cAVR;AAWLC,QAAAA,cAAc,EAAE,mBAXX;AAYLC,QAAAA,SAAS,EAAE,eAZN;AAaLC,QAAAA,aAAa,EAAE,iBAbV;AAcLC,QAAAA,SAAS,EAAE,kBAdN;AAeLC,QAAAA,eAAe,EAAE,SAfZ;AAgBLC,QAAAA,eAAe,EAAE,wBAhBZ;AAiBLC,QAAAA,oBAAoB,EAAE,iCAjBjB;AAkBLC,QAAAA,GAAG,EAAE,aAlBA;AAmBLC,QAAAA,MAAM,EAAE,YAnBH;AAoBLC,QAAAA,QAAQ,EAAE;AApBL,OAbA;AAmCPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,SAAS,EAAE,cAFN;AAGLpB,QAAAA,MAAM,EAAE,eAHH;AAILgB,QAAAA,GAAG,EAAE,cAJA;AAKLK,QAAAA,SAAS,EAAE;AALN,OAnCA;AA0CPC,MAAAA,IAAI,EAAE;AACJA,QAAAA,IAAI,EAAE,MADF;AAEJtB,QAAAA,MAAM,EAAE,OAFJ;AAGJuB,QAAAA,MAAM,EAAE,YAHJ;AAIJC,QAAAA,IAAI,EAAE,OAJF;AAKJC,QAAAA,aAAa,EAAE,MALX;AAMJT,QAAAA,GAAG,EAAE,aAND;AAOJU,QAAAA,eAAe,EAAE;AAPb,OA1CC;AAmDPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,MADF;AAELC,QAAAA,WAAW,EAAE,iBAFR;AAGLC,QAAAA,WAAW,EAAE,iBAHR;AAILC,QAAAA,UAAU,EAAE,iBAJP;AAKLC,QAAAA,WAAW,EAAE,iBALR;AAMLC,QAAAA,MAAM,EAAE,SANH;AAOLC,QAAAA,MAAM,EAAE,UAPH;AAQLC,QAAAA,QAAQ,EAAE;AARL,OAnDA;AA6DPC,MAAAA,EAAE,EAAE;AACFnC,QAAAA,MAAM,EAAE;AADN,OA7DG;AAgEPoC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,CAAC,EAAE,MAFE;AAGLC,QAAAA,UAAU,EAAE,QAHP;AAILC,QAAAA,GAAG,EAAE,OAJA;AAKLC,QAAAA,EAAE,EAAE,eALC;AAMLC,QAAAA,EAAE,EAAE,eANC;AAOLC,QAAAA,EAAE,EAAE,eAPC;AAQLC,QAAAA,EAAE,EAAE,eARC;AASLC,QAAAA,EAAE,EAAE,eATC;AAULC,QAAAA,EAAE,EAAE;AAVC,OAhEA;AA4EPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,SAAS,EAAE,cADN;AAELC,QAAAA,OAAO,EAAE;AAFJ,OA5EA;AAgFPC,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,QADC;AAEPC,QAAAA,UAAU,EAAE,oBAFL;AAGPC,QAAAA,QAAQ,EAAE;AAHH,OAhFF;AAqFPC,MAAAA,SAAS,EAAE;AACTA,QAAAA,SAAS,EAAE,MADF;AAETC,QAAAA,OAAO,EAAE,eAFA;AAGTC,QAAAA,MAAM,EAAE,eAHC;AAITC,QAAAA,IAAI,EAAE,eAJG;AAKTC,QAAAA,MAAM,EAAE,OALC;AAMTC,QAAAA,KAAK,EAAE,eANE;AAOTC,QAAAA,OAAO,EAAE;AAPA,OArFJ;AA8FPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,MAAM,EAAE,aADH;AAELC,QAAAA,IAAI,EAAE,QAFD;AAGLC,QAAAA,UAAU,EAAE,aAHP;AAILC,QAAAA,UAAU,EAAE,UAJP;AAKLC,QAAAA,WAAW,EAAE,MALR;AAMLC,QAAAA,cAAc,EAAE,YANX;AAOLC,QAAAA,KAAK,EAAE,aAPF;AAQLC,QAAAA,cAAc,EAAE,aARX;AASLC,QAAAA,QAAQ,EAAE;AATL,OA9FA;AAyGPC,MAAAA,QAAQ,EAAE;AACRC,QAAAA,SAAS,EAAE,UADH;AAERC,QAAAA,KAAK,EAAE,KAFC;AAGRC,QAAAA,cAAc,EAAE,YAHR;AAIRC,QAAAA,MAAM,EAAE,QAJA;AAKRC,QAAAA,mBAAmB,EAAE,cALb;AAMRC,QAAAA,aAAa,EAAE,eANP;AAORC,QAAAA,SAAS,EAAE;AAPH,OAzGH;AAkHP3B,MAAAA,IAAI,EAAE;AACJ,2BAAmB,YADf;AAEJ,gBAAQ,kBAFJ;AAGJ,gBAAQ,qBAHJ;AAIJ,eAAO,aAJH;AAKJ,iBAAS,yBALL;AAMJ,gBAAQ,YANJ;AAOJ,kBAAU,YAPN;AAQJ,qBAAa,eART;AASJ,yBAAiB,qBATb;AAUJ,wBAAgB,iBAVZ;AAWJ,uBAAe,eAXX;AAYJ,yBAAiB,cAZb;AAaJ,wBAAgB,eAbZ;AAcJ,uBAAe,cAdX;AAeJ,+BAAuB,cAfnB;AAgBJ,6BAAqB,cAhBjB;AAiBJ,mBAAW,iCAjBP;AAkBJ,kBAAU,gCAlBN;AAmBJ,sBAAc,uCAnBV;AAoBJ,oBAAY,2CApBR;AAqBJ,oBAAY,2CArBR;AAsBJ,oBAAY,2CAtBR;AAuBJ,oBAAY,2CAvBR;AAwBJ,oBAAY,2CAxBR;AAyBJ,oBAAY,2CAzBR;AA0BJ,gCAAwB,eA1BpB;AA2BJ,2BAAmB;AA3Bf,OAlHC;AA+IP4B,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,OADC;AAEPC,QAAAA,IAAI,EAAE;AAFC,OA/IF;AAmJPC,MAAAA,WAAW,EAAE;AACXA,QAAAA,WAAW,EAAE,YADF;AAEXC,QAAAA,MAAM,EAAE;AAFG;AAnJN;AADiB,GAA5B;AA0JD,CA3JD,EA2JGC,MA3JH", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-ar-AR.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, function() {\nreturn ", "(function($) {\n  $.extend($.summernote.lang, {\n    'ar-AR': {\n      font: {\n        bold: 'عريض',\n        italic: 'مائل',\n        underline: 'تحته خط',\n        clear: 'مسح التنسيق',\n        height: 'إرتفاع السطر',\n        name: 'الخط',\n        strikethrough: 'فى وسطه خط',\n        subscript: 'مخطوطة',\n        superscript: 'حرف فوقي',\n        size: 'الحجم',\n      },\n      image: {\n        image: 'صورة',\n        insert: 'إضافة صورة',\n        resizeFull: 'الحجم بالكامل',\n        resizeHalf: 'تصغير للنصف',\n        resizeQuarter: 'تصغير للربع',\n        floatLeft: 'تطيير لليسار',\n        floatRight: 'تطيير لليمين',\n        floatNone: 'ثابته',\n        shapeRounded: 'الشكل: تقريب',\n        shapeCircle: 'الشكل: دائرة',\n        shapeThumbnail: 'الشكل: صورة مصغرة',\n        shapeNone: 'الشكل: لا شيء',\n        dragImageHere: 'إدرج الصورة هنا',\n        dropImage: 'إسقاط صورة أو نص',\n        selectFromFiles: 'حدد ملف',\n        maximumFileSize: 'الحد الأقصى لحجم الملف',\n        maximumFileSizeError: 'تم تجاوز الحد الأقصى لحجم الملف',\n        url: 'رابط الصورة',\n        remove: 'حذف الصورة',\n        original: 'Original',\n      },\n      video: {\n        video: 'فيديو',\n        videoLink: 'رابط الفيديو',\n        insert: 'إدراج الفيديو',\n        url: 'رابط الفيديو',\n        providers: '(YouTube, Google Drive, Vimeo, Vine, Instagram, DailyMotion or Youku)',\n      },\n      link: {\n        link: 'رابط',\n        insert: 'إدراج',\n        unlink: 'حذف الرابط',\n        edit: 'تعديل',\n        textToDisplay: 'النص',\n        url: 'مسار الرابط',\n        openInNewWindow: 'فتح في نافذة جديدة',\n      },\n      table: {\n        table: 'جدول',\n        addRowAbove: 'إضافة سطر أعلاه',\n        addRowBelow: 'إضافة سطر أدناه',\n        addColLeft: 'إضافة عمود قبله',\n        addColRight: 'إضافة عمود بعده',\n        delRow: 'حذف سطر',\n        delCol: 'حذف عمود',\n        delTable: 'حذف الجدول',\n      },\n      hr: {\n        insert: 'إدراج خط أفقي',\n      },\n      style: {\n        style: 'تنسيق',\n        p: 'عادي',\n        blockquote: 'إقتباس',\n        pre: 'شفيرة',\n        h1: 'عنوان رئيسي 1',\n        h2: 'عنوان رئيسي 2',\n        h3: 'عنوان رئيسي 3',\n        h4: 'عنوان رئيسي 4',\n        h5: 'عنوان رئيسي 5',\n        h6: 'عنوان رئيسي 6',\n      },\n      lists: {\n        unordered: 'قائمة مُنقطة',\n        ordered: 'قائمة مُرقمة',\n      },\n      options: {\n        help: 'مساعدة',\n        fullscreen: 'حجم الشاشة بالكامل',\n        codeview: 'شفيرة المصدر',\n      },\n      paragraph: {\n        paragraph: 'فقرة',\n        outdent: 'محاذاة للخارج',\n        indent: 'محاذاة للداخل',\n        left: 'محاذاة لليسار',\n        center: 'توسيط',\n        right: 'محاذاة لليمين',\n        justify: 'ملئ السطر',\n      },\n      color: {\n        recent: 'تم إستخدامه',\n        more: 'المزيد',\n        background: 'لون الخلفية',\n        foreground: 'لون النص',\n        transparent: 'شفاف',\n        setTransparent: 'بدون خلفية',\n        reset: 'إعادة الضبط',\n        resetToDefault: 'إعادة الضبط',\n        cpSelect: 'اختار',\n      },\n      shortcut: {\n        shortcuts: 'إختصارات',\n        close: 'غلق',\n        textFormatting: 'تنسيق النص',\n        action: 'Action',\n        paragraphFormatting: 'تنسيق الفقرة',\n        documentStyle: 'تنسيق المستند',\n        extraKeys: 'أزرار إضافية',\n      },\n      help: {\n        'insertParagraph': 'إدراج فقرة',\n        'undo': 'تراجع عن آخر أمر',\n        'redo': 'إعادة تنفيذ آخر أمر',\n        'tab': 'إزاحة (تاب)',\n        'untab': 'سحب النص باتجاه البداية',\n        'bold': 'تنسيق عريض',\n        'italic': 'تنسيق مائل',\n        'underline': 'تنسيق خط سفلي',\n        'strikethrough': 'تنسيق خط متوسط للنص',\n        'removeFormat': 'إزالة التنسيقات',\n        'justifyLeft': 'محاذاة لليسار',\n        'justifyCenter': 'محاذاة توسيط',\n        'justifyRight': 'محاذاة لليمين',\n        'justifyFull': 'محاذاة كاملة',\n        'insertUnorderedList': 'قائمة منقّطة',\n        'insertOrderedList': 'قائمة مرقّمة',\n        'outdent': 'إزاحة للأمام على الفقرة الحالية',\n        'indent': 'إزاحة للخلف على الفقرة الحالية',\n        'formatPara': 'تغيير التنسيق للكتلة الحالية إلى فقرة',\n        'formatH1': 'تغيير التنسيق للكتلة الحالية إلى ترويسة 1',\n        'formatH2': 'تغيير التنسيق للكتلة الحالية إلى ترويسة 2',\n        'formatH3': 'تغيير التنسيق للكتلة الحالية إلى ترويسة 3',\n        'formatH4': 'تغيير التنسيق للكتلة الحالية إلى ترويسة 4',\n        'formatH5': 'تغيير التنسيق للكتلة الحالية إلى ترويسة 5',\n        'formatH6': 'تغيير التنسيق للكتلة الحالية إلى ترويسة 6',\n        'insertHorizontalRule': 'إدراج خط أفقي',\n        'linkDialog.show': 'إظهار خصائص الرابط',\n      },\n      history: {\n        undo: 'تراجع',\n        redo: 'إعادة',\n      },\n      specialChar: {\n        specialChar: 'محارف خاصة',\n        select: 'اختر المحرف الخاص',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "cpSelect", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}