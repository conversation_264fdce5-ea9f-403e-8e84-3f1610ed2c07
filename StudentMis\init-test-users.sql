-- StudentMIS 测试用户初始化脚本
-- 使用数据库
USE studentmis_db;

-- 清空现有用户数据（可选）
-- DELETE FROM user;

-- 插入测试用户数据
INSERT INTO user (user_num, user_name, password, phone, role_id) VALUES
-- 管理员账号
('admin', '系统管理员', '123456', '13800138000', 2),
('1000', '管理员', 'Lcd123', '18179586325', 2),

-- 教师账号  
('T001', '张教授', '123456', '13800138001', 1),
('T002', '李老师', '123456', '13800138002', 1),
('1123', '肖兴江', 'Lcd123', '17418953551', 1),

-- 学生账号
('2024001', '张三', '123456', '13800138101', 0),
('2024002', '李四', '123456', '13800138102', 0),
('2024003', '王五', '123456', '13800138103', 0),
('170340', '张三', 'Lcd123', '15869483651', 0),
('170339', '李四', 'Lcd123', '13589462584', 0),
('160341', '王五', 'Lcd123', '14829726746', 0);

-- 验证插入结果
SELECT 
    user_num AS '用户名',
    user_name AS '姓名', 
    password AS '密码',
    CASE role_id 
        WHEN 0 THEN '学生'
        WHEN 1 THEN '教师' 
        WHEN 2 THEN '管理员'
        ELSE '未知'
    END AS '角色'
FROM user 
ORDER BY role_id DESC, user_num;

-- 显示统计信息
SELECT 
    COUNT(*) AS '总用户数',
    SUM(CASE WHEN role_id = 2 THEN 1 ELSE 0 END) AS '管理员数',
    SUM(CASE WHEN role_id = 1 THEN 1 ELSE 0 END) AS '教师数',
    SUM(CASE WHEN role_id = 0 THEN 1 ELSE 0 END) AS '学生数'
FROM user;
