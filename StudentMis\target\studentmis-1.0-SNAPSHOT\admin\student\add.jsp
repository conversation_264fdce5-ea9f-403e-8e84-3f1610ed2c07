<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <jsp:include page="../../include/header_css.jsp" flush="true"/>
    <title>添加学生信息</title>
</head>
<body class="hold-transition sidebar-mini layout-fixed">
<div class="wrapper">
    <%@include file="../../include/header_nav.jsp"%>
    <%@include file="../../include/left_menu.jsp"%>
    <div class="content-wrapper" style="min-height: 1345.6px;">
        <!-- Content Header (Page header) -->
        <section class="content-header">
            <div class="container-fluid">
                <div class="row mb-2">
                    <div class="col-sm-6">
                        <h1>添加学生信息</h1>
                    </div>
                </div>
            </div><!-- /.container-fluid -->
        </section>

        <!-- Main content -->
        <section class="content">
            <div class="container-fluid">
                <div class="row">
                    <!-- left column -->
                    <div class="col-md-12">
                        <!-- jquery validation -->
                        <div class="card card-primary">
                            <div class="card-header">
                                <h3 class="card-title">请添加<small>学生信息</small></h3>
                            </div>
                            <!-- /.card-header -->
                            <!-- form start -->
                            <form name="form1" id="form1" method="post" action="<%= request.getContextPath() + "/admin/student/StudentServlet?action=add"%>" onsubmit="return verify()">
                                <div class="card-body">
                                    <div class="form-group">
                                        <label >学号</label>
                                        <input type="text" name="txtNum" class="form-control" placeholder="请输入学号">
                                    </div>
                                    <div class="form-group">
                                        <label >姓名</label>
                                        <input type="text" name="txtName" class="form-control"  placeholder="请输入姓名">
                                    </div>
                                    <div class="form-group">
                                        <label >性别</label>
                                        男:<input type="radio" name="rdSex" checked="checked" value="男">
                                        女:<input type="radio" name="rdSex" value="女">
                                    </div>
                                    <div class="form-group">
                                        <label >年龄</label>
                                        <input type="text" name="txtAge" class="form-control"  placeholder="请确认年龄">
                                    </div>
                                    <div class="form-group">
                                        <label>班级</label>
                                        <input type="text" name="txtClass" class="form-control"  placeholder="请输入班级">
                                    </div>
                                    <div class="form-group">
                                        <label >专业</label>
                                        <input type="text" name="txtMajor" class="form-control" placeholder="请输入专业">
                                    </div>
                                    <div class="form-group">
                                        <label>院系</label>
                                        <input type="text" name="txtDepart" class="form-control"  placeholder="请输入院系">
                                    </div>
                                </div>
                                <!-- /.card-body -->
                                <div class="card-footer">
                                    <input type="submit" class="btn btn-primary" name="btnSubmit" value="提交">
                                </div>
                            </form>
                        </div>
                        <!-- /.card -->
                    </div>
                    <!--/.col (left) -->
                    <!-- right column -->
                    <div class="col-md-6">

                    </div>
                    <!--/.col (right) -->
                </div>
                <!-- /.row -->
            </div><!-- /.container-fluid -->
        </section>
        <!-- /.content -->
    </div>
</div>
<%@include file="../../include/foot_js.jsp"%>
<script>
    function verify()
    {
        console.log(`click`);
        //对数据进行检验
        let txtNum=$(`input[name=txtNum]`).val();
        if(txtNum==='')
        {
            alert(`学号不能为空`);
            $(`input[name=txtNum]`).focus();//光标选中
            return false;
        }
        let txtName=$(`input[name=txtName]`).val();
        if(txtName==='')
        {
            alert(`姓名不能为空`);
            $(`input[name=txtName]`).focus();//光标选中
            return false;
        }
        let txtAge=$(`input[name=txtAge]`).val();
        if(txtAge==='')
        {
            alert(`年龄不能为空`);
            $(`input[name=txtAge]`).focus();//光标选中
            return false;
        }
        let txtClass=$(`input[name=txtClass]`).val();
        if(txtClass==='')
        {
            alert(`年级不能为空`);
            $(`input[name=txtClass]`).focus();//光标选中
            return false;
        }
        let txtMajor=$(`input[name=txtMajor]`).val();
        if(txtMajor==='')
        {
            alert(`专业不能为空`);
            $(`input[name=txtMajor]`).focus();//光标选中
            return false;
        }
        let txtDepart=$(`input[name=txtDepart]`).val();
        if(txtDepart==='')
        {
            alert(`部门不能为空`);
            $(`input[name=txtDepart]`).focus();//光标选中
            return false;
        }

    }
</script>

</body>
</html>
