{"version": 3, "file": "lang/summernote-uz-UZ.js", "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAT,EAAY;AACXA,EAAAA,CAAC,CAACC,MAAF,CAASD,CAAC,CAACE,UAAF,CAAaC,IAAtB,EAA4B;AAC1B,aAAS;AACPC,MAAAA,IAAI,EAAE;AACJC,QAAAA,IAAI,EAAE,OADF;AAEJC,QAAAA,MAAM,EAAE,QAFJ;AAGJC,QAAAA,SAAS,EAAE,aAHP;AAIJC,QAAAA,KAAK,EAAE,4BAJH;AAKJC,QAAAA,MAAM,EAAE,kBALJ;AAMJC,QAAAA,IAAI,EAAE,MANF;AAOJC,QAAAA,aAAa,EAAE,WAPX;AAQJC,QAAAA,SAAS,EAAE,eARP;AASJC,QAAAA,WAAW,EAAE,cATT;AAUJC,QAAAA,IAAI,EAAE;AAVF,OADC;AAaPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,MADF;AAELC,QAAAA,MAAM,EAAE,cAFH;AAGLC,QAAAA,UAAU,EAAE,eAHP;AAILC,QAAAA,UAAU,EAAE,sBAJP;AAKLC,QAAAA,aAAa,EAAE,sBALV;AAMLC,QAAAA,SAAS,EAAE,mBANN;AAOLC,QAAAA,UAAU,EAAE,mBAPP;AAQLC,QAAAA,SAAS,EAAE,0BARN;AASLC,QAAAA,YAAY,EAAE,eATT;AAULC,QAAAA,WAAW,EAAE,cAVR;AAWLC,QAAAA,cAAc,EAAE,kBAXX;AAYLC,QAAAA,SAAS,EAAE,YAZN;AAaLC,QAAAA,aAAa,EAAE,uBAbV;AAcLC,QAAAA,SAAS,EAAE,uBAdN;AAeLC,QAAAA,eAAe,EAAE,0BAfZ;AAgBLC,QAAAA,GAAG,EAAE,gBAhBA;AAiBLC,QAAAA,MAAM,EAAE;AAjBH,OAbA;AAgCPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,SAAS,EAAE,gBAFN;AAGLjB,QAAAA,MAAM,EAAE,OAHH;AAILc,QAAAA,GAAG,EAAE,WAJA;AAKLI,QAAAA,SAAS,EAAE;AALN,OAhCA;AAuCPC,MAAAA,IAAI,EAAE;AACJA,QAAAA,IAAI,EAAE,QADF;AAEJnB,QAAAA,MAAM,EAAE,gBAFJ;AAGJoB,QAAAA,MAAM,EAAE,sBAHJ;AAIJC,QAAAA,IAAI,EAAE,cAJF;AAKJC,QAAAA,aAAa,EAAE,kBALX;AAMJR,QAAAA,GAAG,EAAE,eAND;AAOJS,QAAAA,eAAe,EAAE;AAPb,OAvCC;AAgDPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE;AADF,OAhDA;AAmDPC,MAAAA,EAAE,EAAE;AACFzB,QAAAA,MAAM,EAAE;AADN,OAnDG;AAsDP0B,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,CAAC,EAAE,MAFE;AAGLC,QAAAA,UAAU,EAAE,OAHP;AAILC,QAAAA,GAAG,EAAE,KAJA;AAKLC,QAAAA,EAAE,EAAE,YALC;AAMLC,QAAAA,EAAE,EAAE,aANC;AAOLC,QAAAA,EAAE,EAAE,aAPC;AAQLC,QAAAA,EAAE,EAAE,aARC;AASLC,QAAAA,EAAE,EAAE,aATC;AAULC,QAAAA,EAAE,EAAE;AAVC,OAtDA;AAkEPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,SAAS,EAAE,oBADN;AAELC,QAAAA,OAAO,EAAE;AAFJ,OAlEA;AAsEPC,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,OADC;AAEPC,QAAAA,UAAU,EAAE,oBAFL;AAGPC,QAAAA,QAAQ,EAAE;AAHH,OAtEF;AA2EPC,MAAAA,SAAS,EAAE;AACTA,QAAAA,SAAS,EAAE,UADF;AAETC,QAAAA,OAAO,EAAE,4BAFA;AAGTC,QAAAA,MAAM,EAAE,4BAHC;AAITC,QAAAA,IAAI,EAAE,uBAJG;AAKTC,QAAAA,MAAM,EAAE,mBALC;AAMTC,QAAAA,KAAK,EAAE,uBANE;AAOTC,QAAAA,OAAO,EAAE;AAPA,OA3EJ;AAoFPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,MAAM,EAAE,aADH;AAELC,QAAAA,IAAI,EAAE,aAFD;AAGLC,QAAAA,UAAU,EAAE,YAHP;AAILC,QAAAA,UAAU,EAAE,YAJP;AAKLC,QAAAA,WAAW,EAAE,QALR;AAMLC,QAAAA,cAAc,EAAE,iBANX;AAOLC,QAAAA,KAAK,EAAE,aAPF;AAQLC,QAAAA,cAAc,EAAE;AARX,OApFA;AA8FPC,MAAAA,QAAQ,EAAE;AACRC,QAAAA,SAAS,EAAE,6BADH;AAERC,QAAAA,KAAK,EAAE,MAFC;AAGRC,QAAAA,cAAc,EAAE,SAHR;AAIRC,QAAAA,MAAM,EAAE,QAJA;AAKRC,QAAAA,mBAAmB,EAAE,sBALb;AAMRC,QAAAA,aAAa,EAAE,iBANP;AAORC,QAAAA,SAAS,EAAE;AAPH,OA9FH;AAuGPC,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,aADC;AAEPC,QAAAA,IAAI,EAAE;AAFC;AAvGF;AADiB,GAA5B;AA8GD,CA/GD,EA+GGC,MA/GH", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-uz-UZ.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, function() {\nreturn ", "(function($) {\n  $.extend($.summernote.lang, {\n    'uz-UZ': {\n      font: {\n        bold: 'қалин',\n        italic: 'Кур<PERSON>ив',\n        underline: 'Белгиланган',\n        clear: 'Ҳарф турларини олиб ташлаш',\n        height: 'Чизиқ баландлиги',\n        name: 'Ҳарф',\n        strikethrough: 'Ўчирилган',\n        subscript: 'Пастки индекс',\n        superscript: 'Юқори индекс',\n        size: 'ҳарф ҳажми',\n      },\n      image: {\n        image: 'Расм',\n        insert: 'расмни қўйиш',\n        resizeFull: 'Ҳажмни тиклаш',\n        resizeHalf: '50% гача кичрайтириш',\n        resizeQuarter: '25% гача кичрайтириш',\n        floatLeft: 'Чапда жойлаштириш',\n        floatRight: 'Ўнгда жойлаштириш',\n        floatNone: 'Стандарт бўйича жойлашув',\n        shapeRounded: 'Шакли: Юмалоқ',\n        shapeCircle: 'Шакли: Доира',\n        shapeThumbnail: 'Шакли: Миниатюра',\n        shapeNone: 'Шакли: Йўқ',\n        dragImageHere: 'Суратни кўчириб ўтинг',\n        dropImage: 'Суратни кўчириб ўтинг',\n        selectFromFiles: 'Файллардан бирини танлаш',\n        url: 'суратлар URL и',\n        remove: 'Суратни ўчириш',\n      },\n      video: {\n        video: 'Видео',\n        videoLink: 'Видеога ҳавола',\n        insert: 'Видео',\n        url: 'URL видео',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion или Youku)',\n      },\n      link: {\n        link: 'Ҳавола',\n        insert: 'Ҳаволани қўйиш',\n        unlink: 'Ҳаволани олиб ташлаш',\n        edit: 'Таҳрир қилиш',\n        textToDisplay: 'Кўринадиган матн',\n        url: 'URL ўтиш учун',\n        openInNewWindow: 'Янги дарчада очиш',\n      },\n      table: {\n        table: 'Жадвал',\n      },\n      hr: {\n        insert: 'Горизонтал чизиқни қўйиш',\n      },\n      style: {\n        style: 'Услуб',\n        p: 'Яхши',\n        blockquote: 'Жумла',\n        pre: 'Код',\n        h1: 'Сарлавҳа 1',\n        h2: 'Сарлавҳа  2',\n        h3: 'Сарлавҳа  3',\n        h4: 'Сарлавҳа  4',\n        h5: 'Сарлавҳа  5',\n        h6: 'Сарлавҳа  6',\n      },\n      lists: {\n        unordered: 'Белгиланган рўйҳат',\n        ordered: 'Рақамланган рўйҳат',\n      },\n      options: {\n        help: 'Ёрдам',\n        fullscreen: 'Бутун экран бўйича',\n        codeview: 'Бошланғич код',\n      },\n      paragraph: {\n        paragraph: 'Параграф',\n        outdent: 'Орқага қайтишни камайтириш',\n        indent: 'Орқага қайтишни кўпайтириш',\n        left: 'Чап қирғоққа тўғрилаш',\n        center: 'Марказга тўғрилаш',\n        right: 'Ўнг қирғоққа тўғрилаш',\n        justify: 'Эни бўйлаб чўзиш',\n      },\n      color: {\n        recent: 'Охирги ранг',\n        more: 'Яна ранглар',\n        background: 'Фон  ранги',\n        foreground: 'Ҳарф ранги',\n        transparent: 'Шаффоф',\n        setTransparent: 'Шаффофдай қилиш',\n        reset: 'Бекор қилиш',\n        resetToDefault: 'Стандартга оид тиклаш',\n      },\n      shortcut: {\n        shortcuts: 'Клавишларнинг ҳамохҳанглиги',\n        close: 'Ёпиқ',\n        textFormatting: 'Матнни ',\n        action: 'Ҳаркат',\n        paragraphFormatting: 'Параграфни форматлаш',\n        documentStyle: 'Ҳужжатнинг тури',\n        extraKeys: 'Қўшимча имкониятлар',\n      },\n      history: {\n        undo: 'Бекор қилиш',\n        redo: 'Қайтариш',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "url", "remove", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}