<template>
  <div class="students-view">
    <div class="page-header">
      <h1>学生管理</h1>
      <p>管理学生信息、学籍状态和学业进度</p>
    </div>

    <div class="content-card">
      <div class="card-header">
        <h2>学生列表</h2>
        <div class="header-actions">
          <button class="btn btn-primary" @click="handleAddStudent">
            <span class="btn-icon">➕</span>
            添加学生
          </button>
          <button class="btn btn-secondary" @click="handleImportStudents">
            <span class="btn-icon">📥</span>
            批量导入
          </button>
        </div>
      </div>

      <div class="search-bar">
        <input 
          type="text" 
          placeholder="搜索学生姓名、学号..." 
          class="search-input"
          v-model="searchKeyword"
        >
        <select v-model="filterMajor" class="filter-select">
          <option value="">全部专业</option>
          <option value="计算机科学与技术">计算机科学与技术</option>
          <option value="软件工程">软件工程</option>
          <option value="数据科学与大数据技术">数据科学与大数据技术</option>
          <option value="网络工程">网络工程</option>
        </select>
        <select v-model="filterStatus" class="filter-select">
          <option value="">全部状态</option>
          <option value="ACTIVE">在读</option>
          <option value="GRADUATED">已毕业</option>
          <option value="SUSPENDED">休学</option>
          <option value="DROPPED">退学</option>
        </select>
        <button class="search-btn" @click="handleSearch">
          <span class="btn-icon">🔍</span>
        </button>
      </div>

      <div class="students-table">
        <table>
          <thead>
            <tr>
              <th>学号</th>
              <th>姓名</th>
              <th>专业</th>
              <th>班级</th>
              <th>年级</th>
              <th>总学分</th>
              <th>平均绩点</th>
              <th>状态</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="student in filteredStudents" :key="student.id">
              <td><code>{{ student.studentId }}</code></td>
              <td>{{ student.name }}</td>
              <td>{{ student.major }}</td>
              <td>{{ student.className }}</td>
              <td>{{ student.grade }}级</td>
              <td>
                <span class="credit-info">
                  {{ student.totalCredits }}/160
                  <span class="credit-progress" :class="getCreditStatus(student.totalCredits)">
                    {{ getCreditProgress(student.totalCredits) }}
                  </span>
                </span>
              </td>
              <td>
                <span class="gpa-info" :class="getGpaStatus(student.gpa)">
                  {{ student.gpa.toFixed(2) }}
                  <span class="gpa-level">{{ getGpaLevel(student.gpa) }}</span>
                </span>
              </td>
              <td>
                <span class="status-badge" :class="student.status.toLowerCase()">
                  {{ getStatusName(student.status) }}
                </span>
              </td>
              <td>
                <div class="action-buttons">
                  <button class="btn-small btn-edit" @click="handleEditStudent(student)">编辑</button>
                  <button class="btn-small btn-view" @click="handleViewStudent(student)">详情</button>
                  <button class="btn-small btn-grades" @click="handleViewGrades(student)">成绩</button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="pagination">
        <span>共 {{ totalStudents }} 名学生</span>
        <div class="pagination-controls">
          <button class="btn-small" @click="prevPage" :disabled="currentPage === 1">上一页</button>
          <span>第 {{ currentPage }} / {{ totalPages }} 页</span>
          <button class="btn-small" @click="nextPage" :disabled="currentPage === totalPages">下一页</button>
        </div>
      </div>
    </div>

    <!-- 学生详情模态框 -->
    <div v-if="showDetailModal" class="modal-overlay" @click="closeDetailModal">
      <div class="modal-content student-detail-modal" @click.stop>
        <div class="modal-header">
          <h3>学生详细信息</h3>
          <button class="close-btn" @click="closeDetailModal">✕</button>
        </div>
        <div class="modal-body">
          <div class="student-info-grid">
            <div class="info-section">
              <h4>基本信息</h4>
              <div class="info-item">
                <label>学号：</label>
                <span>{{ selectedStudent?.studentId }}</span>
              </div>
              <div class="info-item">
                <label>姓名：</label>
                <span>{{ selectedStudent?.name }}</span>
              </div>
              <div class="info-item">
                <label>性别：</label>
                <span>{{ selectedStudent?.gender === 'MALE' ? '男' : '女' }}</span>
              </div>
              <div class="info-item">
                <label>出生日期：</label>
                <span>{{ selectedStudent?.birthDate }}</span>
              </div>
              <div class="info-item">
                <label>联系电话：</label>
                <span>{{ selectedStudent?.phone }}</span>
              </div>
              <div class="info-item">
                <label>邮箱：</label>
                <span>{{ selectedStudent?.email }}</span>
              </div>
            </div>

            <div class="info-section">
              <h4>学业信息</h4>
              <div class="info-item">
                <label>专业：</label>
                <span>{{ selectedStudent?.major }}</span>
              </div>
              <div class="info-item">
                <label>班级：</label>
                <span>{{ selectedStudent?.className }}</span>
              </div>
              <div class="info-item">
                <label>入学时间：</label>
                <span>{{ selectedStudent?.admissionDate }}</span>
              </div>
              <div class="info-item">
                <label>学籍状态：</label>
                <span class="status-badge" :class="selectedStudent?.status.toLowerCase()">
                  {{ getStatusName(selectedStudent?.status) }}
                </span>
              </div>
            </div>

            <div class="info-section academic-progress">
              <h4>学业进度</h4>
              <div class="progress-item">
                <label>总学分：</label>
                <div class="progress-bar">
                  <div class="progress-fill" :style="{ width: (selectedStudent?.totalCredits / 160 * 100) + '%' }"></div>
                  <span class="progress-text">{{ selectedStudent?.totalCredits }}/160</span>
                </div>
              </div>
              <div class="progress-item">
                <label>必修学分：</label>
                <div class="progress-bar">
                  <div class="progress-fill" :style="{ width: (selectedStudent?.requiredCredits / 120 * 100) + '%' }"></div>
                  <span class="progress-text">{{ selectedStudent?.requiredCredits }}/120</span>
                </div>
              </div>
              <div class="progress-item">
                <label>选修学分：</label>
                <div class="progress-bar">
                  <div class="progress-fill" :style="{ width: (selectedStudent?.electiveCredits / 40 * 100) + '%' }"></div>
                  <span class="progress-text">{{ selectedStudent?.electiveCredits }}/40</span>
                </div>
              </div>
              <div class="progress-item">
                <label>平均绩点：</label>
                <span class="gpa-display" :class="getGpaStatus(selectedStudent?.gpa)">
                  {{ selectedStudent?.gpa.toFixed(2) }} ({{ getGpaLevel(selectedStudent?.gpa) }})
                </span>
              </div>
            </div>

            <div class="info-section graduation-status">
              <h4>毕业要求检查</h4>
              <div class="requirement-check">
                <div class="check-item" :class="selectedStudent?.totalCredits >= 160 ? 'passed' : 'failed'">
                  <span class="check-icon">{{ selectedStudent?.totalCredits >= 160 ? '✅' : '❌' }}</span>
                  <span>总学分要求 (≥160学分)</span>
                </div>
                <div class="check-item" :class="selectedStudent?.gpa >= 2.0 ? 'passed' : 'failed'">
                  <span class="check-icon">{{ selectedStudent?.gpa >= 2.0 ? '✅' : '❌' }}</span>
                  <span>平均绩点要求 (≥2.0)</span>
                </div>
                <div class="check-item" :class="selectedStudent?.requiredCredits >= 120 ? 'passed' : 'failed'">
                  <span class="check-icon">{{ selectedStudent?.requiredCredits >= 120 ? '✅' : '❌' }}</span>
                  <span>必修课学分 (≥120学分)</span>
                </div>
                <div class="check-item" :class="selectedStudent?.electiveCredits >= 40 ? 'passed' : 'failed'">
                  <span class="check-icon">{{ selectedStudent?.electiveCredits >= 40 ? '✅' : '❌' }}</span>
                  <span>选修课学分 (≥40学分)</span>
                </div>
                <div class="check-item" :class="selectedStudent?.failedCourses.length === 0 ? 'passed' : 'warning'">
                  <span class="check-icon">{{ selectedStudent?.failedCourses.length === 0 ? '✅' : '⚠️' }}</span>
                  <span>挂科情况 ({{ selectedStudent?.failedCourses.length }}门挂科)</span>
                </div>
              </div>
              
              <div v-if="selectedStudent?.failedCourses.length > 0" class="failed-courses">
                <h5>挂科课程：</h5>
                <div class="failed-course-list">
                  <span 
                    v-for="course in selectedStudent?.failedCourses" 
                    :key="course"
                    class="failed-course-tag"
                  >
                    {{ course }}
                  </span>
                </div>
              </div>

              <div class="graduation-prediction">
                <h5>毕业预测：</h5>
                <div class="prediction-result" :class="getGraduationStatus(selectedStudent)">
                  {{ getGraduationPrediction(selectedStudent) }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { getAllStudents } from '../data/studentsData.js'

// 响应式数据
const searchKeyword = ref('')
const filterMajor = ref('')
const filterStatus = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const showDetailModal = ref(false)
const selectedStudent = ref(null)

// 学生数据 - 从数据文件加载5000个学生
const students = ref([])

// 计算属性
const filteredStudents = computed(() => {
  return students.value.filter(student => {
    if (searchKeyword.value && !student.name.includes(searchKeyword.value) && !student.studentId.includes(searchKeyword.value)) {
      return false
    }
    if (filterMajor.value && student.major !== filterMajor.value) {
      return false
    }
    if (filterStatus.value && student.status !== filterStatus.value) {
      return false
    }
    return true
  })
})

const totalStudents = computed(() => filteredStudents.value.length)
const totalPages = computed(() => Math.ceil(totalStudents.value / pageSize.value))

// 方法
const getCreditStatus = (credits: number) => {
  if (credits >= 160) return 'excellent'
  if (credits >= 120) return 'good'
  if (credits >= 80) return 'normal'
  return 'warning'
}

const getCreditProgress = (credits: number) => {
  const percentage = (credits / 160 * 100).toFixed(0)
  return `${percentage}%`
}

const getGpaStatus = (gpa: number) => {
  if (gpa >= 3.5) return 'excellent'
  if (gpa >= 3.0) return 'good'
  if (gpa >= 2.0) return 'normal'
  return 'warning'
}

const getGpaLevel = (gpa: number) => {
  if (gpa >= 3.5) return '优秀'
  if (gpa >= 3.0) return '良好'
  if (gpa >= 2.0) return '合格'
  return '警告'
}

const getStatusName = (status: string) => {
  const statusMap = {
    ACTIVE: '在读',
    GRADUATED: '已毕业',
    SUSPENDED: '休学',
    DROPPED: '退学'
  }
  return statusMap[status] || status
}

const getGraduationStatus = (student: any) => {
  if (!student) return 'unknown'
  
  const meetsCredits = student.totalCredits >= 160
  const meetsGpa = student.gpa >= 2.0
  const meetsRequired = student.requiredCredits >= 120
  const meetsElective = student.electiveCredits >= 40
  const noFailed = student.failedCourses.length === 0
  
  if (meetsCredits && meetsGpa && meetsRequired && meetsElective && noFailed) {
    return 'can-graduate'
  } else if (meetsGpa && noFailed) {
    return 'on-track'
  } else {
    return 'at-risk'
  }
}

const getGraduationPrediction = (student: any) => {
  if (!student) return '数据不足'
  
  const status = getGraduationStatus(student)
  
  switch (status) {
    case 'can-graduate':
      return '✅ 满足毕业要求，可以正常毕业'
    case 'on-track':
      return '📈 学业进展良好，预计可按时毕业'
    case 'at-risk':
      return '⚠️ 存在学业风险，需要重点关注'
    default:
      return '❓ 数据不足，无法预测'
  }
}

const handleAddStudent = () => {
  alert('添加学生功能\n\n将打开学生添加对话框')
}

const handleImportStudents = () => {
  alert('批量导入功能\n\n支持Excel文件导入学生数据')
}

const handleSearch = () => {
  currentPage.value = 1
}

const handleEditStudent = (student: any) => {
  alert(`编辑学生\n\n学生：${student.name}\n学号：${student.studentId}`)
}

const handleViewStudent = (student: any) => {
  selectedStudent.value = student
  showDetailModal.value = true
}

const handleViewGrades = (student: any) => {
  alert(`查看成绩\n\n学生：${student.name}\n将显示该学生的详细成绩信息和绩点计算`)
}

const closeDetailModal = () => {
  showDetailModal.value = false
  selectedStudent.value = null
}

const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
  }
}

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
  }
}

// 页面加载时初始化数据
onMounted(() => {
  console.log('加载学生数据...')
  students.value = getAllStudents()
  console.log(`成功加载 ${students.value.length} 名学生数据`)
})
</script>

<style scoped>
.students-view {
  padding: 2rem;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 2rem;
}

.page-header h1 {
  font-size: 2rem;
  color: #333;
  margin: 0 0 0.5rem 0;
}

.page-header p {
  color: #666;
  margin: 0;
}

.content-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e1e5e9;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.btn-primary {
  background: #1976d2;
  color: white;
}

.btn-secondary {
  background: #f5f7fa;
  color: #666;
  border: 1px solid #e1e5e9;
}

.search-bar {
  padding: 1.5rem;
  border-bottom: 1px solid #e1e5e9;
  display: flex;
  gap: 1rem;
}

.search-input {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  font-size: 1rem;
}

.filter-select {
  padding: 0.75rem;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  min-width: 150px;
}

.search-btn {
  padding: 0.75rem 1.5rem;
  background: #1976d2;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
}

.students-table {
  overflow-x: auto;
}

.students-table table {
  width: 100%;
  border-collapse: collapse;
}

.students-table th,
.students-table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #e1e5e9;
}

.students-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #333;
}

.students-table code {
  background: #f5f7fa;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
}

.credit-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.credit-progress {
  font-size: 0.8rem;
  padding: 0.125rem 0.5rem;
  border-radius: 12px;
  font-weight: 500;
}

.credit-progress.excellent {
  background: #e8f5e8;
  color: #388e3c;
}

.credit-progress.good {
  background: #e3f2fd;
  color: #1976d2;
}

.credit-progress.normal {
  background: #fff3e0;
  color: #f57c00;
}

.credit-progress.warning {
  background: #ffebee;
  color: #d32f2f;
}

.gpa-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  font-weight: 600;
}

.gpa-info.excellent {
  color: #388e3c;
}

.gpa-info.good {
  color: #1976d2;
}

.gpa-info.normal {
  color: #f57c00;
}

.gpa-info.warning {
  color: #d32f2f;
}

.gpa-level {
  font-size: 0.8rem;
  font-weight: 400;
  opacity: 0.8;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.status-badge.active {
  background: #e8f5e8;
  color: #388e3c;
}

.status-badge.graduated {
  background: #e3f2fd;
  color: #1976d2;
}

.status-badge.suspended {
  background: #fff3e0;
  color: #f57c00;
}

.status-badge.dropped {
  background: #ffebee;
  color: #d32f2f;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
}

.btn-small {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
}

.btn-edit {
  background: #e3f2fd;
  color: #1976d2;
}

.btn-view {
  background: #e0f2f1;
  color: #00695c;
}

.btn-grades {
  background: #fff3e0;
  color: #f57c00;
}

.pagination {
  padding: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 1000px;
  max-height: 80vh;
  overflow-y: auto;
}

.student-detail-modal {
  max-width: 1200px;
}

.modal-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e1e5e9;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #666;
}

.modal-body {
  padding: 1.5rem;
}

.student-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.info-section {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
}

.info-section h4 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.1rem;
  border-bottom: 2px solid #1976d2;
  padding-bottom: 0.5rem;
}

.info-item {
  display: flex;
  margin-bottom: 0.75rem;
  align-items: center;
}

.info-item label {
  min-width: 100px;
  font-weight: 500;
  color: #333;
}

.info-item span {
  flex: 1;
  color: #666;
}

.progress-item {
  margin-bottom: 1rem;
}

.progress-item label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #333;
}

.progress-bar {
  position: relative;
  background: #e1e5e9;
  border-radius: 10px;
  height: 20px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #1976d2, #42a5f5);
  transition: width 0.3s ease;
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 0.8rem;
  font-weight: 600;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.gpa-display {
  font-size: 1.2rem;
  font-weight: 600;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  text-align: center;
}

.gpa-display.excellent {
  background: #e8f5e8;
  color: #388e3c;
}

.gpa-display.good {
  background: #e3f2fd;
  color: #1976d2;
}

.gpa-display.normal {
  background: #fff3e0;
  color: #f57c00;
}

.gpa-display.warning {
  background: #ffebee;
  color: #d32f2f;
}

.requirement-check {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.check-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 8px;
  font-weight: 500;
}

.check-item.passed {
  background: #e8f5e8;
  color: #388e3c;
}

.check-item.failed {
  background: #ffebee;
  color: #d32f2f;
}

.check-item.warning {
  background: #fff3e0;
  color: #f57c00;
}

.check-icon {
  font-size: 1.2rem;
}

.failed-courses {
  margin-top: 1rem;
}

.failed-courses h5 {
  margin: 0 0 0.5rem 0;
  color: #d32f2f;
}

.failed-course-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.failed-course-tag {
  background: #ffebee;
  color: #d32f2f;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.graduation-prediction {
  margin-top: 1rem;
}

.graduation-prediction h5 {
  margin: 0 0 0.5rem 0;
  color: #333;
}

.prediction-result {
  padding: 1rem;
  border-radius: 8px;
  font-weight: 500;
  text-align: center;
}

.prediction-result.can-graduate {
  background: #e8f5e8;
  color: #388e3c;
}

.prediction-result.on-track {
  background: #e3f2fd;
  color: #1976d2;
}

.prediction-result.at-risk {
  background: #ffebee;
  color: #d32f2f;
}

.prediction-result.unknown {
  background: #f5f7fa;
  color: #666;
}
</style>
