<%@ page import="com.ntvu.studentmis.entity.User" %>
<%@ page import="com.ntvu.studentmis.db.DBManager" %>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <jsp:include page="header_css.jsp" flush="true"/>
    <title>个人信息</title>
</head>
<body class="hold-transition sidebar-mini layout-fixed">
<div class="wrapper">
    <%@include file="header_nav.jsp"%>
    <%@include file="left_menu.jsp"%>
    <div class="content-wrapper" style="min-height: 1345.6px;">
        <!-- Content Header (Page header) -->
        <section class="content-header">
            <div class="container-fluid">
                <div class="row mb-2">
                    <div class="col-sm-6">
                        <h1>个人信息</h1>
                    </div>
                </div>
            </div><!-- /.container-fluid -->
        </section>
        <%
            String txtloginName=(String)session.getAttribute("curUserName");
            User user=new DBManager().getDetails(txtloginName);
        %>
        <!-- Main content -->
        <section class="content">
            <div class="container-fluid">
                <div class="row">
                    <!-- left column -->
                    <div class="col-md-12">
                        <!-- jquery validation -->
                        <div class="card card-primary">
                            <div class="card-header">
                                <h3 class="card-title">查看<small>个人信息</small></h3>
                            </div>
                            <!-- /.card-header -->
                            <!-- form start -->
                            <form name="form1" id="form1" method="post" action="<%= request.getContextPath() + "/admin/user/UserServlet?action=edit"%>" onsubmit="return verify()">
                                <div class="card-body">
                                    <input type="hidden" name="id" value="<%= user.getUser_id()%>">
                                    <div class="form-group">
                                        <label for="exampleInputPassword1">学工号</label>
                                        <input type="text" name="txtLoginName" class="form-control" id="exampleInputUser"  readonly="readonly" value="<%= user.getUser_num()%>">
                                    </div>
                                    <div class="form-group">
                                        <%--@declare id="exampleinputpassword1"--%><label for="exampleInputPassword1">姓名</label>
                                        <input type="text" name="txtRealName" class="form-control" id="exampleInputRealName"  value="<%= user.getUser_name()%>">
                                    </div>
                                    <div class="form-group">
                                        <label for="exampleInputPassword1">密码</label>
                                        <input type="text" name="txtLoginPassword" class="form-control" id="exampleInputPassword1" placeholder="请输入密码" value="<%= user.getPassword()%>">
                                    </div>
                                    <div class="form-group">
                                        <label for="exampleInputPassword1">手机号</label>
                                        <input type="tel" name="txtTelephone" class="form-control" id="exampleInputTelephone" placeholder="请输入手机号" value="<%= user.getPhone()%>">
                                    </div>
                                    <div class="form-group">
                                        <label>身份</label>
                                        <input name="selectVal" hidden="hidden" value="<%= user.getRole_id()%>">
                                        <label for="selectList"></label><select id="selectList" name="selectList" >
                                        <option name="txtRole" value="">请选择身份</option>
                                        <option name="txtRole" value="0">学生</option>
                                        <option name="txtRole" value="1">教师</option>
                                        <option name="txtRole" value="2">管理员</option>
                                    </select>
                                    </div>
                                </div>
                                <!-- /.card-body -->
                                <div class="card-footer">
                                    <input type="submit" class="btn btn-primary" name="btnSubmit" value="修改">
                                </div>
                            </form>
                        </div>
                        <!-- /.card -->
                    </div>
                    <!--/.col (left) -->
                    <!-- right column -->
                    <div class="col-md-6">

                    </div>
                    <!--/.col (right) -->
                </div>
                <!-- /.row -->
            </div><!-- /.container-fluid -->
        </section>
        <!-- /.content -->
    </div>
    <!-- footer -->
    <%@include file="../include/foot_js.jsp"%>
</div>
<!-- ./wrapper -->
<%@include file="foot_js.jsp"%>
<script type="text/javascript">
    //设置身份选中项
    let selectVal=$(`input[name=selectVal]`).val();
    console.log(selectVal);
    var numbers = $("#selectList").find("option"); //获取select下拉框的所有值
    for (var j = 0; j < numbers.length; j++) {
        if ($(numbers[j]).val() === selectVal) {
            $(numbers[j]).prop("selected", "selected");
        }
    }
    function verify() {
        //对数据进行检验
        let txtRealName=$(`input[name=txtRealName]`).val();
        if(txtRealName==='')
        {
            alert(`姓名不能为空`);
            $(`input[name=txtRealName]`).focus();//光标选中
            return false;
        }
        let txtLoginPassword=$(`input[name=txtLoginPassword]`).val();
        if(txtLoginPassword==='')
        {
            alert(`密码不能为空`);
            $(`input[name=txtLoginPassword]`).focus();//光标选中
            return false;
        }
        let txtTelephone=$(`input[name=txtTelephone]`).val();
        if(txtTelephone==='')
        {
            alert(`手机号不能为空`);
            $(`input[name=txtTelephone]`).focus();//光标选中
            return false;
        }
    }
</script>

</body>
</html>
