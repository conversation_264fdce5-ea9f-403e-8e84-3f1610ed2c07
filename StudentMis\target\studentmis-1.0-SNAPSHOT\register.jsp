<%@ page import="com.ntvu.studentmis.entity.User" %>
<%@ page import="com.ntvu.studentmis.db.DBManager" %>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>注册用户</title>
  <!-- Google Font: Source Sans Pro -->
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
  <!-- Font Awesome -->
  <link rel="stylesheet" href="plugins/fontawesome-free/css/all.min.css">
  <!-- icheck bootstrap -->
  <link rel="stylesheet" href="plugins/icheck-bootstrap/icheck-bootstrap.min.css">
  <!-- Theme style -->
  <link rel="stylesheet" href="dist/css/adminlte.min.css">
</head>
<body class="hold-transition register-page">
<%
  request.setCharacterEncoding("utf-8");
  String user_num = request.getParameter("user_num");
  String phone = request.getParameter("phone");
%>
<div class="register-box">
  <div class="register-logo">
    <a href="#"><b>注册</b>用户</a>
  </div>
  <div class="card">
    <div class="card-body register-card-body">
      <p class="login-box-msg">注册一个新用户</p>

      <form name="form1" id="form1" method="post" action="<%= request.getContextPath() + "/admin/user/UserServlet?action=register"%>" onsubmit="return verify()">
        <div class="input-group mb-3">
          <input type="hidden" name="user_num" value="<%=user_num%>">
          <input type="hidden" name="phone" value="<%=phone%>">
          <input type="text" class="form-control" placeholder="用户名" name="txtLoginName">
          <div class="input-group-append">
            <div class="input-group-text">
              <span class="fas fa-user"></span>
            </div>
          </div>
        </div>
        <div class="input-group mb-3">
          <input type="password" class="form-control" placeholder="密码" name="txtLoginPassword"
                 pattern="(?=.*\d)(?=.*[a-z])(?=.*[A-Z]).{6,}"
                 title="密码必须包含至少一个大写字母、一个小写字母和一个数字，且长度至少6位"
                 required>
          <div class="input-group-append">
            <div class="input-group-text">
              <span class="fas fa-lock"></span>
            </div>
          </div>
        </div>
        <div class="input-group mb-3">
          <input type="password" class="form-control" placeholder="确认密码" name="txtLoginPassword2"
                 pattern="(?=.*\d)(?=.*[a-z])(?=.*[A-Z]).{6,}"
                 title="密码必须包含至少一个大写字母、一个小写字母和一个数字，且长度至少6位"
                 required>
          <div class="input-group-append">
            <div class="input-group-text">
              <span class="fas fa-lock"></span>
            </div>
          </div>

        </div>
        <div class="input-group mb-3">
          <input type="text" class="form-control" placeholder="真实用户名" name="txtRealName">
          <div class="input-group-append">
            <div class="input-group-text">
              <span class="fas fa-user"></span>
            </div>
          </div>
        </div>

        <div class="input-group mb-3">
          <input type="text" class="form-control" placeholder="手机号" name="txtTelephone"  pattern="^1[3-9]\d{9}$"
                 title="请输入11位有效手机号（以13-19开头）"
                 required>
          <div class="input-group-append">
            <div class="input-group-text">
              <span class="fas fa-phone"></span>
            </div>
          </div>
        </div>

        <div class="input-group mb-3">
          <label>身份</label>
          <label for="selectList"></label><select id="selectList" name="selectList" >
          <option name="txtRole" value="">请选择身份</option>
          <option name="txtRole" value="0">学生</option>
          <option name="txtRole" value="1">教师</option>
          <option name="txtRole" value="2">管理员</option>
        </select>
        </div>

        <div class="row">
          <div class="col-3"></div>
          <!-- /.col -->
          <div class="col-6">
            <input type="submit" class="btn btn-primary btn-block" value="注册">
          </div>
          <div class="col-3"></div>
          <!-- /.col -->
        </div>
      </form>
      <a href="login.jsp" class="text-center">我已经有账号了</a>
    </div>
    <!-- /.form-box -->
  </div><!-- /.card -->
</div>
<%@include file="include/foot_js.jsp"%>
<script type="text/javascript">
  // 假设从后端返回的user数据
  function verify() {
    //对数据进行检验
    let user_num=$(`input[name=user_num]`).val();
    if(user_num!=='')
    {
      alert(`用户名已存在，请选择其他用户名`);
      return false;
    }
    let phone=$(`input[name=phone]`).val();
    if(phone!=='')
    {
      alert(`手机号已被注册！`);
      return false;
    }
    //对数据进行检验
    let txtLoginName=$(`input[name=txtLoginName]`).val();
    if(txtLoginName==='')
    {
      alert(`登录名称不能为空`);
      $(`input[name=txtLoginName]`).focus();//光标选中
      return false;
    }
    let txtLoginPassword=$(`input[name=txtLoginPassword]`).val();
    if(txtLoginPassword==='')
    {
      alert(`密码不能为空`);
      $(`input[name=txtLoginPassword]`).focus();//光标选中
      return false;
    }
    let txtLoginPassword2=$(`input[name=txtLoginPassword2]`).val();
    if(txtLoginPassword2==='')
    {
      alert(`确认密码不能为空`);
      $(`input[name=txtLoginPassword2]`).focus();//光标选中
      return false;
    }
    if(txtLoginPassword!==txtLoginPassword2)
    {
      alert(`两次密码必须相同`);
      $(`input[name=txtLoginPassword]`).focus();//光标选中
      $(`input[name=txtLoginPassword2]`).focus();//光标选中
      return false;
    }

    let txtRealName=$(`input[name=txtRealName]`).val();
    if(txtRealName==='')
    {
      alert(`姓名不能为空`);
      $(`input[name=txtRealName]`).focus();//光标选中
      return false;
    }
    let txtTelephone=$(`input[name=txtTelephone]`).val();
    if(txtTelephone==='')
    {
      alert(`手机号不能为空`);
      $(`input[name=txtTelephone]`).focus();//光标选中
      return false;
    }
    let selectValue = $('select').val();
    if(selectValue==='')
    {
      alert(`请选择你的身份`);
      return false;
    }
  }
</script>
</body>
</html>
