<template>
  <div class="courses-view">
    <div class="page-header">
      <h1>课程管理</h1>
      <p>管理课程信息、学分设置和绩点计算</p>
    </div>

    <div class="content-card">
      <div class="card-header">
        <h2>课程列表</h2>
        <div class="header-actions">
          <button
            v-if="hasManagePermission"
            class="btn btn-primary"
            @click="handleAddCourse"
          >
            <span class="btn-icon">➕</span>
            添加课程
          </button>
          <button
            v-if="hasManagePermission"
            class="btn btn-secondary"
            @click="handleImportCourses"
          >
            <span class="btn-icon">📥</span>
            批量导入
          </button>
          <span v-if="!hasManagePermission" class="no-permission-text">仅查看模式</span>
        </div>
      </div>

      <div class="search-bar">
        <input 
          type="text" 
          placeholder="搜索课程名称、课程代码..." 
          class="search-input"
          v-model="searchKeyword"
        >
        <select v-model="filterCollege" class="filter-select">
          <option value="">全部学院</option>
          <option value="08">计算机工程学院</option>
          <option value="02">机械与海洋工程学院</option>
          <option value="11">商学院</option>
          <option value="06">电子工程学院</option>
        </select>
        <button class="search-btn" @click="handleSearch">
          <span class="btn-icon">🔍</span>
        </button>
      </div>

      <div class="courses-table">
        <table>
          <thead>
            <tr>
              <th>课程代码</th>
              <th>课程名称</th>
              <th>学分</th>
              <th>课程类型</th>
              <th>开课学院</th>
              <th>学时</th>
              <th>考核方式</th>
              <th>状态</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="course in filteredCourses" :key="course.id">
              <td><code>{{ course.code }}</code></td>
              <td>{{ course.name }}</td>
              <td>
                <span class="credit-badge">{{ course.credits }}学分</span>
              </td>
              <td>
                <span class="type-badge" :class="course.type">
                  {{ getCourseTypeName(course.type) }}
                </span>
              </td>
              <td>{{ course.college }}</td>
              <td>{{ course.hours }}学时</td>
              <td>
                <span class="exam-badge" :class="course.examType">
                  {{ getExamTypeName(course.examType) }}
                </span>
              </td>
              <td>
                <span class="status-badge" :class="course.status">
                  {{ course.status === 'active' ? '开课' : '停课' }}
                </span>
              </td>
              <td>
                <div class="action-buttons">
                  <button
                    v-if="hasManagePermission"
                    class="btn-small btn-edit"
                    @click="handleEditCourse(course)"
                  >
                    编辑
                  </button>
                  <button
                    class="btn-small btn-info"
                    @click="handleViewGrades(course)"
                  >
                    成绩
                  </button>
                  <button
                    v-if="hasManagePermission"
                    class="btn-small btn-delete"
                    @click="handleDeleteCourse(course)"
                  >
                    删除
                  </button>
                  <span v-if="!hasManagePermission" class="no-permission">无权限</span>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="pagination">
        <span>共 {{ totalCourses }} 门课程</span>
        <div class="pagination-controls">
          <button class="btn-small" @click="prevPage" :disabled="currentPage === 1">上一页</button>
          <span>第 {{ currentPage }} / {{ totalPages }} 页</span>
          <button class="btn-small" @click="nextPage" :disabled="currentPage === totalPages">下一页</button>
        </div>
      </div>
    </div>

    <!-- 学分绩点说明 -->
    <div class="content-card">
      <div class="card-header">
        <h2>学分绩点计算说明</h2>
        <span class="subtitle">按照清华大学标准</span>
      </div>
      <div class="gpa-explanation">
        <div class="gpa-section">
          <h3>绩点计算公式</h3>
          <div class="formula">
            <p><strong>GPA = Σ(课程学分 × 课程绩点) / Σ课程学分</strong></p>
            <p>其中，课程绩点根据百分制成绩计算：</p>
          </div>
        </div>

        <div class="gpa-section">
          <h3>成绩与绩点对照表</h3>
          <div class="grade-table">
            <table>
              <thead>
                <tr>
                  <th>百分制成绩</th>
                  <th>等级</th>
                  <th>绩点</th>
                  <th>说明</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>95-100</td>
                  <td>A+</td>
                  <td>4.0</td>
                  <td>优秀</td>
                </tr>
                <tr>
                  <td>90-94</td>
                  <td>A</td>
                  <td>4.0</td>
                  <td>优秀</td>
                </tr>
                <tr>
                  <td>85-89</td>
                  <td>A-</td>
                  <td>3.7</td>
                  <td>良好</td>
                </tr>
                <tr>
                  <td>80-84</td>
                  <td>B+</td>
                  <td>3.3</td>
                  <td>良好</td>
                </tr>
                <tr>
                  <td>75-79</td>
                  <td>B</td>
                  <td>3.0</td>
                  <td>中等</td>
                </tr>
                <tr>
                  <td>70-74</td>
                  <td>B-</td>
                  <td>2.7</td>
                  <td>中等</td>
                </tr>
                <tr>
                  <td>65-69</td>
                  <td>C+</td>
                  <td>2.3</td>
                  <td>及格</td>
                </tr>
                <tr>
                  <td>60-64</td>
                  <td>C</td>
                  <td>2.0</td>
                  <td>及格</td>
                </tr>
                <tr>
                  <td>0-59</td>
                  <td>F</td>
                  <td>0.0</td>
                  <td>不及格</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <div class="gpa-section">
          <h3>毕业要求</h3>
          <div class="graduation-requirements">
            <div class="requirement-item">
              <span class="requirement-label">总学分要求：</span>
              <span class="requirement-value">≥ 160学分</span>
            </div>
            <div class="requirement-item">
              <span class="requirement-label">平均绩点要求：</span>
              <span class="requirement-value">≥ 2.0</span>
            </div>
            <div class="requirement-item">
              <span class="requirement-label">必修课学分：</span>
              <span class="requirement-value">≥ 120学分</span>
            </div>
            <div class="requirement-item">
              <span class="requirement-label">选修课学分：</span>
              <span class="requirement-value">≥ 40学分</span>
            </div>
            <div class="requirement-item">
              <span class="requirement-label">实践环节：</span>
              <span class="requirement-value">≥ 20学分</span>
            </div>
          </div>
        </div>

        <div class="gpa-section">
          <h3>学位等级划分</h3>
          <div class="degree-levels">
            <div class="level-item excellent">
              <span class="level-name">优秀学士学位</span>
              <span class="level-requirement">GPA ≥ 3.5 且无不及格记录</span>
            </div>
            <div class="level-item good">
              <span class="level-name">良好学士学位</span>
              <span class="level-requirement">3.0 ≤ GPA < 3.5</span>
            </div>
            <div class="level-item normal">
              <span class="level-name">普通学士学位</span>
              <span class="level-requirement">2.0 ≤ GPA < 3.0</span>
            </div>
            <div class="level-item warning">
              <span class="level-name">学业警告</span>
              <span class="level-requirement">GPA < 2.0</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 成绩查看对话框 -->
    <div v-if="showGradesDialog" class="modal-overlay" @click="closeGradesDialog">
      <div class="modal-content grades-modal" @click.stop>
        <div class="modal-header">
          <h3>{{ selectedCourse?.name }} - 成绩查看</h3>
          <button class="close-btn" @click="closeGradesDialog">×</button>
        </div>
        <div class="modal-body">
          <div class="grades-stats">
            <div class="stat-card">
              <div class="stat-label">选课人数</div>
              <div class="stat-value">{{ courseGrades.length }}</div>
            </div>
            <div class="stat-card">
              <div class="stat-label">平均分</div>
              <div class="stat-value">{{ averageScore.toFixed(1) }}</div>
            </div>
            <div class="stat-card">
              <div class="stat-label">及格率</div>
              <div class="stat-value">{{ passRate.toFixed(1) }}%</div>
            </div>
            <div class="stat-card">
              <div class="stat-label">优秀率</div>
              <div class="stat-value">{{ excellentRate.toFixed(1) }}%</div>
            </div>
          </div>

          <div class="grades-table">
            <table>
              <thead>
                <tr>
                  <th>学号</th>
                  <th>姓名</th>
                  <th>班级</th>
                  <th>平时成绩</th>
                  <th>期中成绩</th>
                  <th>期末成绩</th>
                  <th>总评成绩</th>
                  <th>等级</th>
                  <th>绩点</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="grade in courseGrades" :key="grade.studentId">
                  <td>{{ grade.studentId }}</td>
                  <td>{{ grade.studentName }}</td>
                  <td>{{ grade.className }}</td>
                  <td>{{ grade.regularScore }}</td>
                  <td>{{ grade.midtermScore }}</td>
                  <td>{{ grade.finalScore }}</td>
                  <td>
                    <span class="score-badge" :class="getScoreLevel(grade.totalScore)">
                      {{ grade.totalScore }}
                    </span>
                  </td>
                  <td>
                    <span class="grade-level" :class="getScoreLevel(grade.totalScore)">
                      {{ getGradeLevel(grade.totalScore) }}
                    </span>
                  </td>
                  <td>
                    <span class="gpa-badge" :class="getScoreLevel(grade.totalScore)">
                      {{ getGPA(grade.totalScore) }}
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" @click="closeGradesDialog">关闭</button>
          <button
            v-if="hasManagePermission"
            type="button"
            class="btn btn-primary"
            @click="exportCourseGrades"
          >
            导出成绩
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { permissionManager } from '@/utils/permissions'

// 响应式数据
const searchKeyword = ref('')
const filterCollege = ref('')
const currentPage = ref(1)
const pageSize = ref(10)

// 成绩查看对话框相关
const showGradesDialog = ref(false)
const selectedCourse = ref(null)
const courseGrades = ref([])

// 模拟课程数据
const courses = ref([
  {
    id: 1,
    code: 'CS101',
    name: '计算机科学导论',
    credits: 3,
    type: 'required',
    college: '计算机工程学院',
    hours: 48,
    examType: 'exam',
    status: 'active'
  },
  {
    id: 2,
    code: 'CS201',
    name: '数据结构与算法',
    credits: 4,
    type: 'required',
    college: '计算机工程学院',
    hours: 64,
    examType: 'exam',
    status: 'active'
  },
  {
    id: 3,
    code: 'CS301',
    name: '数据库系统原理',
    credits: 3,
    type: 'required',
    college: '计算机工程学院',
    hours: 48,
    examType: 'exam',
    status: 'active'
  },
  {
    id: 4,
    code: 'CS401',
    name: '软件工程',
    credits: 3,
    type: 'required',
    college: '计算机工程学院',
    hours: 48,
    examType: 'project',
    status: 'active'
  },
  {
    id: 5,
    code: 'CS501',
    name: '人工智能基础',
    credits: 2,
    type: 'elective',
    college: '计算机工程学院',
    hours: 32,
    examType: 'exam',
    status: 'active'
  },
  {
    id: 6,
    code: 'MATH101',
    name: '高等数学A',
    credits: 5,
    type: 'required',
    college: '理学院',
    hours: 80,
    examType: 'exam',
    status: 'active'
  },
  {
    id: 7,
    code: 'ENG101',
    name: '大学英语',
    credits: 2,
    type: 'required',
    college: '外国语学院',
    hours: 32,
    examType: 'exam',
    status: 'active'
  },
  {
    id: 8,
    code: 'PE101',
    name: '体育',
    credits: 1,
    type: 'required',
    college: '体育学院',
    hours: 32,
    examType: 'assessment',
    status: 'active'
  }
])

// 计算属性
const filteredCourses = computed(() => {
  return courses.value.filter(course => {
    if (searchKeyword.value && !course.name.includes(searchKeyword.value) && !course.code.includes(searchKeyword.value)) {
      return false
    }
    if (filterCollege.value && !course.college.includes(filterCollege.value)) {
      return false
    }
    return true
  })
})

const totalCourses = computed(() => filteredCourses.value.length)
const totalPages = computed(() => Math.ceil(totalCourses.value / pageSize.value))

// 获取当前用户信息
const currentUser = computed(() => {
  const userStr = localStorage.getItem('currentUser')
  if (userStr) {
    const user = JSON.parse(userStr)
    // 确保权限管理器有当前用户信息
    if (!permissionManager.getCurrentUser()) {
      permissionManager.setCurrentUser(user)
    }
    return user
  }
  return null
})

// 权限检查
const hasManagePermission = computed(() => {
  return permissionManager.hasPermission('course.manage')
})

const hasViewPermission = computed(() => {
  return permissionManager.hasPermission('course.view')
})

// 成绩统计计算属性
const averageScore = computed(() => {
  if (courseGrades.value.length === 0) return 0
  const total = courseGrades.value.reduce((sum, grade) => sum + grade.totalScore, 0)
  return total / courseGrades.value.length
})

const passRate = computed(() => {
  if (courseGrades.value.length === 0) return 0
  const passCount = courseGrades.value.filter(grade => grade.totalScore >= 60).length
  return (passCount / courseGrades.value.length) * 100
})

const excellentRate = computed(() => {
  if (courseGrades.value.length === 0) return 0
  const excellentCount = courseGrades.value.filter(grade => grade.totalScore >= 90).length
  return (excellentCount / courseGrades.value.length) * 100
})

// 方法
const getCourseTypeName = (type: string) => {
  const typeMap = {
    required: '必修',
    elective: '选修',
    practice: '实践'
  }
  return typeMap[type] || type
}

const getExamTypeName = (examType: string) => {
  const examMap = {
    exam: '考试',
    assessment: '考查',
    project: '课程设计'
  }
  return examMap[examType] || examType
}

// 成绩相关工具方法
const getScoreLevel = (score: number) => {
  if (score >= 90) return 'excellent'
  if (score >= 80) return 'good'
  if (score >= 70) return 'average'
  if (score >= 60) return 'pass'
  return 'fail'
}

const getGradeLevel = (score: number) => {
  if (score >= 95) return 'A+'
  if (score >= 90) return 'A'
  if (score >= 85) return 'A-'
  if (score >= 80) return 'B+'
  if (score >= 75) return 'B'
  if (score >= 70) return 'B-'
  if (score >= 65) return 'C+'
  if (score >= 60) return 'C'
  return 'F'
}

const getGPA = (score: number) => {
  if (score >= 95) return '4.0'
  if (score >= 90) return '4.0'
  if (score >= 85) return '3.7'
  if (score >= 80) return '3.3'
  if (score >= 75) return '3.0'
  if (score >= 70) return '2.7'
  if (score >= 65) return '2.3'
  if (score >= 60) return '2.0'
  return '0.0'
}

const handleAddCourse = () => {
  alert('添加课程功能\n\n将打开课程添加对话框')
}

const handleImportCourses = () => {
  alert('批量导入功能\n\n支持Excel文件导入课程数据')
}

const handleSearch = () => {
  console.log('搜索关键词:', searchKeyword.value)
}

const handleEditCourse = (course: any) => {
  alert(`编辑课程\n\n课程：${course.name}\n代码：${course.code}`)
}

const handleViewGrades = (course: any) => {
  selectedCourse.value = course
  generateCourseGrades(course)
  showGradesDialog.value = true
}

// 生成课程成绩数据
const generateCourseGrades = (course: any) => {
  // 模拟学生数据（从localStorage获取或使用默认数据）
  const studentsData = JSON.parse(localStorage.getItem('studentsData') || '[]')

  // 如果没有学生数据，生成一些示例数据
  if (studentsData.length === 0) {
    const sampleStudents = [
      { studentId: '2024010001', name: '马豪', className: '计算机4241班' },
      { studentId: '2024010002', name: '董强', className: '计算机4241班' },
      { studentId: '2024010003', name: '李明', className: '计算机4241班' },
      { studentId: '2024010004', name: '王芳', className: '计算机4242班' },
      { studentId: '2024010005', name: '张伟', className: '计算机4242班' },
      { studentId: '2024020001', name: '刘洋', className: '软件4241班' },
      { studentId: '2024020002', name: '陈静', className: '软件4241班' },
      { studentId: '2024020003', name: '赵磊', className: '软件4242班' }
    ]

    courseGrades.value = sampleStudents.map(student => {
      // 生成随机成绩
      const regularScore = Math.floor(Math.random() * 30) + 70 // 70-100
      const midtermScore = Math.floor(Math.random() * 30) + 70 // 70-100
      const finalScore = Math.floor(Math.random() * 30) + 70 // 70-100
      const totalScore = Math.round(regularScore * 0.3 + midtermScore * 0.3 + finalScore * 0.4)

      return {
        studentId: student.studentId,
        studentName: student.name,
        className: student.className,
        courseCode: course.code,
        courseName: course.name,
        regularScore,
        midtermScore,
        finalScore,
        totalScore,
        credits: course.credits
      }
    })
  } else {
    // 使用真实学生数据生成成绩
    const selectedStudents = studentsData.slice(0, Math.min(50, studentsData.length))
    courseGrades.value = selectedStudents.map(student => {
      const regularScore = Math.floor(Math.random() * 30) + 70
      const midtermScore = Math.floor(Math.random() * 30) + 70
      const finalScore = Math.floor(Math.random() * 30) + 70
      const totalScore = Math.round(regularScore * 0.3 + midtermScore * 0.3 + finalScore * 0.4)

      return {
        studentId: student.studentId,
        studentName: student.name,
        className: student.className,
        courseCode: course.code,
        courseName: course.name,
        regularScore,
        midtermScore,
        finalScore,
        totalScore,
        credits: course.credits
      }
    })
  }
}

const closeGradesDialog = () => {
  showGradesDialog.value = false
  selectedCourse.value = null
  courseGrades.value = []
}

const exportCourseGrades = () => {
  if (!hasManagePermission.value) {
    alert('权限不足！您没有数据导出权限。')
    return
  }

  // 生成CSV内容
  const headers = ['学号', '姓名', '班级', '平时成绩', '期中成绩', '期末成绩', '总评成绩', '等级', '绩点']
  const csvContent = [
    headers.join(','),
    ...courseGrades.value.map(grade => [
      grade.studentId,
      grade.studentName,
      grade.className,
      grade.regularScore,
      grade.midtermScore,
      grade.finalScore,
      grade.totalScore,
      getGradeLevel(grade.totalScore),
      getGPA(grade.totalScore)
    ].join(','))
  ].join('\n')

  // 下载CSV文件
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  link.setAttribute('href', url)
  link.setAttribute('download', `${selectedCourse.value?.name}_成绩表.csv`)
  link.style.visibility = 'hidden'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)

  alert('成绩表导出成功！')
}

const handleDeleteCourse = (course: any) => {
  if (confirm(`确定要删除课程 "${course.name}" 吗？`)) {
    const index = courses.value.findIndex(c => c.id === course.id)
    if (index > -1) {
      courses.value.splice(index, 1)
      alert('课程删除成功！')
    }
  }
}

const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
  }
}

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
  }
}
</script>

<style scoped>
.courses-view {
  padding: 2rem;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 2rem;
}

.page-header h1 {
  font-size: 2rem;
  color: #333;
  margin: 0 0 0.5rem 0;
}

.page-header p {
  color: #666;
  margin: 0;
}

.content-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  overflow: hidden;
}

.card-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e1e5e9;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  color: #333;
}

.subtitle {
  color: #666;
  font-size: 0.9rem;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.btn-primary {
  background: #1976d2;
  color: white;
}

.btn-secondary {
  background: #f5f7fa;
  color: #666;
  border: 1px solid #e1e5e9;
}

.search-bar {
  padding: 1.5rem;
  border-bottom: 1px solid #e1e5e9;
  display: flex;
  gap: 1rem;
}

.search-input {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  font-size: 1rem;
}

.filter-select {
  padding: 0.75rem;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  min-width: 150px;
}

.search-btn {
  padding: 0.75rem 1.5rem;
  background: #1976d2;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
}

.courses-table {
  overflow-x: auto;
}

.courses-table table {
  width: 100%;
  border-collapse: collapse;
}

.courses-table th,
.courses-table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #e1e5e9;
}

.courses-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #333;
}

.courses-table code {
  background: #f5f7fa;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
}

.credit-badge,
.type-badge,
.exam-badge,
.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.credit-badge {
  background: #e3f2fd;
  color: #1976d2;
}

.type-badge.required {
  background: #ffebee;
  color: #d32f2f;
}

.type-badge.elective {
  background: #e8f5e8;
  color: #388e3c;
}

.type-badge.practice {
  background: #fff3e0;
  color: #f57c00;
}

.exam-badge.exam {
  background: #e3f2fd;
  color: #1976d2;
}

.exam-badge.assessment {
  background: #f3e5f5;
  color: #7b1fa2;
}

.exam-badge.project {
  background: #e0f2f1;
  color: #00695c;
}

.status-badge.active {
  background: #e8f5e8;
  color: #388e3c;
}

.status-badge.inactive {
  background: #ffebee;
  color: #d32f2f;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
}

.btn-small {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
}

.btn-edit {
  background: #e3f2fd;
  color: #1976d2;
}

.btn-info {
  background: #e0f2f1;
  color: #00695c;
}

.btn-delete {
  background: #ffebee;
  color: #d32f2f;
}

.pagination {
  padding: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* GPA说明样式 */
.gpa-explanation {
  padding: 1.5rem;
}

.gpa-section {
  margin-bottom: 2rem;
}

.gpa-section h3 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.1rem;
}

.formula {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #1976d2;
}

.formula p {
  margin: 0.5rem 0;
  color: #333;
}

.grade-table table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 1rem;
}

.grade-table th,
.grade-table td {
  padding: 0.75rem;
  text-align: center;
  border: 1px solid #e1e5e9;
}

.grade-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #333;
}

.graduation-requirements {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.requirement-item {
  display: flex;
  justify-content: space-between;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #1976d2;
}

.requirement-label {
  color: #666;
  font-weight: 500;
}

.requirement-value {
  color: #1976d2;
  font-weight: 600;
}

.degree-levels {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.level-item {
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid;
}

.level-item.excellent {
  background: #e8f5e8;
  border-left-color: #388e3c;
}

.level-item.good {
  background: #e3f2fd;
  border-left-color: #1976d2;
}

.level-item.normal {
  background: #fff3e0;
  border-left-color: #f57c00;
}

.level-item.warning {
  background: #ffebee;
  border-left-color: #d32f2f;
}

.level-name {
  display: block;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.level-requirement {
  color: #666;
  font-size: 0.9rem;
}

/* 权限控制样式 */
.no-permission {
  color: #999;
  font-size: 0.8rem;
  font-style: italic;
}

.no-permission-text {
  color: #999;
  font-size: 0.9rem;
  font-style: italic;
  padding: 0.75rem 1.5rem;
}

/* 成绩查看对话框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  max-width: 90vw;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.grades-modal {
  width: 1200px;
}

.modal-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e1e5e9;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.close-btn:hover {
  background: #f5f5f5;
}

.modal-body {
  padding: 1.5rem;
  overflow-y: auto;
  flex: 1;
}

.modal-footer {
  padding: 1.5rem;
  border-top: 1px solid #e1e5e9;
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

/* 成绩统计卡片 */
.grades-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
  border: 1px solid #e1e5e9;
}

.stat-label {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0.5rem;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1976d2;
}

/* 成绩表格 */
.grades-table {
  overflow-x: auto;
}

.grades-table table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;
}

.grades-table th,
.grades-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #e1e5e9;
}

.grades-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #333;
  position: sticky;
  top: 0;
}

/* 成绩徽章样式 */
.score-badge,
.grade-level,
.gpa-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
}

.score-badge.excellent,
.grade-level.excellent,
.gpa-badge.excellent {
  background: #e8f5e8;
  color: #2e7d32;
}

.score-badge.good,
.grade-level.good,
.gpa-badge.good {
  background: #e3f2fd;
  color: #1976d2;
}

.score-badge.average,
.grade-level.average,
.gpa-badge.average {
  background: #fff3e0;
  color: #f57c00;
}

.score-badge.pass,
.grade-level.pass,
.gpa-badge.pass {
  background: #fce4ec;
  color: #c2185b;
}

.score-badge.fail,
.grade-level.fail,
.gpa-badge.fail {
  background: #ffebee;
  color: #d32f2f;
}
</style>
