@echo off
chcp 65001 >nul
echo ========================================
echo   数据库连接测试工具
echo ========================================
echo.
echo 正在测试数据库连接...
echo.

echo 1. 测试MySQL服务状态...
mysql -u root -pxhxabc -e "SELECT 'MySQL连接成功!' AS status;" 2>nul
if %errorlevel% == 0 (
    echo ✅ MySQL服务正常
) else (
    echo ❌ MySQL连接失败，请检查：
    echo    - MySQL服务是否启动
    echo    - 密码是否为 xhxabc
    echo    - 端口3306是否可用
    goto :end
)

echo.
echo 2. 检查数据库是否存在...
mysql -u root -pxhxabc -e "USE studentmis_db; SELECT 'studentmis_db数据库存在!' AS status;" 2>nul
if %errorlevel% == 0 (
    echo ✅ studentmis_db数据库存在
) else (
    echo ❌ studentmis_db数据库不存在，请先导入数据库
    echo    运行: import-database.bat
    goto :end
)

echo.
echo 3. 检查用户表数据...
mysql -u root -pxhxabc studentmis_db -e "SELECT user_num, user_name, password, role_id FROM user WHERE user_num='1000';" 2>nul
if %errorlevel% == 0 (
    echo ✅ 用户表数据正常
    echo.
    echo 管理员账号信息：
    mysql -u root -pxhxabc studentmis_db -e "SELECT user_num AS '用户名', user_name AS '姓名', password AS '密码', CASE role_id WHEN 0 THEN '学生' WHEN 1 THEN '教师' WHEN 2 THEN '管理员' END AS '角色' FROM user WHERE user_num IN ('1000', '1123', '170340');"
) else (
    echo ❌ 用户表数据异常
)

:end
echo.
echo ========================================
pause
