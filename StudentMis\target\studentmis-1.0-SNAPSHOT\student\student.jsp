<%@ page import="com.ntvu.studentmis.pager.PagerHelper" %>
<%@ page import="com.ntvu.studentmis.db.DBStudent" %>
<%@ page import="com.ntvu.studentmis.entity.Student" %>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <jsp:include page="../include/header_css.jsp" flush="true"/>
    <title>学生信息列表</title>
</head>
<body class="hold-transition sidebar-mini layout-fixed">
<div class="wrapper">
    <%@include file="header_nav.jsp"%>
    <%@include file="left_menu.jsp"%>
    <%
        request.setCharacterEncoding("utf-8");
        PagerHelper<Student> pager = new PagerHelper(request);
        if (g_user.getUser_num() != null && !g_user.getUser_num().trim().equals("")) {
            pager.getQueryParams().put("stu_num", g_user.getUser_num());
        }
        if (g_user.getUser_name() != null && !g_user.getUser_name().trim().equals("")) {
            pager.getQueryParams().put("stu_name", g_user.getUser_name());
        }

        new DBStudent().getList(pager);
    %>
    <!-- Content Wrapper. Contains page content -->
    <form id="form1" name="form1" method="post"
          action="<%= request.getContextPath() + "/admin/student/list.jsp"%>">
        <div class="content-wrapper">
            <section class="content">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <!-- /.card-header -->
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-sm-12">
                                                <table id="example1"
                                                       class="table table-bordered table-striped dataTable dtr-inline"
                                                       aria-describedby="example1_info">
                                                    <thead>
                                                    <tr>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="Platform(s): activate to sort column ascending">
                                                            序号
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="Browser: activate to sort column ascending">
                                                            学号
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="Platform(s): activate to sort column ascending">
                                                            姓名
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="CSS grade: activate to sort column ascending">
                                                            性别
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="CSS grade: activate to sort column ascending">
                                                            年龄
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="CSS grade: activate to sort column ascending">
                                                            班级
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="CSS grade: activate to sort column ascending">
                                                            专业
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="CSS grade: activate to sort column ascending">
                                                            院系
                                                        </th>
                                                    </tr>
                                                    </thead>
                                                    <tbody>
                                                    <%
                                                        int index = 0;
                                                        for (Student student : pager.getData()) {
                                                            index++;
                                                    %>
                                                    <tr class="<%= index % 2 == 1 ? "odd" : "even"%>">
                                                        <td><%= index%></td>
                                                        <td class="dtr-control sorting_1"
                                                            tabindex="0"><%= student.getStu_num()%>
                                                        </td>
                                                        <td><%= student.getStu_name()%>
                                                        </td>
                                                        <td><%= student.getStu_sex()%>
                                                        </td>
                                                        <td><%= student.getStu_age()%>
                                                        </td>
                                                        <td><%= student.getStu_class()%></td>
                                                        <td><%= student.getMajor()%></td>
                                                        <td><%= student.getDepartment()%></td>
                                                    </tr>
                                                    <%
                                                        }
                                                    %>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-5">
                                                <div class="dataTables_info" id="example1_info" role="status"
                                                     aria-live="polite">每页显示10条记录
                                                </div>
                                            </div>
                                            <div class="col-sm-12 col-md-7">
                                                <%@ include file="../include/pager_footer.jsp"%>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- /.card-body -->
                            </div>
                            <!-- /.card -->
                        </div>
                        <!-- /.col -->
                    </div>
                    <!-- /.row -->
                </div>
                <!-- /.container-fluid -->
            </section>
        </div>
    </form>
</div>
<%@include file="../include/foot_js.jsp"%>
<script>
    /**
     * 跳转到指定的页
     * @param toPageIndex
     */
        //本页地址
    let pageListUrl = '/student/student.jsp';
    function doPager(toPageIndex)
    {
        $('#form1').attr('action','<%= request.getContextPath() %>' + pageListUrl + '?pageIndex=' + toPageIndex);
        $('#form1').submit();
    }
</script>

</body>
</html>
