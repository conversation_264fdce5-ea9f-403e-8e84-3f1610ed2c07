package com.ntvu.studentmis.servlet;

import com.ntvu.studentmis.db.DBManager;
import com.ntvu.studentmis.entity.User;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
    import javax.servlet.http.HttpServletRequest;
    import javax.servlet.http.HttpServletResponse;
    import javax.servlet.http.HttpSession;
import java.io.IOException;

/**
 * 处理用户登录过程
 */
public class LoginServlet extends HttpServlet {


    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        // 设置编码
        req.setCharacterEncoding("UTF-8");
        resp.setCharacterEncoding("UTF-8");
        resp.setContentType("text/html;charset=UTF-8");

        String userName = req.getParameter("txtUserName");//从请求中获取参数
        String password = req.getParameter("txtPassword");//从请求中获取参数
        HttpSession session = req.getSession();

        // 调试信息
        System.out.println("=== 登录调试信息 ===");
        System.out.println("用户名: " + userName);
        System.out.println("密码: " + password);

        //简单判断：后续修改为查询数据库
        boolean succeed = new DBManager().login(userName, password);//WebTools.md5(password)
        System.out.println("登录结果: " + succeed);
        if(succeed)
        {
            //成功
            session.setAttribute("curUserName",userName);
            User user=new DBManager().getDetails(userName);
            //身份判断
            if(user.getRole_id()==2)//管理员
            {
                System.out.println("管理员登录");
                resp.sendRedirect("admin/user/list.jsp");
            }else if(user.getRole_id()==0) {//学生
                System.out.println("学生登录");
                resp.sendRedirect("student/student.jsp");
            }else//教师
            {
                System.out.println("教师登录");
                resp.sendRedirect("teacher/teacher.jsp");
            }
        }else{
            //失败 - 先设置错误消息，再重定向
            session.setAttribute("errorMsg","账号或密码错误！");
            session.setAttribute("user_num",userName); // 保留用户名
            resp.sendRedirect("login.jsp");
            return; // 登录失败时直接返回，不执行后续代码
        }
        //登录成功时保存数据
        session.setAttribute("user_num",userName);
        session.setAttribute("password",""); // 不保存密码
    }
}
