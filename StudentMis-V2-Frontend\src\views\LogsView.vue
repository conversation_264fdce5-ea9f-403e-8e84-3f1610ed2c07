<template>
  <div class="logs-view">
    <div class="page-header">
      <h1>操作日志</h1>
      <p>查看系统操作记录和审计信息</p>
    </div>

    <div class="content-card">
      <div class="card-header">
        <h2>日志筛选</h2>
      </div>
      <div class="filter-bar">
        <div class="filter-group">
          <label>时间范围</label>
          <select v-model="filters.timeRange" class="filter-select">
            <option value="today">今天</option>
            <option value="week">最近一周</option>
            <option value="month">最近一月</option>
            <option value="custom">自定义</option>
          </select>
        </div>
        <div class="filter-group">
          <label>操作类型</label>
          <select v-model="filters.actionType" class="filter-select">
            <option value="">全部</option>
            <option value="login">登录</option>
            <option value="logout">登出</option>
            <option value="create">创建</option>
            <option value="update">更新</option>
            <option value="delete">删除</option>
            <option value="export">导出</option>
          </select>
        </div>
        <div class="filter-group">
          <label>用户</label>
          <input type="text" v-model="filters.username" placeholder="用户名" class="filter-input">
        </div>
        <div class="filter-group">
          <label>模块</label>
          <select v-model="filters.module" class="filter-select">
            <option value="">全部</option>
            <option value="user">用户管理</option>
            <option value="student">学生管理</option>
            <option value="grade">成绩管理</option>
            <option value="course">课程管理</option>
            <option value="system">系统设置</option>
          </select>
        </div>
        <button class="btn btn-primary" @click="applyFilters">
          <span class="btn-icon">🔍</span>
          筛选
        </button>
        <button class="btn btn-secondary" @click="resetFilters">
          <span class="btn-icon">🔄</span>
          重置
        </button>
      </div>
    </div>

    <div class="content-card">
      <div class="card-header">
        <h2>操作记录</h2>
        <div class="header-actions">
          <button class="btn btn-secondary" @click="exportLogs">
            <span class="btn-icon">📥</span>
            导出日志
          </button>
          <button class="btn btn-warning" @click="clearOldLogs">
            <span class="btn-icon">🗑️</span>
            清理旧日志
          </button>
        </div>
      </div>

      <div class="logs-table">
        <table>
          <thead>
            <tr>
              <th>时间</th>
              <th>用户</th>
              <th>操作类型</th>
              <th>模块</th>
              <th>操作描述</th>
              <th>IP地址</th>
              <th>状态</th>
              <th>详情</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="log in filteredLogs" :key="log.id">
              <td>{{ formatTime(log.timestamp) }}</td>
              <td>
                <div class="user-info">
                  <span class="username">{{ log.username }}</span>
                  <span class="real-name">{{ log.realName }}</span>
                </div>
              </td>
              <td>
                <span class="action-badge" :class="log.actionType">
                  {{ getActionName(log.actionType) }}
                </span>
              </td>
              <td>
                <span class="module-badge" :class="log.module">
                  {{ getModuleName(log.module) }}
                </span>
              </td>
              <td class="description">{{ log.description }}</td>
              <td>{{ log.ipAddress }}</td>
              <td>
                <span class="status-badge" :class="log.status">
                  {{ log.status === 'success' ? '成功' : '失败' }}
                </span>
              </td>
              <td>
                <button class="btn-small btn-info" @click="showLogDetail(log)">查看</button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="pagination">
        <span>共 {{ totalLogs }} 条记录</span>
        <div class="pagination-controls">
          <button class="btn-small" @click="prevPage" :disabled="currentPage === 1">上一页</button>
          <span>第 {{ currentPage }} / {{ totalPages }} 页</span>
          <button class="btn-small" @click="nextPage" :disabled="currentPage === totalPages">下一页</button>
        </div>
      </div>
    </div>

    <!-- 日志详情模态框 -->
    <div v-if="showDetailModal" class="modal-overlay" @click="closeDetailModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>操作日志详情</h3>
          <button class="close-btn" @click="closeDetailModal">✕</button>
        </div>
        <div class="modal-body">
          <div class="detail-item">
            <label>操作时间：</label>
            <span>{{ selectedLog?.timestamp }}</span>
          </div>
          <div class="detail-item">
            <label>操作用户：</label>
            <span>{{ selectedLog?.realName }} ({{ selectedLog?.username }})</span>
          </div>
          <div class="detail-item">
            <label>操作类型：</label>
            <span>{{ getActionName(selectedLog?.actionType) }}</span>
          </div>
          <div class="detail-item">
            <label>操作模块：</label>
            <span>{{ getModuleName(selectedLog?.module) }}</span>
          </div>
          <div class="detail-item">
            <label>操作描述：</label>
            <span>{{ selectedLog?.description }}</span>
          </div>
          <div class="detail-item">
            <label>IP地址：</label>
            <span>{{ selectedLog?.ipAddress }}</span>
          </div>
          <div class="detail-item">
            <label>用户代理：</label>
            <span>{{ selectedLog?.userAgent }}</span>
          </div>
          <div class="detail-item">
            <label>操作结果：</label>
            <span>{{ selectedLog?.status === 'success' ? '成功' : '失败' }}</span>
          </div>
          <div class="detail-item" v-if="selectedLog?.errorMessage">
            <label>错误信息：</label>
            <span class="error-message">{{ selectedLog?.errorMessage }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive } from 'vue'

// 筛选条件
const filters = reactive({
  timeRange: 'week',
  actionType: '',
  username: '',
  module: ''
})

// 分页
const currentPage = ref(1)
const pageSize = ref(20)

// 模态框
const showDetailModal = ref(false)
const selectedLog = ref(null)

// 模拟日志数据
const logs = ref([
  {
    id: 1,
    timestamp: '2024-06-19 14:30:25',
    username: 'admin',
    realName: '系统管理员',
    actionType: 'login',
    module: 'system',
    description: '用户登录系统',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    status: 'success'
  },
  {
    id: 2,
    timestamp: '2024-06-19 14:25:12',
    username: '2024080101',
    realName: '张伟',
    actionType: 'update',
    module: 'student',
    description: '修改个人信息',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    status: 'success'
  },
  {
    id: 3,
    timestamp: '2024-06-19 14:20:45',
    username: 'T001',
    realName: '李教授',
    actionType: 'create',
    module: 'grade',
    description: '录入学生成绩',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
    status: 'success'
  },
  {
    id: 4,
    timestamp: '2024-06-19 14:15:30',
    username: 'admin',
    realName: '系统管理员',
    actionType: 'delete',
    module: 'user',
    description: '删除用户账户',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    status: 'success'
  },
  {
    id: 5,
    timestamp: '2024-06-19 14:10:18',
    username: '2024080102',
    realName: '李娜',
    actionType: 'login',
    module: 'system',
    description: '用户登录失败',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15',
    status: 'failed',
    errorMessage: '密码错误'
  }
])

// 计算属性
const filteredLogs = computed(() => {
  return logs.value.filter(log => {
    if (filters.actionType && log.actionType !== filters.actionType) return false
    if (filters.username && !log.username.includes(filters.username)) return false
    if (filters.module && log.module !== filters.module) return false
    return true
  })
})

const totalLogs = computed(() => filteredLogs.value.length)
const totalPages = computed(() => Math.ceil(totalLogs.value / pageSize.value))

// 方法
const formatTime = (timestamp: string) => {
  return timestamp
}

const getActionName = (actionType: string) => {
  const actionMap = {
    login: '登录',
    logout: '登出',
    create: '创建',
    update: '更新',
    delete: '删除',
    export: '导出'
  }
  return actionMap[actionType] || actionType
}

const getModuleName = (module: string) => {
  const moduleMap = {
    system: '系统',
    user: '用户管理',
    student: '学生管理',
    grade: '成绩管理',
    course: '课程管理'
  }
  return moduleMap[module] || module
}

const applyFilters = () => {
  currentPage.value = 1
  console.log('应用筛选条件:', filters)
}

const resetFilters = () => {
  filters.timeRange = 'week'
  filters.actionType = ''
  filters.username = ''
  filters.module = ''
  currentPage.value = 1
}

const exportLogs = () => {
  alert('导出日志功能\n\n将导出当前筛选条件下的所有日志记录为Excel文件')
}

const clearOldLogs = () => {
  if (confirm('确定要清理30天前的操作日志吗？此操作不可恢复。')) {
    alert('旧日志清理完成！\n\n已清理30天前的操作日志记录。')
  }
}

const showLogDetail = (log: any) => {
  selectedLog.value = log
  showDetailModal.value = true
}

const closeDetailModal = () => {
  showDetailModal.value = false
  selectedLog.value = null
}

const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
  }
}

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
  }
}
</script>

<style scoped>
.logs-view {
  padding: 2rem;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 2rem;
}

.page-header h1 {
  font-size: 2rem;
  color: #333;
  margin: 0 0 0.5rem 0;
}

.page-header p {
  color: #666;
  margin: 0;
}

.content-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  overflow: hidden;
}

.card-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e1e5e9;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

.filter-bar {
  padding: 1.5rem;
  display: flex;
  gap: 1rem;
  align-items: end;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-group label {
  font-size: 0.9rem;
  color: #666;
  font-weight: 500;
}

.filter-select,
.filter-input {
  padding: 0.5rem;
  border: 1px solid #e1e5e9;
  border-radius: 6px;
  min-width: 120px;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.btn-primary {
  background: #1976d2;
  color: white;
}

.btn-secondary {
  background: #f5f7fa;
  color: #666;
  border: 1px solid #e1e5e9;
}

.btn-warning {
  background: #ff9800;
  color: white;
}

.logs-table {
  overflow-x: auto;
}

.logs-table table {
  width: 100%;
  border-collapse: collapse;
}

.logs-table th,
.logs-table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #e1e5e9;
}

.logs-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #333;
}

.user-info {
  display: flex;
  flex-direction: column;
}

.username {
  font-weight: 500;
  color: #333;
}

.real-name {
  font-size: 0.8rem;
  color: #666;
}

.action-badge,
.module-badge,
.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.action-badge.login {
  background: #e8f5e8;
  color: #388e3c;
}

.action-badge.logout {
  background: #fff3e0;
  color: #f57c00;
}

.action-badge.create {
  background: #e3f2fd;
  color: #1976d2;
}

.action-badge.update {
  background: #f3e5f5;
  color: #7b1fa2;
}

.action-badge.delete {
  background: #ffebee;
  color: #d32f2f;
}

.action-badge.export {
  background: #e0f2f1;
  color: #00695c;
}

.module-badge.system {
  background: #e3f2fd;
  color: #1976d2;
}

.module-badge.user {
  background: #f3e5f5;
  color: #7b1fa2;
}

.module-badge.student {
  background: #e8f5e8;
  color: #388e3c;
}

.module-badge.grade {
  background: #fff3e0;
  color: #f57c00;
}

.module-badge.course {
  background: #fce4ec;
  color: #c2185b;
}

.status-badge.success {
  background: #e8f5e8;
  color: #388e3c;
}

.status-badge.failed {
  background: #ffebee;
  color: #d32f2f;
}

.description {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.btn-small {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
}

.btn-info {
  background: #e3f2fd;
  color: #1976d2;
}

.pagination {
  padding: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e1e5e9;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #666;
}

.modal-body {
  padding: 1.5rem;
}

.detail-item {
  display: flex;
  margin-bottom: 1rem;
  align-items: flex-start;
}

.detail-item label {
  min-width: 100px;
  font-weight: 500;
  color: #333;
}

.detail-item span {
  flex: 1;
  color: #666;
}

.error-message {
  color: #d32f2f !important;
  font-weight: 500;
}
</style>
