<%@ page import="com.ntvu.studentmis.entity.Course" %>
<%@ page import="com.ntvu.studentmis.db.DBCourse" %>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <jsp:include page="../../include/header_css.jsp" flush="true"/>
    <title>添加课程信息</title>
</head>
<body class="hold-transition sidebar-mini layout-fixed">
<div class="wrapper">
    <%@include file="../../include/header_nav.jsp"%>
    <%@include file="../../include/left_menu.jsp"%>
    <div class="content-wrapper" style="min-height: 1345.6px;">
        <!-- Content Header (Page header) -->
        <section class="content-header">
            <div class="container-fluid">
                <div class="row mb-2">
                    <div class="col-sm-6">
                        <h1>添加课程信息</h1>
                    </div>
                </div>
            </div><!-- /.container-fluid -->
        </section>

        <!-- Main content -->
        <section class="content">
            <div class="container-fluid">
                <div class="row">
                    <!-- left column -->
                    <div class="col-md-12">
                        <!-- jquery validation -->
                        <div class="card card-primary">
                            <div class="card-header">
                                <h3 class="card-title">请添加<small>课程信息</small></h3>
                            </div>
                            <!-- /.card-header -->
                            <!-- form start -->
                            <form name="form1" id="form1" method="post" action="<%= request.getContextPath() + "/admin/course/CourseServlet?action=add"%>" onsubmit="return verify()">
                                <div class="card-body">
                                    <div class="form-group">
                                        <label>课程编码 <span class="text-muted">(自动生成)</span></label>
                                        <input type="text" name="txtCourseCode" class="form-control" placeholder="系统将自动生成课程编码" readonly style="background-color: #f8f9fa;">
                                    </div>
                                    <div class="form-group">
                                        <label >科目</label>
                                        <input type="text" name="txtName" class="form-control" placeholder="请输入科目">
                                    </div>
                                    <div class="form-group">
                                        <label>课程性质</label>
                                        <select name="txtCourseType" class="form-control">
                                            <option value="REQUIRED">必修</option>
                                            <option value="ELECTIVE">选修</option>
                                            <option value="PUBLIC_REQUIRED">公共必修</option>
                                            <option value="PUBLIC_ELECTIVE">公共选修</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label >学分</label>
                                        <input type="text" name="txtCredit" class="form-control"  placeholder="请输入学分">
                                    </div>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label>理论学时</label>
                                                <input type="text" name="txtTheoryHours" class="form-control" placeholder="理论学时" onchange="calculateTotalHours()">
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label>实践学时</label>
                                                <input type="text" name="txtPracticeHours" class="form-control" placeholder="实践学时" onchange="calculateTotalHours()">
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label>总学时 <span class="text-muted">(自动计算)</span></label>
                                                <input type="text" name="txtHours" class="form-control" placeholder="总学时" readonly style="background-color: #f8f9fa;">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label>任课教师</label>
                                        <input type="text" name="txtTeacher" class="form-control"  placeholder="请输入任课教师">
                                    </div>
                                    <div class="form-group">
                                        <label>开设时间</label>
                                        <input type="text" name="txtDate" class="form-control"  placeholder="请输入开设时间">
                                    </div>
                                </div>
                                <!-- /.card-body -->
                                <div class="card-footer">
                                    <input type="submit" class="btn btn-primary" name="btnSubmit" value="提交">
                                </div>
                            </form>
                        </div>
                        <!-- /.card -->
                    </div>
                    <!--/.col (left) -->
                    <!-- right column -->
                    <div class="col-md-6">

                    </div>
                    <!--/.col (right) -->
                </div>
                <!-- /.row -->
            </div><!-- /.container-fluid -->
        </section>
        <!-- /.content -->
    </div>
</div>
<%@include file="../../include/foot_js.jsp"%>
<script>
    /**
     * 计算总学时
     */
    function calculateTotalHours() {
        const theoryHours = parseInt($('input[name=txtTheoryHours]').val()) || 0;
        const practiceHours = parseInt($('input[name=txtPracticeHours]').val()) || 0;
        const totalHours = theoryHours + practiceHours;
        $('input[name=txtHours]').val(totalHours);
    }

    /**
     * 智能分配学时 - 根据总学时自动分配理论和实践学时
     */
    function autoAllocateHours() {
        const totalHours = parseInt($('input[name=txtHours]').val()) || 0;
        if (totalHours > 0) {
            const theoryHours = Math.round(totalHours * 0.7);
            const practiceHours = totalHours - theoryHours;
            $('input[name=txtTheoryHours]').val(theoryHours);
            $('input[name=txtPracticeHours]').val(practiceHours);
        }
    }

    /**
     * 生成课程编码
     */
    function generateCourseCode() {
        const courseName = $('input[name=txtName]').val();
        if (courseName) {
            // 简单的编码生成逻辑：取课程名前两个字符的拼音首字母 + 随机数
            const timestamp = new Date().getTime().toString().slice(-4);
            const courseCode = 'COURSE_' + timestamp;
            $('input[name=txtCourseCode]').val(courseCode);
        }
    }

    $(document).ready(function() {
        // 课程名称变化时自动生成编码
        $('input[name=txtName]').on('blur', function() {
            generateCourseCode();
        });

        // 总学时变化时提供智能分配选项
        $('input[name=txtHours]').on('blur', function() {
            const totalHours = parseInt($(this).val()) || 0;
            const theoryHours = parseInt($('input[name=txtTheoryHours]').val()) || 0;
            const practiceHours = parseInt($('input[name=txtPracticeHours]').val()) || 0;

            // 如果理论和实践学时都为空，提供自动分配
            if (totalHours > 0 && theoryHours === 0 && practiceHours === 0) {
                if (confirm('是否自动分配学时？\n建议分配：理论学时70%，实践学时30%')) {
                    autoAllocateHours();
                }
            }
        });
    });

    function verify()
    {
        console.log(`click`);
        //对数据进行检验
        let txtName=$(`input[name=txtName]`).val();
        if(txtName==='')
        {
            alert(`科目不能为空`);
            $(`input[name=txtName]`).focus();//光标选中
            return false;
        }
        let txtCredit=$(`input[name=txtCredit]`).val();
        if(txtCredit==='')
        {
            alert(`学分不能为空`);
            $(`input[name=txtCredit]`).focus();//光标选中
            return false;
        }
        // 验证理论学时
        let txtTheoryHours=$(`input[name=txtTheoryHours]`).val();
        if(txtTheoryHours==='' || isNaN(txtTheoryHours) || parseInt(txtTheoryHours) < 0)
        {
            alert(`理论学时不能为空且必须为非负数`);
            $(`input[name=txtTheoryHours]`).focus();
            return false;
        }

        // 验证实践学时
        let txtPracticeHours=$(`input[name=txtPracticeHours]`).val();
        if(txtPracticeHours==='' || isNaN(txtPracticeHours) || parseInt(txtPracticeHours) < 0)
        {
            alert(`实践学时不能为空且必须为非负数`);
            $(`input[name=txtPracticeHours]`).focus();
            return false;
        }

        // 验证总学时
        let txtHours=$(`input[name=txtHours]`).val();
        if(txtHours==='' || isNaN(txtHours) || parseInt(txtHours) <= 0)
        {
            alert(`总学时不能为空且必须为正数`);
            $(`input[name=txtHours]`).focus();
            return false;
        }

        // 验证学时逻辑一致性
        const theoryHours = parseInt(txtTheoryHours);
        const practiceHours = parseInt(txtPracticeHours);
        const totalHours = parseInt(txtHours);

        if(theoryHours + practiceHours !== totalHours)
        {
            alert(`理论学时(${theoryHours}) + 实践学时(${practiceHours}) 必须等于总学时(${totalHours})`);
            return false;
        }
        let txtTeacher=$(`input[name=txtTeacher]`).val();
        if(txtTeacher==='')
        {
            alert(`任课老师不能为空`);
            $(`input[name=txtTeacher]`).focus();//光标选中
            return false;
        }
        let txtDate=$(`input[name=txtDate]`).val();
        if(txtDate==='')
        {
            alert(`日期不能为空`);
            $(`input[name=txtDate]`).focus();//光标选中
            return false;
        }
    }
</script>

</body>
</html>
