<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns="http://xmlns.jcp.org/xml/ns/javaee"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/javaee http://xmlns.jcp.org/xml/ns/javaee/web-app_4_0.xsd"
         version="4.0">

    <!-- 编码过滤器 -->
    <filter>
        <filter-name>CharacterEncodingFilter</filter-name>
        <filter-class>com.ntvu.studentmis.filter.CharacterEncodingFilter</filter-class>
        <init-param>
            <param-name>encoding</param-name>
            <param-value>UTF-8</param-value>
        </init-param>
    </filter>
    <filter-mapping>
        <filter-name>CharacterEncodingFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <!--首次展示界面 -->
    <welcome-file-list>
        <welcome-file>login.jsp</welcome-file>
    </welcome-file-list>

    <!--LoginServlet -->
    <servlet>
        <servlet-name>LoginServlet</servlet-name>
        <servlet-class>com.ntvu.studentmis.servlet.LoginServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>LoginServlet</servlet-name>
        <url-pattern>/Login</url-pattern>
    </servlet-mapping>

    <!--LoginOutServlet -->
    <servlet>
        <servlet-name>LoginOutServlet</servlet-name>
        <servlet-class>com.ntvu.studentmis.servlet.LogoutServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>LoginOutServlet</servlet-name>
        <url-pattern>/LoginOut</url-pattern>
    </servlet-mapping>

    <!--UserServlet -->
    <servlet>
        <servlet-name>UserServlet</servlet-name>
        <servlet-class>com.ntvu.studentmis.servlet.UserServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>UserServlet</servlet-name>
        <url-pattern>/admin/user/UserServlet</url-pattern>
    </servlet-mapping>

    <!--StudentServlet -->
    <servlet>
        <servlet-name>StudentServlet</servlet-name>
        <servlet-class>com.ntvu.studentmis.servlet.StudentServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>StudentServlet</servlet-name>
        <url-pattern>/admin/student/StudentServlet</url-pattern>
    </servlet-mapping>

    <!--ScoreServlet -->
    <servlet>
        <servlet-name>ScoreServlet</servlet-name>
        <servlet-class>com.ntvu.studentmis.servlet.ScoreServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ScoreServlet</servlet-name>
        <url-pattern>/admin/score/ScoreServlet</url-pattern>
    </servlet-mapping>

    <!--CourseServlet -->
    <servlet>
        <servlet-name>CourseServlet</servlet-name>
        <servlet-class>com.ntvu.studentmis.servlet.CourseServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>CourseServlet</servlet-name>
        <url-pattern>/admin/course/CourseServlet</url-pattern>
    </servlet-mapping>

    <!--TeacherServlet -->
    <servlet>
        <servlet-name>TeacherServlet</servlet-name>
        <servlet-class>com.ntvu.studentmis.servlet.TeacherServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>TeacherServlet</servlet-name>
        <url-pattern>/admin/teacher/TeacherServlet</url-pattern>
    </servlet-mapping>

    <!--TeacherStudentServlet -->
    <servlet>
        <servlet-name>TeacherStudentServlet</servlet-name>
        <servlet-class>com.ntvu.studentmis.servlet.TeacherStudentServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>TeacherStudentServlet</servlet-name>
        <url-pattern>/teacher/Student/TeacherStudentServlet</url-pattern>
    </servlet-mapping>
    <!--TeacherServlet -->
    <servlet>
        <servlet-name>TeacherScoreServlet</servlet-name>
        <servlet-class>com.ntvu.studentmis.servlet.TeacherScoreServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>TeacherScoreServlet</servlet-name>
        <url-pattern>/teacher/Score/TeacherScoreServlet</url-pattern>
    </servlet-mapping>

    <!--ToExcel -->
    <servlet>
        <servlet-name>ToExcel</servlet-name>
        <servlet-class>com.ntvu.studentmis.util.ToExcel</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ToExcel</servlet-name>
        <url-pattern>/admin/student/ToExcel</url-pattern>
    </servlet-mapping>
</web-app>