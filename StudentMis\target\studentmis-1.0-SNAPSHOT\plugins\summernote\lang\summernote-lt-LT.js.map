{"version": 3, "file": "lang/summernote-lt-LT.js", "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAT,EAAY;AACXA,EAAAA,CAAC,CAACC,MAAF,CAASD,CAAC,CAACE,UAAF,CAAaC,IAAtB,EAA4B;AAC1B,aAAS;AACPC,MAAAA,IAAI,EAAE;AACJC,QAAAA,IAAI,EAAE,aADF;AAEJC,QAAAA,MAAM,EAAE,UAFJ;AAGJC,QAAAA,SAAS,EAAE,WAHP;AAIJC,QAAAA,KAAK,EAAE,gBAJH;AAKJC,QAAAA,MAAM,EAAE,iBALJ;AAMJC,QAAAA,IAAI,EAAE,oBANF;AAOJC,QAAAA,aAAa,EAAE,aAPX;AAQJC,QAAAA,WAAW,EAAE,YART;AASJC,QAAAA,SAAS,EAAE,UATP;AAUJC,QAAAA,IAAI,EAAE;AAVF,OADC;AAaPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,cADF;AAELC,QAAAA,MAAM,EAAE,qBAFH;AAGLC,QAAAA,UAAU,EAAE,cAHP;AAILC,QAAAA,UAAU,EAAE,oBAJP;AAKLC,QAAAA,aAAa,EAAE,oBALV;AAMLC,QAAAA,SAAS,EAAE,qBANN;AAOLC,QAAAA,UAAU,EAAE,sBAPP;AAQLC,QAAAA,SAAS,EAAE,iBARN;AASLC,QAAAA,YAAY,EAAE,wBATT;AAULC,QAAAA,WAAW,EAAE,oBAVR;AAWLC,QAAAA,cAAc,EAAE,mBAXX;AAYLC,QAAAA,SAAS,EAAE,cAZN;AAaLC,QAAAA,aAAa,EAAE,yBAbV;AAcLC,QAAAA,SAAS,EAAE,oBAdN;AAeLC,QAAAA,eAAe,EAAE,mBAfZ;AAgBLC,QAAAA,eAAe,EAAE,wBAhBZ;AAiBLC,QAAAA,oBAAoB,EAAE,kCAjBjB;AAkBLC,QAAAA,GAAG,EAAE,0BAlBA;AAmBLC,QAAAA,MAAM,EAAE,sBAnBH;AAoBLC,QAAAA,QAAQ,EAAE;AApBL,OAbA;AAmCPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,SAAS,EAAE,YAFN;AAGLpB,QAAAA,MAAM,EAAE,cAHH;AAILgB,QAAAA,GAAG,EAAE,YAJA;AAKLK,QAAAA,SAAS,EAAE;AALN,OAnCA;AA0CPC,MAAAA,IAAI,EAAE;AACJA,QAAAA,IAAI,EAAE,SADF;AAEJtB,QAAAA,MAAM,EAAE,iBAFJ;AAGJuB,QAAAA,MAAM,EAAE,mBAHJ;AAIJC,QAAAA,IAAI,EAAE,WAJF;AAKJC,QAAAA,aAAa,EAAE,iBALX;AAMJT,QAAAA,GAAG,EAAE,gCAND;AAOJU,QAAAA,eAAe,EAAE;AAPb,OA1CC;AAmDPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,SADF;AAELC,QAAAA,WAAW,EAAE,eAFR;AAGLC,QAAAA,WAAW,EAAE,eAHR;AAILC,QAAAA,UAAU,EAAE,iBAJP;AAKLC,QAAAA,WAAW,EAAE,kBALR;AAMLC,QAAAA,MAAM,EAAE,YANH;AAOLC,QAAAA,MAAM,EAAE,eAPH;AAQLC,QAAAA,QAAQ,EAAE;AARL,OAnDA;AA6DPC,MAAAA,EAAE,EAAE;AACFnC,QAAAA,MAAM,EAAE;AADN,OA7DG;AAgEPoC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,SADF;AAELC,QAAAA,CAAC,EAAE,KAFE;AAGLC,QAAAA,UAAU,EAAE,QAHP;AAILC,QAAAA,GAAG,EAAE,OAJA;AAKLC,QAAAA,EAAE,EAAE,YALC;AAMLC,QAAAA,EAAE,EAAE,YANC;AAOLC,QAAAA,EAAE,EAAE,YAPC;AAQLC,QAAAA,EAAE,EAAE,YARC;AASLC,QAAAA,EAAE,EAAE,YATC;AAULC,QAAAA,EAAE,EAAE;AAVC,OAhEA;AA4EPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,SAAS,EAAE,wBADN;AAELC,QAAAA,OAAO,EAAE;AAFJ,OA5EA;AAgFPC,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,SADC;AAEPC,QAAAA,UAAU,EAAE,qBAFL;AAGPC,QAAAA,QAAQ,EAAE;AAHH,OAhFF;AAqFPC,MAAAA,SAAS,EAAE;AACTA,QAAAA,SAAS,EAAE,WADF;AAETC,QAAAA,OAAO,EAAE,mBAFA;AAGTC,QAAAA,MAAM,EAAE,mBAHC;AAITC,QAAAA,IAAI,EAAE,kBAJG;AAKTC,QAAAA,MAAM,EAAE,mBALC;AAMTC,QAAAA,KAAK,EAAE,mBANE;AAOTC,QAAAA,OAAO,EAAE;AAPA,OArFJ;AA8FPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,MAAM,EAAE,0BADH;AAELC,QAAAA,IAAI,EAAE,gBAFD;AAGLC,QAAAA,UAAU,EAAE,aAHP;AAILC,QAAAA,UAAU,EAAE,eAJP;AAKLC,QAAAA,WAAW,EAAE,WALR;AAMLC,QAAAA,cAAc,EAAE,iCANX;AAOLC,QAAAA,KAAK,EAAE,SAPF;AAQLC,QAAAA,cAAc,EAAE;AARX,OA9FA;AAwGPC,MAAAA,QAAQ,EAAE;AACRC,QAAAA,SAAS,EAAE,oBADH;AAERC,QAAAA,KAAK,EAAE,UAFC;AAGRC,QAAAA,cAAc,EAAE,qBAHR;AAIRC,QAAAA,MAAM,EAAE,UAJA;AAKRC,QAAAA,mBAAmB,EAAE,yBALb;AAMRC,QAAAA,aAAa,EAAE,mBANP;AAORC,QAAAA,SAAS,EAAE;AAPH,OAxGH;AAiHP1B,MAAAA,IAAI,EAAE;AACJ,2BAAmB,kBADf;AAEJ,gBAAQ,yBAFJ;AAGJ,gBAAQ,yBAHJ;AAIJ,eAAO,KAJH;AAKJ,iBAAS,OALL;AAMJ,gBAAQ,kBANJ;AAOJ,kBAAU,oBAPN;AAQJ,qBAAa,uBART;AASJ,yBAAiB,2BATb;AAUJ,wBAAgB,eAVZ;AAWJ,uBAAe,gBAXX;AAYJ,yBAAiB,kBAZb;AAaJ,wBAAgB,iBAbZ;AAcJ,uBAAe,gBAdX;AAeJ,+BAAuB,uBAfnB;AAgBJ,6BAAqB,qBAhBjB;AAiBJ,mBAAW,8BAjBP;AAkBJ,kBAAU,6BAlBN;AAmBJ,sBAAc,sDAnBV;AAoBJ,oBAAY,sCApBR;AAqBJ,oBAAY,sCArBR;AAsBJ,oBAAY,sCAtBR;AAuBJ,oBAAY,sCAvBR;AAwBJ,oBAAY,sCAxBR;AAyBJ,oBAAY,sCAzBR;AA0BJ,gCAAwB,wBA1BpB;AA2BJ,2BAAmB;AA3Bf,OAjHC;AA8IP2B,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,mBADC;AAEPC,QAAAA,IAAI,EAAE;AAFC,OA9IF;AAkJPC,MAAAA,WAAW,EAAE;AACXA,QAAAA,WAAW,EAAE,oBADF;AAEXC,QAAAA,MAAM,EAAE;AAFG;AAlJN;AADiB,GAA5B;AAyJD,CA1JD,EA0JGC,MA1JH", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-lt-LT.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, function() {\nreturn ", "(function($) {\n  $.extend($.summernote.lang, {\n    'lt-LT': {\n      font: {\n        bold: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n        italic: '<PERSON><PERSON><PERSON><PERSON>',\n        underline: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\n        clear: 'Be formatavimo',\n        height: '<PERSON><PERSON><PERSON><PERSON><PERSON> au<PERSON>',\n        name: '<PERSON><PERSON><PERSON> pavadinimas',\n        strikethrough: '<PERSON><PERSON><PERSON><PERSON>',\n        superscript: 'Vir<PERSON><PERSON><PERSON>',\n        subscript: 'Indeksas',\n        size: 'Šrifto dydis',\n      },\n      image: {\n        image: 'Pa<PERSON><PERSON><PERSON>l<PERSON><PERSON>',\n        insert: 'Įterpti paveikslėlį',\n        resizeFull: 'Piln<PERSON> dydis',\n        resizeHalf: 'Sumažinti dydį 50%',\n        resizeQuarter: 'Su<PERSON><PERSON>inti dydį 25%',\n        floatLeft: 'Kairinis lygiavimas',\n        floatRight: 'Dešininis lygiavimas',\n        floatNone: 'Jokio lygiavimo',\n        shapeRounded: 'Forma: apvalūs kraštai',\n        shapeCircle: 'Forma: apskritimas',\n        shapeThumbnail: 'Forma: miniatiūra',\n        shapeNone: 'Forma: jokia',\n        dragImageHere: 'Vilkite paveikslėlį čia',\n        dropImage: 'Drop image or Text',\n        selectFromFiles: 'Pasirinkite failą',\n        maximumFileSize: 'Maskimalus failo dydis',\n        maximumFileSizeError: 'Maskimalus failo dydis viršytas!',\n        url: 'Paveikslėlio URL adresas',\n        remove: 'Ištrinti paveikslėlį',\n        original: 'Original',\n      },\n      video: {\n        video: 'Video',\n        videoLink: 'Video Link',\n        insert: 'Insert Video',\n        url: 'Video URL?',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion or Youku)',\n      },\n      link: {\n        link: 'Nuoroda',\n        insert: 'Įterpti nuorodą',\n        unlink: 'Pašalinti nuorodą',\n        edit: 'Redaguoti',\n        textToDisplay: 'Rodomas tekstas',\n        url: 'Koks URL adresas yra susietas?',\n        openInNewWindow: 'Atidaryti naujame lange',\n      },\n      table: {\n        table: 'Lentelė',\n        addRowAbove: 'Add row above',\n        addRowBelow: 'Add row below',\n        addColLeft: 'Add column left',\n        addColRight: 'Add column right',\n        delRow: 'Delete row',\n        delCol: 'Delete column',\n        delTable: 'Delete table',\n      },\n      hr: {\n        insert: 'Įterpti horizontalią liniją',\n      },\n      style: {\n        style: 'Stilius',\n        p: 'pus',\n        blockquote: 'Citata',\n        pre: 'Kodas',\n        h1: 'Antraštė 1',\n        h2: 'Antraštė 2',\n        h3: 'Antraštė 3',\n        h4: 'Antraštė 4',\n        h5: 'Antraštė 5',\n        h6: 'Antraštė 6',\n      },\n      lists: {\n        unordered: 'Suženklintasis sąrašas',\n        ordered: 'Sunumeruotas sąrašas',\n      },\n      options: {\n        help: 'Pagalba',\n        fullscreen: 'Viso ekrano režimas',\n        codeview: 'HTML kodo peržiūra',\n      },\n      paragraph: {\n        paragraph: 'Pastraipa',\n        outdent: 'Sumažinti įtrauką',\n        indent: 'Padidinti įtrauką',\n        left: 'Kairinė lygiuotė',\n        center: 'Centrinė lygiuotė',\n        right: 'Dešininė lygiuotė',\n        justify: 'Abipusis išlyginimas',\n      },\n      color: {\n        recent: 'Paskutinė naudota spalva',\n        more: 'Daugiau spalvų',\n        background: 'Fono spalva',\n        foreground: 'Šrifto spalva',\n        transparent: 'Permatoma',\n        setTransparent: 'Nustatyti skaidrumo intensyvumą',\n        reset: 'Atkurti',\n        resetToDefault: 'Atstatyti numatytąją spalvą',\n      },\n      shortcut: {\n        shortcuts: 'Spartieji klavišai',\n        close: 'Uždaryti',\n        textFormatting: 'Teksto formatavimas',\n        action: 'Veiksmas',\n        paragraphFormatting: 'Pastraipos formatavimas',\n        documentStyle: 'Dokumento stilius',\n        extraKeys: 'Papildomi klavišų deriniai',\n      },\n      help: {\n        'insertParagraph': 'Insert Paragraph',\n        'undo': 'Undoes the last command',\n        'redo': 'Redoes the last command',\n        'tab': 'Tab',\n        'untab': 'Untab',\n        'bold': 'Set a bold style',\n        'italic': 'Set a italic style',\n        'underline': 'Set a underline style',\n        'strikethrough': 'Set a strikethrough style',\n        'removeFormat': 'Clean a style',\n        'justifyLeft': 'Set left align',\n        'justifyCenter': 'Set center align',\n        'justifyRight': 'Set right align',\n        'justifyFull': 'Set full align',\n        'insertUnorderedList': 'Toggle unordered list',\n        'insertOrderedList': 'Toggle ordered list',\n        'outdent': 'Outdent on current paragraph',\n        'indent': 'Indent on current paragraph',\n        'formatPara': 'Change current block\\'s format as a paragraph(P tag)',\n        'formatH1': 'Change current block\\'s format as H1',\n        'formatH2': 'Change current block\\'s format as H2',\n        'formatH3': 'Change current block\\'s format as H3',\n        'formatH4': 'Change current block\\'s format as H4',\n        'formatH5': 'Change current block\\'s format as H5',\n        'formatH6': 'Change current block\\'s format as H6',\n        'insertHorizontalRule': 'Insert horizontal rule',\n        'linkDialog.show': 'Show Link Dialog',\n      },\n      history: {\n        undo: 'Anuliuoti veiksmą',\n        redo: 'Perdaryti veiksmą',\n      },\n      specialChar: {\n        specialChar: 'SPECIAL CHARACTERS',\n        select: 'Select Special characters',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "superscript", "subscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}