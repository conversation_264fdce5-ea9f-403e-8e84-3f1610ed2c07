<template>
  <div class="roles-view">
    <div class="page-header">
      <h1>角色权限管理</h1>
      <p>管理系统中的角色和权限配置</p>
    </div>

    <div class="content-card">
      <div class="card-header">
        <h2>角色列表</h2>
        <button
          v-if="hasManagePermission"
          class="btn btn-primary"
          @click="handleAddRole"
        >
          <span class="btn-icon">➕</span>
          添加角色
        </button>
        <span v-if="!hasManagePermission" class="no-permission-text">仅查看模式</span>
      </div>

      <div class="roles-grid">
        <div v-for="role in roles" :key="role.id" class="role-card">
          <div class="role-header">
            <h3>{{ role.name }}</h3>
            <div class="role-actions">
              <button
                v-if="hasManagePermission"
                class="btn-small btn-edit"
                @click="handleEditRole(role)"
              >
                编辑
              </button>
              <button
                v-if="hasManagePermission"
                class="btn-small btn-delete"
                @click="handleDeleteRole(role)"
              >
                删除
              </button>
              <span v-if="!hasManagePermission" class="no-permission">无权限</span>
            </div>
          </div>
          <p class="role-description">{{ role.description }}</p>
          <div class="role-stats">
            <span class="stat-item">
              <span class="stat-label">用户数：</span>
              <span class="stat-value">{{ role.userCount }}</span>
            </span>
            <span class="stat-item">
              <span class="stat-label">权限数：</span>
              <span class="stat-value">{{ role.permissions.length }}</span>
            </span>
          </div>
          <div class="permissions-preview">
            <h4>主要权限：</h4>
            <div class="permission-tags">
              <span 
                v-for="permission in role.permissions.slice(0, 3)" 
                :key="permission"
                class="permission-tag"
              >
                {{ getPermissionName(permission) }}
              </span>
              <span v-if="role.permissions.length > 3" class="more-permissions">
                +{{ role.permissions.length - 3 }}个权限
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="content-card">
      <div class="card-header">
        <h2>权限管理</h2>
        <button
          v-if="hasManagePermission"
          class="btn btn-secondary"
          @click="handleRefreshPermissions"
        >
          <span class="btn-icon">🔄</span>
          刷新权限
        </button>
        <span v-if="!hasManagePermission" class="no-permission-text">仅查看模式</span>
      </div>

      <div class="permissions-table">
        <table>
          <thead>
            <tr>
              <th>权限代码</th>
              <th>权限名称</th>
              <th>权限描述</th>
              <th>所属模块</th>
              <th>使用角色</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="permission in permissions" :key="permission.code">
              <td><code>{{ permission.code }}</code></td>
              <td>{{ permission.name }}</td>
              <td>{{ permission.description }}</td>
              <td>
                <span class="module-badge" :class="permission.module">
                  {{ permission.module }}
                </span>
              </td>
              <td>
                <div class="role-tags">
                  <span 
                    v-for="roleName in getRolesWithPermission(permission.code)" 
                    :key="roleName"
                    class="role-tag"
                  >
                    {{ roleName }}
                  </span>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 角色编辑对话框 -->
    <div v-if="showRoleDialog" class="modal-overlay" @click="closeRoleDialog">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>{{ editingRole.id ? '编辑角色' : '添加角色' }}</h3>
          <button class="close-btn" @click="closeRoleDialog">×</button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="saveRole">
            <div class="form-group">
              <label>角色名称</label>
              <input
                type="text"
                v-model="editingRole.name"
                class="form-input"
                placeholder="请输入角色名称"
                required
              >
            </div>
            <div class="form-group">
              <label>角色描述</label>
              <textarea
                v-model="editingRole.description"
                class="form-input"
                placeholder="请输入角色描述"
                rows="3"
                required
              ></textarea>
            </div>
            <div class="form-group">
              <label>权限配置</label>
              <div class="permissions-grid">
                <div v-for="permission in permissions" :key="permission.code" class="permission-item">
                  <label class="permission-checkbox">
                    <input
                      type="checkbox"
                      :value="permission.code"
                      v-model="editingRole.permissions"
                    >
                    <span class="checkmark"></span>
                    <div class="permission-info">
                      <div class="permission-name">{{ permission.name }}</div>
                      <div class="permission-desc">{{ permission.description }}</div>
                      <span class="module-badge" :class="permission.module">{{ permission.module }}</span>
                    </div>
                  </label>
                </div>
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" @click="closeRoleDialog">取消</button>
          <button type="button" class="btn btn-primary" @click="saveRole">保存</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { permissionManager } from '@/utils/permissions'

// 响应式数据
const showRoleDialog = ref(false)
const editingRole = ref({
  id: 0,
  name: '',
  description: '',
  userCount: 0,
  permissions: []
})

// 角色数据 - 支持本地存储持久化
const roles = ref([])

// 初始化角色数据
const initializeRoles = () => {
  // 尝试从localStorage加载角色数据
  const savedRoles = localStorage.getItem('systemRoles')
  if (savedRoles) {
    roles.value = JSON.parse(savedRoles)
  } else {
    // 默认角色数据
    roles.value = [
      {
        id: 1,
        name: '超级管理员',
        description: '拥有系统所有权限，可以管理所有功能模块',
        userCount: 1,
        permissions: ['user.manage', 'role.manage', 'system.config', 'data.export', 'log.view', 'student.manage', 'grade.manage', 'course.manage', 'report.view']
      },
      {
        id: 2,
        name: '教务管理员',
        description: '负责学生信息和成绩管理，课程安排等教务工作',
        userCount: 5,
        permissions: ['student.manage', 'grade.manage', 'course.manage', 'report.view', 'student.view', 'grade.input']
      },
      {
        id: 3,
        name: '教师',
        description: '可以查看和录入所授课程的学生成绩',
        userCount: 120,
        permissions: ['grade.input', 'student.view', 'course.view', 'grade.view']
      },
      {
        id: 4,
        name: '学生',
        description: '可以查看个人信息和成绩，进行选课等操作',
        userCount: 5000,
        permissions: ['profile.view', 'grade.view', 'course.select', 'course.view']
      }
    ]
    saveRoles()
  }
}

// 保存角色数据到localStorage
const saveRoles = () => {
  localStorage.setItem('systemRoles', JSON.stringify(roles.value))
}

// 权限数据
const permissions = ref([
  { code: 'user.manage', name: '用户管理', description: '创建、编辑、删除用户账户', module: 'system' },
  { code: 'role.manage', name: '角色管理', description: '管理角色和权限配置', module: 'system' },
  { code: 'system.config', name: '系统配置', description: '修改系统参数和配置', module: 'system' },
  { code: 'student.manage', name: '学生管理', description: '管理学生信息和学籍', module: 'student' },
  { code: 'student.view', name: '查看学生', description: '查看学生基本信息', module: 'student' },
  { code: 'grade.manage', name: '成绩管理', description: '管理所有学生成绩', module: 'grade' },
  { code: 'grade.input', name: '成绩录入', description: '录入和修改成绩', module: 'grade' },
  { code: 'grade.view', name: '查看成绩', description: '查看个人成绩信息', module: 'grade' },
  { code: 'course.manage', name: '课程管理', description: '管理课程信息和安排', module: 'course' },
  { code: 'course.view', name: '查看课程', description: '查看课程信息', module: 'course' },
  { code: 'course.select', name: '选课', description: '进行课程选择', module: 'course' },
  { code: 'profile.view', name: '个人资料', description: '查看和修改个人信息', module: 'profile' },
  { code: 'data.export', name: '数据导出', description: '导出系统数据', module: 'system' },
  { code: 'log.view', name: '日志查看', description: '查看系统操作日志', module: 'system' },
  { code: 'report.view', name: '报表查看', description: '查看统计报表', module: 'report' }
])

// 权限检查
const hasManagePermission = computed(() => {
  return permissionManager.hasPermission('role.manage')
})

// 方法
const getPermissionName = (code: string) => {
  const permission = permissions.value.find(p => p.code === code)
  return permission ? permission.name : code
}

const getRolesWithPermission = (permissionCode: string) => {
  return roles.value
    .filter(role => role.permissions.includes(permissionCode))
    .map(role => role.name)
}

const handleAddRole = () => {
  editingRole.value = {
    id: 0,
    name: '',
    description: '',
    userCount: 0,
    permissions: []
  }
  showRoleDialog.value = true
}

const handleEditRole = (role: any) => {
  editingRole.value = {
    ...role,
    permissions: [...role.permissions] // 深拷贝权限数组
  }
  showRoleDialog.value = true
}

const handleDeleteRole = (role: any) => {
  // 防止删除系统核心角色
  if (role.name === '超级管理员' || role.name === '学生') {
    alert('系统核心角色不能删除！')
    return
  }

  if (confirm(`确定要删除角色 "${role.name}" 吗？\n\n此操作将影响 ${role.userCount} 个用户，请谨慎操作！`)) {
    const index = roles.value.findIndex(r => r.id === role.id)
    if (index > -1) {
      roles.value.splice(index, 1)
      saveRoles()
      alert('角色删除成功！')
    }
  }
}

const closeRoleDialog = () => {
  showRoleDialog.value = false
  editingRole.value = {
    id: 0,
    name: '',
    description: '',
    userCount: 0,
    permissions: []
  }
}

const saveRole = () => {
  // 验证必填字段
  if (!editingRole.value.name || !editingRole.value.description) {
    alert('请填写角色名称和描述！')
    return
  }

  // 验证角色名称唯一性（编辑时排除自己）
  const existingRole = roles.value.find(r => r.name === editingRole.value.name && r.id !== editingRole.value.id)
  if (existingRole) {
    alert('角色名称已存在，请使用其他名称！')
    return
  }

  if (editingRole.value.id === 0) {
    // 添加新角色
    const newId = Math.max(...roles.value.map(r => r.id), 0) + 1
    const newRole = {
      ...editingRole.value,
      id: newId,
      userCount: 0
    }
    roles.value.push(newRole)
    alert('角色添加成功！')
  } else {
    // 更新现有角色
    const index = roles.value.findIndex(r => r.id === editingRole.value.id)
    if (index > -1) {
      roles.value[index] = { ...editingRole.value }
      alert('角色信息更新成功！')
    }
  }

  saveRoles()
  closeRoleDialog()
}

const handleRefreshPermissions = () => {
  // 重新初始化权限数据
  initializeRoles()
  alert('权限刷新成功！\n\n已重新加载最新的权限配置')
}

// 页面初始化
onMounted(() => {
  console.log('角色权限管理页面已加载')
  initializeRoles()
})
</script>

<style scoped>
.roles-view {
  padding: 2rem;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 2rem;
}

.page-header h1 {
  font-size: 2rem;
  color: #333;
  margin: 0 0 0.5rem 0;
}

.page-header p {
  color: #666;
  margin: 0;
}

.content-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  overflow: hidden;
}

.card-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e1e5e9;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  color: #333;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.btn-primary {
  background: #1976d2;
  color: white;
}

.btn-secondary {
  background: #f5f7fa;
  color: #666;
  border: 1px solid #e1e5e9;
}

.roles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
  padding: 1.5rem;
}

.role-card {
  border: 1px solid #e1e5e9;
  border-radius: 12px;
  padding: 1.5rem;
  background: #f8f9fa;
}

.role-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.role-header h3 {
  margin: 0;
  color: #333;
}

.role-actions {
  display: flex;
  gap: 0.5rem;
}

.btn-small {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
}

.btn-edit {
  background: #e3f2fd;
  color: #1976d2;
}

.btn-delete {
  background: #ffebee;
  color: #d32f2f;
}

.role-description {
  color: #666;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.role-stats {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.stat-label {
  color: #666;
  font-size: 0.9rem;
}

.stat-value {
  font-weight: 600;
  color: #1976d2;
}

.permissions-preview h4 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 0.9rem;
}

.permission-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.permission-tag {
  background: #e3f2fd;
  color: #1976d2;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
}

.more-permissions {
  background: #f5f7fa;
  color: #666;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
}

.permissions-table {
  overflow-x: auto;
}

.permissions-table table {
  width: 100%;
  border-collapse: collapse;
}

.permissions-table th,
.permissions-table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #e1e5e9;
}

.permissions-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #333;
}

.permissions-table code {
  background: #f5f7fa;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
}

.module-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.module-badge.system {
  background: #e3f2fd;
  color: #1976d2;
}

.module-badge.student {
  background: #e8f5e8;
  color: #388e3c;
}

.module-badge.grade {
  background: #fff3e0;
  color: #f57c00;
}

.module-badge.course {
  background: #f3e5f5;
  color: #7b1fa2;
}

.module-badge.profile {
  background: #fce4ec;
  color: #c2185b;
}

.module-badge.report {
  background: #e0f2f1;
  color: #00695c;
}

.role-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

.role-tag {
  background: #f5f7fa;
  color: #666;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
}

/* 对话框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e1e5e9;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.close-btn:hover {
  background: #f5f5f5;
}

.modal-body {
  padding: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #333;
}

.form-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  box-sizing: border-box;
}

.form-input:focus {
  outline: none;
  border-color: #1976d2;
  box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
}

.modal-footer {
  padding: 1.5rem;
  border-top: 1px solid #e1e5e9;
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

.no-permission {
  color: #999;
  font-size: 0.8rem;
  font-style: italic;
}

.no-permission-text {
  color: #999;
  font-size: 0.9rem;
  font-style: italic;
  padding: 0.75rem 1.5rem;
}

/* 权限选择网格 */
.permissions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 1rem;
}

.permission-item {
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 1rem;
  background: #f8f9fa;
}

.permission-checkbox {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  cursor: pointer;
}

.permission-checkbox input[type="checkbox"] {
  margin: 0;
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.permission-info {
  flex: 1;
}

.permission-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 0.25rem;
}

.permission-desc {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.checkmark {
  display: none;
}
</style>
