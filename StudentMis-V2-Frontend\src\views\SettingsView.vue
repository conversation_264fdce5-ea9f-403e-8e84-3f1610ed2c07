<template>
  <div class="settings-view">
    <div class="page-header">
      <h1>系统设置</h1>
      <p>配置系统参数和功能选项</p>
    </div>

    <div class="settings-container">
      <!-- 基本设置 -->
      <div class="content-card">
        <div class="card-header">
          <h2>基本设置</h2>
        </div>
        <div class="settings-form">
          <div class="form-group">
            <label>系统名称</label>
            <input type="text" v-model="settings.systemName" class="form-input">
          </div>
          <div class="form-group">
            <label>系统版本</label>
            <input type="text" v-model="settings.systemVersion" class="form-input" readonly>
          </div>
          <div class="form-group">
            <label>系统描述</label>
            <textarea v-model="settings.systemDescription" class="form-textarea"></textarea>
          </div>
          <div class="form-group">
            <label>管理员邮箱</label>
            <input type="email" v-model="settings.adminEmail" class="form-input">
          </div>
        </div>
      </div>

      <!-- 安全设置 -->
      <div class="content-card">
        <div class="card-header">
          <h2>安全设置</h2>
        </div>
        <div class="settings-form">
          <div class="form-group">
            <label>密码最小长度</label>
            <input type="number" v-model="settings.minPasswordLength" class="form-input" min="6" max="20">
          </div>
          <div class="form-group">
            <label>登录失败锁定次数</label>
            <input type="number" v-model="settings.maxLoginAttempts" class="form-input" min="3" max="10">
          </div>
          <div class="form-group">
            <label>会话超时时间（分钟）</label>
            <input type="number" v-model="settings.sessionTimeout" class="form-input" min="30" max="480">
          </div>
          <div class="form-group checkbox-group">
            <label class="checkbox-label">
              <input type="checkbox" v-model="settings.enableTwoFactor">
              <span class="checkmark"></span>
              启用双因素认证
            </label>
          </div>
          <div class="form-group checkbox-group">
            <label class="checkbox-label">
              <input type="checkbox" v-model="settings.forcePasswordChange">
              <span class="checkmark"></span>
              强制定期更改密码
            </label>
          </div>
        </div>
      </div>

      <!-- 邮件设置 -->
      <div class="content-card">
        <div class="card-header">
          <h2>邮件设置</h2>
        </div>
        <div class="settings-form">
          <div class="form-group">
            <label>SMTP服务器</label>
            <input type="text" v-model="settings.smtpHost" class="form-input">
          </div>
          <div class="form-group">
            <label>SMTP端口</label>
            <input type="number" v-model="settings.smtpPort" class="form-input">
          </div>
          <div class="form-group">
            <label>发件人邮箱</label>
            <input type="email" v-model="settings.senderEmail" class="form-input">
          </div>
          <div class="form-group">
            <label>邮箱密码</label>
            <input type="password" v-model="settings.emailPassword" class="form-input">
          </div>
          <div class="form-group checkbox-group">
            <label class="checkbox-label">
              <input type="checkbox" v-model="settings.enableSSL">
              <span class="checkmark"></span>
              启用SSL加密
            </label>
          </div>
          <div class="form-actions">
            <button class="btn btn-secondary" @click="testEmailConnection">测试邮件连接</button>
          </div>
        </div>
      </div>

      <!-- 数据库设置 -->
      <div class="content-card">
        <div class="card-header">
          <h2>数据库设置</h2>
        </div>
        <div class="settings-form">
          <div class="form-group">
            <label>数据库类型</label>
            <select v-model="settings.dbType" class="form-select">
              <option value="mysql">MySQL</option>
              <option value="postgresql">PostgreSQL</option>
              <option value="oracle">Oracle</option>
            </select>
          </div>
          <div class="form-group">
            <label>数据库主机</label>
            <input type="text" v-model="settings.dbHost" class="form-input">
          </div>
          <div class="form-group">
            <label>数据库端口</label>
            <input type="number" v-model="settings.dbPort" class="form-input">
          </div>
          <div class="form-group">
            <label>数据库名称</label>
            <input type="text" v-model="settings.dbName" class="form-input">
          </div>
          <div class="form-group">
            <label>连接池大小</label>
            <input type="number" v-model="settings.dbPoolSize" class="form-input" min="5" max="100">
          </div>
          <div class="form-actions">
            <button class="btn btn-secondary" @click="testDatabaseConnection">测试数据库连接</button>
          </div>
        </div>
      </div>

      <!-- 系统维护 -->
      <div class="content-card">
        <div class="card-header">
          <h2>系统维护</h2>
        </div>
        <div class="settings-form">
          <div class="maintenance-actions">
            <div class="action-item">
              <div class="action-info">
                <h3>清理系统日志</h3>
                <p>清理30天前的系统操作日志</p>
              </div>
              <button class="btn btn-warning" @click="cleanSystemLogs">清理日志</button>
            </div>
            <div class="action-item">
              <div class="action-info">
                <h3>数据库备份</h3>
                <p>创建当前数据库的完整备份</p>
              </div>
              <button class="btn btn-primary" @click="backupDatabase">立即备份</button>
            </div>
            <div class="action-item">
              <div class="action-info">
                <h3>缓存清理</h3>
                <p>清理Redis缓存和临时文件</p>
              </div>
              <button class="btn btn-secondary" @click="clearCache">清理缓存</button>
            </div>
            <div class="action-item">
              <div class="action-info">
                <h3>系统重启</h3>
                <p>重启应用服务器（谨慎操作）</p>
              </div>
              <button class="btn btn-danger" @click="restartSystem">重启系统</button>
            </div>
          </div>
        </div>
      </div>

      <!-- 保存按钮 -->
      <div class="save-actions">
        <button class="btn btn-primary btn-large" @click="saveSettings">
          <span class="btn-icon">💾</span>
          保存设置
        </button>
        <button class="btn btn-secondary btn-large" @click="resetSettings">
          <span class="btn-icon">🔄</span>
          重置设置
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'

// 系统设置数据
const settings = reactive({
  // 基本设置
  systemName: '江苏海洋大学学生成绩管理系统',
  systemVersion: 'V2.0.0',
  systemDescription: '现代化的学生成绩管理系统，为江苏海洋大学提供全面的学生信息管理解决方案。',
  adminEmail: '<EMAIL>',
  
  // 安全设置
  minPasswordLength: 8,
  maxLoginAttempts: 5,
  sessionTimeout: 120,
  enableTwoFactor: false,
  forcePasswordChange: true,
  
  // 邮件设置
  smtpHost: 'smtp.jou.edu.cn',
  smtpPort: 587,
  senderEmail: '<EMAIL>',
  emailPassword: '',
  enableSSL: true,
  
  // 数据库设置
  dbType: 'mysql',
  dbHost: 'localhost',
  dbPort: 3306,
  dbName: 'studentmis_v2',
  dbPoolSize: 20
})

// 方法
const saveSettings = () => {
  // 模拟保存设置
  alert('设置保存成功！\n\n系统配置已更新，部分设置需要重启后生效。')
}

const resetSettings = () => {
  if (confirm('确定要重置所有设置到默认值吗？')) {
    alert('设置已重置到默认值！')
  }
}

const testEmailConnection = () => {
  alert('邮件连接测试\n\n正在测试SMTP连接...\n✅ 连接成功！邮件服务配置正确。')
}

const testDatabaseConnection = () => {
  alert('数据库连接测试\n\n正在测试数据库连接...\n✅ 连接成功！数据库配置正确。')
}

const cleanSystemLogs = () => {
  if (confirm('确定要清理30天前的系统日志吗？此操作不可恢复。')) {
    alert('系统日志清理完成！\n\n已清理30天前的日志记录，释放存储空间。')
  }
}

const backupDatabase = () => {
  if (confirm('确定要创建数据库备份吗？此过程可能需要几分钟时间。')) {
    alert('数据库备份已开始！\n\n备份文件将保存到：/backup/studentmis_v2_20240619.sql')
  }
}

const clearCache = () => {
  if (confirm('确定要清理系统缓存吗？')) {
    alert('缓存清理完成！\n\n已清理Redis缓存和临时文件。')
  }
}

const restartSystem = () => {
  if (confirm('确定要重启系统吗？这将中断所有用户的连接。')) {
    alert('系统重启指令已发送！\n\n系统将在30秒后重启，请通知所有用户保存工作。')
  }
}
</script>

<style scoped>
.settings-view {
  padding: 2rem;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 2rem;
}

.page-header h1 {
  font-size: 2rem;
  color: #333;
  margin: 0 0 0.5rem 0;
}

.page-header p {
  color: #666;
  margin: 0;
}

.settings-container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.content-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e1e5e9;
  background: #f8f9fa;
}

.card-header h2 {
  margin: 0;
  color: #333;
  font-size: 1.25rem;
}

.settings-form {
  padding: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #333;
}

.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: #1976d2;
}

.form-textarea {
  min-height: 100px;
  resize: vertical;
}

.checkbox-group {
  display: flex;
  align-items: center;
}

.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  margin-bottom: 0;
}

.checkbox-label input[type="checkbox"] {
  margin-right: 0.75rem;
}

.form-actions {
  margin-top: 1rem;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.btn-primary {
  background: #1976d2;
  color: white;
}

.btn-secondary {
  background: #f5f7fa;
  color: #666;
  border: 1px solid #e1e5e9;
}

.btn-warning {
  background: #ff9800;
  color: white;
}

.btn-danger {
  background: #f44336;
  color: white;
}

.btn-large {
  padding: 1rem 2rem;
  font-size: 1.1rem;
}

.maintenance-actions {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.action-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  background: #f8f9fa;
}

.action-info h3 {
  margin: 0 0 0.25rem 0;
  color: #333;
  font-size: 1rem;
}

.action-info p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.save-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  padding: 2rem 0;
}

@media (max-width: 768px) {
  .action-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .save-actions {
    flex-direction: column;
  }
}
</style>
