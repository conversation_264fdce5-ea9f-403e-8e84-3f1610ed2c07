score<%@ page import="com.ntvu.studentmis.pager.PagerHelper" %>
<%@ page import="com.ntvu.studentmis.entity.Score" %>
<%@ page import="com.ntvu.studentmis.util.WebTools" %>
<%@ page import="com.ntvu.studentmis.db.DBScore" %>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <jsp:include page="../../include/header_css.jsp" flush="true"/>
    <title>学生成绩列表</title>
</head>
<body class="hold-transition sidebar-mini layout-fixed">
<div class="wrapper">
    <%@include file="../../teacher/header_nav.jsp"%>
    <%@include file="../../teacher/left_menu.jsp"%>
    <%
        request.setCharacterEncoding("utf-8");
        String stu_num = request.getParameter("stu_num");
        String stu_name = request.getParameter("stu_name");
        PagerHelper<Score> pager = new PagerHelper(request);
        if (stu_num != null && !stu_num.trim().equals("")) {
            pager.getQueryParams().put("stu_num", stu_num);
        }
        if (stu_name != null && !stu_name.trim().equals("")) {
            pager.getQueryParams().put("stu_name", stu_name);
        }
        new DBScore().getList(pager);
        double sumScore=pager.getSumScore();
        double avgScore=pager.getAvgScore();
    %>
    <!-- Content Wrapper. Contains page content -->
    <form id="form1" name="form1" method="post"
          action="<%= request.getContextPath() + "/teacher/Score/TeacherScoreServlet?action=add"%>">
        <div class="content-wrapper">
            <section class="content">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <div class="row">

                                        <div class="col-lg-2">学号：<input type="text" name="stu_num"
                                                                          class="form-control" value="<%= WebTools.parseNullorEmpty(stu_num)%>"></div>
                                        <div class="col-lg-2">姓名：<input type="text" name="stu_name"
                                                                          class="form-control" value="<%= WebTools.parseNullorEmpty(stu_name)%>"></div>
                                        <div class="col-lg-2">
                                            <input type="button" class="btn btn-block btn-primary btn-xs;form-control"
                                                    style="margin-top: 25px;" name="btnFind" value="查询">
                                        </div>
                                        <div class="col-lg-2">
                                            <input type="button" class="btn btn-block btn-danger btn-xs;form-control"
                                                    style="margin-top: 25px;" name="btnDelSel" value="删除所勾选的">
                                        </div>
                                        <div class="col-lg-2">此学生总成绩：<input type="text" class="form-control" disabled="disabled" value="<%= sumScore==0.0?"":sumScore%>" placeholder="点击查询后显示"></div>
                                        <div class="col-lg-2">此学生平均分：<input type="text" class="form-control" disabled="disabled" value="<%= avgScore==0.0?"":avgScore%>" placeholder="点击查询后显示"></div>
                                    </div>
                                    <!-- /.card-header -->
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-sm-12">
                                                <table id="example1"
                                                       class="table table-bordered table-striped dataTable dtr-inline"
                                                       aria-describedby="example1_info">
                                                    <thead>
                                                    <tr>
                                                        <th class="sorting sorting_asc" tabindex="0"
                                                            aria-controls="example1" rowspan="1" colspan="1"
                                                            aria-sort="ascending"
                                                            aria-label="Rendering engine: activate to sort column descending">
                                                            <input type="checkbox" class="form-check" title="全选" name="checkAll">
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="Platform(s): activate to sort column ascending">
                                                            序号
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="Browser: activate to sort column ascending">
                                                            学号
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="Platform(s): activate to sort column ascending">
                                                            姓名
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="CSS grade: activate to sort column ascending">
                                                            班级
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="CSS grade: activate to sort column ascending">
                                                            科目
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="CSS grade: activate to sort column ascending">
                                                            成绩
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="CSS grade: activate to sort column ascending">
                                                            专业
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="CSS grade: activate to sort column ascending">
                                                            操作
                                                        </th>
                                                    </tr>
                                                    </thead>
                                                    <tbody>
                                                    <%
                                                        int index = 0;
                                                        for (Score score : pager.getData()) {
                                                            index++;
                                                    %>
                                                    <tr class="<%= index % 2 == 1 ? "odd" : "even"%>">
                                                        <td><input type="checkbox" class="form-check" name="checkItem" value="<%= score.getScore_id()%>"></td>
                                                        <td><%= index%></td>
                                                        <td class="dtr-control sorting_1"
                                                            tabindex="0"><%= score.getStu_num()%>
                                                        </td>
                                                        <td><%= score.getStu_name()%>
                                                        </td>
                                                        <td><%= score.getStu_class()%>
                                                        </td>
                                                        <td><%= score.getCourse_name()%>
                                                        </td>
                                                        <td><%= score.getScore_grade()%></td>
                                                        <td><%= score.getMajor()%></td>
                                                        <td style="width: 200px">
                                                            <div class="row">
                                                                <div class="col-6">
                                                                    <button type="button"
                                                                            class="btn btn-block btn-primary btn-xs"
                                                                            style="width: 80px" onclick="window.location.href = '<%= contextPath +"/teacher/score/edit.jsp?id=" + score.getScore_id()%>';">编辑
                                                                    </button>
                                                                </div>
                                                                <div class="col-6">
                                                                    <button type="button"
                                                                            class="btn btn-block btn-danger btn-xs"
                                                                            style="width: 80px" onclick="if(confirm('当前操作不可恢复，确认删除吗？')){
                                                                            window.location.href='<%= contextPath +"/teacher/Score/TeacherScoreServlet?action=delete&id=" + score.getScore_id()%>';}">删除
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    <%
                                                        }
                                                    %>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-5">
                                                <div class="dataTables_info" id="example1_info" role="status"
                                                     aria-live="polite">每页显示10条记录
                                                </div>
                                            </div>
                                            <div class="col-sm-12 col-md-7">
                                                <%@ include file="../../include/pager_footer.jsp"%>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- /.card-body -->
                            </div>
                            <!-- /.card -->
                        </div>
                        <!-- /.col -->
                    </div>
                    <!-- /.row -->
                </div>
                <!-- /.container-fluid -->
            </section>
        </div>
    </form>
</div>
<%@include file="../../include/foot_js.jsp"%>
<script>
    $(function (){
        //绑定勾选框按钮事件
        $('input[name=checkAll]').bind('change',function (){
            console.log('checkAll');
            let checked=$(this).prop('checked');
            //更改表格中所有chkItem
            $('input[name=checkItem]').each(function (){
                console.log('checkItem');
                $(this).prop('checked',checked);
            });
        });
        //绑定删除所有按钮事件
        $('input[name=btnDelSel]').bind('click',function (){
            let ids='';
            $('input[name=checkItem]').each(function (){
                if( $(this).prop('checked')===true)
                {
                    ids+=$(this).val()+',';
                }
            });
            if(ids.length>0)
            {
                if(confirm('当前操作不可恢复,确认要删除吗?'))
                {
                    console.log(ids);
                    window.location.href='<%=contextPath+"/teacher/Score/TeacherScoreServlet?action=deleteSelected&ids="%>'+ids;
                }
            }else {
                alert('请选择待删除项');
            }

        });
        $('input[name=btnFind]').bind('click',function (){
            $('#form1').attr('action','<%= request.getContextPath() + "/teacher/score/list.jsp"%>');
            $(`#form1`).submit();
        });
    });
    /**
     * 跳转到指定的页
     * @param toPageIndex
     */
        //本页地址
    let pageListUrl = '/teacher/score/list.jsp';
    function doPager(toPageIndex)
    {
        $('#form1').attr('action','<%= request.getContextPath() %>' + pageListUrl + '?pageIndex=' + toPageIndex);
        $('#form1').submit();
    }
</script>

</body>
</html>
