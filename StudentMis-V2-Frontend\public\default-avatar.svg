<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
  <!-- 渐变定义 -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="avatarGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFFFF;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#F5F5F5;stop-opacity:0.9" />
    </linearGradient>
  </defs>

  <!-- 背景圆 -->
  <circle cx="50" cy="50" r="50" fill="url(#bgGradient)"/>

  <!-- 头部 -->
  <circle cx="50" cy="35" r="16" fill="url(#avatarGradient)"/>

  <!-- 身体 -->
  <path d="M20 85c0-16.569 13.431-30 30-30s30 13.431 30 30v15H20v-15z" fill="url(#avatarGradient)"/>

  <!-- 装饰圆环 -->
  <circle cx="50" cy="50" r="48" fill="none" stroke="#FFFFFF" stroke-width="2" opacity="0.3"/>
  <circle cx="50" cy="50" r="45" fill="none" stroke="#FFFFFF" stroke-width="1" opacity="0.2"/>

  <!-- 面部特征 -->
  <circle cx="45" cy="32" r="2.5" fill="#667eea" opacity="0.8"/>
  <circle cx="55" cy="32" r="2.5" fill="#667eea" opacity="0.8"/>
  <path d="M 44 41 Q 50 44 56 41" stroke="#667eea" stroke-width="2.5" fill="none" stroke-linecap="round" opacity="0.8"/>

  <!-- 高光效果 -->
  <circle cx="45" cy="30" r="1" fill="white" opacity="0.9"/>
  <circle cx="55" cy="30" r="1" fill="white" opacity="0.9"/>

  <!-- 装饰点 -->
  <circle cx="30" cy="25" r="2" fill="#FFFFFF" opacity="0.4"/>
  <circle cx="70" cy="30" r="1.5" fill="#FFFFFF" opacity="0.3"/>
  <circle cx="25" cy="70" r="1" fill="#FFFFFF" opacity="0.5"/>
  <circle cx="75" cy="75" r="1.5" fill="#FFFFFF" opacity="0.4"/>
</svg>
