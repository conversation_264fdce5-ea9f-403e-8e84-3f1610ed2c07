﻿文档编号：**JOU-SECD-JS4241-03T-06**

版 本 号：**V1.0**

<a name="_toc360453642"></a><a name="_toc327852950"></a><a name="_toc296255960"></a><a name="_toc359946601"></a><a name="_toc296723289"></a><a name="_toc296256683"></a><a name="_toc297035845"></a><a name="_toc296423708"></a><a name="_toc422865759"></a><a name="_toc359946392"></a>     <a name="_toc200979680"></a>基于JavaWeb的学生成绩管理系统项目软件测试计划

|**项目名称**|`        `**基于JavaWeb的学生成绩管理系统**                |
| - | - |
|**项目负责人**|`                     `**王梓齐**                         |
|**项目开发单位**   ||

|**江苏      江苏海洋大学应用技术学院计算机4241第3项目组**  |
| - |

|||
| - | - |
|**小组成员**|`  `**张庆祥、蔡家璇、刘川东、沈磊、程学峰、徐浩翔**                 |







**2020年6月16日**

<a name="_toc296723290"></a><a name="_toc297035846"></a><a name="_toc327852951"></a><a name="_toc296255961"></a><a name="_toc296256684"></a><a name="_toc360453643"></a><a name="_toc359946602"></a><a name="_toc296423709"></a><a name="_toc422865760"></a><a name="_toc359946393"></a>软件工程课程设计项目组任务分派单（组长用）

<a name="_toc200979681"></a><a name="_toc517672474"></a><a name="_toc517770659"></a>班级：  计算机4241  组别：   3   组长姓名：  王梓齐     时间：2025年6月16日

项目名称：       **基于JavaWeb的学生成绩管理系统**       阶段名称：  软件测试计划      

|序号|学号|姓名|任务名称|具体任务内容|完成标准|起止日期|验收成绩|
| :-: | :-: | :-: | :-: | :-: | :-: | :-: | :-: |
|1|2024140518|王梓齐|软件测试计划统筹|负责测试计划整体架构设计与任务协调|测试计划完整规范，符合项目要求|2025\.6.1-2025.6.19|80|
|2|2024140525|张庆祥|功能测试设计|设计模块功能测试用例与执行方案|测试用例覆盖主要功能点|2025\.6.1-2025.6.19|80|
|3|2024140485|蔡家璇|接口测试设计|设计模块间接口测试方案与验证方法|接口测试覆盖率达 90% 以上|2025\.6.1-2025.6.19|80|
|4|2024140499|刘川东|数据测试设计|设计数据持久化测试方案与数据校验规则|数据完整性与一致性测试通过|2025\.6.1-2025.6.19|80|
|5|2024140520|徐浩翔|性能测试设计|设计系统性能测试场景与指标验收标准|性能测试结果符合预期要求|2025\.6.1-2025.6.19|80|
|6|2024140487|程学峰|测试实施准备|准备各类测试场景所需的输入与预期数据|测试数据完整、有效|2025\.6.1-2025.6.19|80|
|7|2024140508|沈磊|评价准则及整理测试报告|评价准则及汇总测试结果，生成测试报告|报告格式规范，内容完整|2025\.6.1-2025.6.19|80|

1. 本表由组长为其组员每次上机实践分派任务使用，应认真填写相关任务名称、内容、完成标准等信息；

2、本表在每次任务完成后，由组长按照完成标准验收，并给出每个组员成绩评定（每人平均70分制），除组长保留一份外，应及时上报至对分易。

注意：为组员分配颜色标志在上表里
**


目录

[1引言	1](#_toc22954)

[ 1．1编写目的	1](#_toc16323)

[ 1．2背景	1](#_toc12285)

[ 1．3定义	1](#_toc2028)

[1．4参考资料	1](#_toc5559)

[2测试计划	1](#_toc15677)

[ 2．1软件说明	2](#_toc24297)

[2.2功能测试	2](#_toc20571)

[2.3接口测试	3](#_toc23316)

[2.4 数据测试	4](#_toc13933)

[2.4.1 测试目的	4](#_toc10180)

[2.4.2 测试范围	5](#_toc10186)

[2.4.3 测试指标与验收标准	5](#_toc20744)

[2.4.4 测试工具与方法	5](#_toc10508)

[2.5性能测试	5](#_toc30386)

[3.测试设计说明	6](#_toc23003)

[3.1功能测试设计	6](#_toc9343)

[ 3.1.1测试目的	6](#_toc7914)

[3.1.2测试范围	6](#_toc15510)

[3.1.3进度安排	6](#_toc10489)

[3.1.4测试条件	6](#_toc3850)

[ 3.1.5测试资料 	6](#_toc10463)

[3.1.6功能测试用例表	7](#_toc20019)

[3.2 接口测试设计	8](#_toc16617)

[3.2.1 测试目的	8](#_toc20262)

[3.2.2 测试范围	8](#_toc1541)

[3.2.3接口测试用例表	8](#_toc16018)

[3.2.4测试工具	9](#_toc14241)

[3.2.5测试流程	9](#_toc30145)

[3.3 数据测试设计	9](#_toc19236)

[3.3.1 测试目的	9](#_toc14499)

[3.3.2 测试范围	10](#_toc4190)

[3.3.3 进度安排	10](#_toc11514)

[3.3.4 测试条件	10](#_toc7790)

[3.3.5 测试资料	10](#_toc7206)

[3.3.6 测试用例	11](#_toc29937)

[3.4性能测试设计	12](#_toc23593)

[3.4.1 测试目的	12](#_toc23405)

[3.4.2 测试指标	12](#_toc25036)

[3.4.3测试工具	12](#_toc6489)

[3.4.4测试流程	12](#_toc19743)

[3.4.5 注意事项	12](#_toc13113)

[3.4.6性能测试场景设计图	12](#_toc2714)

[4. 测试实施	13](#_toc32151)

[ 4.1测试数据准备	13](#_toc13999)

[4.1.1数据准备目的	13](#_toc7406)

[4.1.2 测试数据类型	13](#_toc7823)

[4.1.3 测试数据内容	13](#_toc446)

[4.1.4 数据生成方法	14](#_toc20589)

[4.1.5 数据验证方法	14](#_toc10845)

[4.1.6数据存储与管理	14](#_toc32390)

[4.1.7 数据准备进度安排	14](#_toc74)

[4.2硬件环境	14](#_toc21429)

[4.3软件环境	15](#_toc5378)

[4.4测试步骤	15](#_toc3422)

[4.4.1功能测试步骤	15](#_toc25266)

[4.4.2接口测试步骤	15](#_toc3567)

[4.4.3数据测试步骤	15](#_toc5943)

[4.4.4性能测试步骤	15](#_toc29783)

[5. 评价准则	16](#_toc25665)

[5.1 范围	16](#_toc26642)

[5.1.1功能测试	16](#_toc26022)

[5.1.2接口测试	16](#_toc1727)

[5.1.3数据测试	16](#_toc30497)

[5.1.5性能测试	16](#_toc1895)

[5.2 数据整理	16](#_toc23812)

[5.2.1功能测试和接口测试	16](#_toc2255)

[5.2.2数据测试	16](#_toc32697)

[5.2.3性能测试	16](#_toc27536)

[5.3 尺度	16](#_toc14333)

[5.3.1功能测试	16](#_toc24016)

[5.3.2接口测试	16](#_toc23097)

[5.3.3数据测试	17](#_toc11720)



















<a name="_toc22954"></a>1引言 

<a name="_toc16323"></a>**
1．1编写目的 


本测试计划旨在为 “基于 JavaWeb 的学生成绩管理系统” 的测试工作提供全面、详细的指导，明确测试的范围、方法、流程和标准，确保测试工作有序、高效地进行，以验证系统是否满足需求规格说明书和详细设计说明书的各项要求。本计划的预期读者包括测试人员、开发人员、项目管理人员和用户代表。

<a name="_toc12285"></a>
1．2背景 


a.待测试软件系统的名称：基于 JavaWeb 的学生成绩管理系统

b.项目的任务提出者：江苏海洋大学计算机工程学院

c.开发者：江苏海洋大学应用技术学院计算机 4241 第 3 项目组

d.用户：江苏海洋大学系统管理员、教师及学生

e.运行环境：部署于学校中心机房服务器，通过校园网提供服务

<a name="_toc2028"></a>
1．3定义 


**软件测试**：使用人工或自动手段来运行或测定某个系统的过程，其目的在于检验它是否满足规定的需求或是弄清预期结果与实际结果之间的差别。

**功能测试**：对系统的功能点进行测试，验证功能是否符合需求规格说明书的要求。

**接口测试**：测试系统各模块之间的接口是否正确、稳定，数据传输是否正确。

**数据测试：**检验系统数据在输入、处理、存储和输出全流程中的准确性、完整性、一致性与安全性。包括验证数据格式、取值范围是否合规，检查计算逻辑与业务规则执行情况，确保数据存储可靠、输出正确，并测试数据备份恢复、权限控制等安全功能。

**性能测试**：测试系统在不同负载下的性能表现，包括响应时间、吞吐量、资源利用率等指标。

**测试用例**：为特定的测试目标而设计的一组测试输入、执行条件和预期结果。

<a name="_toc5559"></a>1．4参考资料 


a.《JSP+Servlet+Tomcat 应用开发从零开始学 (第二版)》

b.《软件测试技术与实践》

c.《基于 JavaWeb 技术的学生成绩管理系统需求规格说明书》

d.《基于 JavaWeb 的学生成绩管理系统详细设计说明书》

e.Java 开发规范（阿里巴巴 Java 开发手册）

<a name="_toc15677"></a>2测试计划 

<a name="_toc24297"></a>**
2．1软件说明 
**

本系统采用 JavaWeb 技术开发，基于 B/S 架构，主要实现学生成绩管理功能，包括学生信息管理、成绩录入、成绩查询、成绩统计等模块。系统的功能、输入和输出等质量指标如下表所示：

|**功能模块**|**功能描述**|**输入**|**输出**|**质量指标**|
| - | - | - | - | - |
|学生信息管理|实现学生信息的添加、删除、修改和查询|学生基本信息（学号、姓名、性别、班级等）|学生信息列表、操作结果提示|数据准确性、完整性|
|成绩录入|实现学生成绩的录入和修改|课程编号、学生学号、成绩|成绩录入结果、错误提示|成绩准确性、录入效率|
|成绩查询|实现学生成绩的查询|查询条件（学号、姓名、课程等）|成绩查询结果|查询准确性、响应时间|
|成绩统计|实现学生成绩的统计分析|统计条件（班级、课程、学期等）|统计报表、图表|统计准确性、可视化效果|

<a name="_toc20571"></a>2.2功能测试

**学生信息管理模块**

添加功能：验证系统能否正确处理合法 / 非法学生信息（如重复学号、缺失必填字段），并在数据库中准确存储。

删除功能：测试单条 / 批量删除学生信息时，系统是否同步更新数据库，且关联的成绩数据是否按规则处理（如级联删除或保留历史记录）。

修改功能：验证修改学生核心信息（如学号）时的事务一致性，以及非核心信息（如联系方式）的实时更新能力。

查询功能：测试单条件（学号 / 姓名）、多条件组合（班级 + 性别）及模糊查询（姓名关键字）的准确性，包括分页和排序功能的正确性。

**成绩录入模块**

成绩录入：验证不同成绩类型（整数 / 小数）、边界值（0/100）及非法值（-5/105）的处理逻辑，确保成绩范围校验生效。

成绩修改：测试已录入成绩的更新规则，如是否允许跨学期修改、是否能够触发历史成绩版本的记录。

**成绩查询模块**

单条件查询：验证按学号、课程编号等单一条件查询时，结果集的完整性和准确性。

多条件查询：测试组合条件（如 “计算机 4241 班 + 软件工程课程 + 2024 学年”）的逻辑正确性，确保交集数据准确返回。

**成绩统计模块**

基础统计：验证平均分、最高分、最低分等基础指标的计算准确性。

进阶分析：测试班级排名、成绩分布（优秀 / 良好 / 及格 / 不及格占比）及趋势分析（学期成绩对比）的可视化图表生成能力。

<a name="_toc23316"></a>2.3接口测试

学生信息管理接口

**/student/add**

**测试 JSON 参数格式校验**

学号字段：验证长度为 10 位数字（正则表达式：^\d{10}$），测试边界值（9 位、10 位、11 位）

姓名字段：验证非空（trim 后长度 > 0）测试特殊字符（如@#$）,全空格输入

出生日期：验证格式为YYYY-MM-DD，测试非法日期（如 2025-02-30）

**冲突处理机制**

插入已存在的学号，预期返回 HTTP 409 状态码和错误信息：{"code":409,"message":"学号已存在"}

并发插入相同学号，使用 JMeter 模拟 100 并发请求，验证数据库唯一索引约束生效

**/student/delete**

**对等性验证**

首次删除有效 ID，预期返回 HTTP 200 和{"success":true}

重复删除同一 ID，连续调用 10 次，每次均返回相同结果

使用 Postman Collection Runner 执行批量测试

**外键约束测试**

删除关联成绩记录的学生，预期返回 HTTP 400 和错误信息：{"code":400,"message":"存在关联成绩，无法删除"}

先删除关联成绩再删学生，验证操作成功

**/student/query**

**分页与排序测试**

验证 pageSize 默认值 = 20，最大值 = 100，超限返回 HTTP 400

测试排序字段注入（如order by id;drop table），预期返回 HTTP 403

使用 SQL Profiler 监控实际执行的 SQL 语句

**成绩管理接口**

**/score/add**

**事务性验证**

使用测试数据：

json

[

`  `{"studentId":1001,"courseId":2001,"score":85},

`  `{"studentId":1002,"courseId":2001,"score":92},

`  `{"studentId":1001,"courseId":2002,"score":78}

]

中断网络模拟部分提交，验证数据库回滚

查询成绩表确认无脏数据

**关联校验**

插入不存在的 studentId/courseId，预期返回 HTTP 404

成绩范围验证（0-100），测试边界值（-1、0、100、101）

**/score/query**

**复杂查询测试**

构造测试数据：

|**学生 ID**|**课程 ID**|**成绩**|
| - | - | - |
|1001|2001|58|
|1001|2002|45|
|1002|2001|88|

执行查询：挂科次数>=2，预期返回学生 1001

**性能测试**

在 100 万条成绩数据下，验证响应时间≤3 秒

使用 Explain 分析查询执行计划，确保索引生效

**统计分析接口**

**/statistics/grade**

**大数据量分页测试**

预置 10 万条学生 + 100 万条成绩数据

测试参数组合：

plaintext

pageNum=1&pageSize=100  

pageNum=100&pageSize=50  

pageNum=1000&pageSize=10  

验证总记录数、总页数计算正确

**跨模块数据聚合**

验证返回字段：

json

{

`  `"studentId": 1001,

`  `"name": "张三",

`  `"avgScore": 82.5,

`  `"ranking": 12,

`  `"courseCount": 4

}

使用数据对比工具（如 DBeaver）验证聚合结果准确性

<a name="_toc13933"></a>2.4 数据测试

<a name="_toc10180"></a>2.4.1 测试目的

验证系统数据在存储、传输、处理过程中的完整性、准确性、一致性及合规性，确保：

关键数据（学生信息、成绩记录等）无丢失、无损坏；

数据约束（主键、外键、唯一索引等）有效生效；

跨模块数据关联（如学生信息与成绩记录的映射）准确无误；

历史数据更新或迁移时的一致性维护。

<a name="_toc10186"></a>2.4.2 测试范围

**数据完整性测试**

学生信息表：验证学号、姓名、班级等必填字段非空，批量导入时无记录遗漏；

成绩表：检查成绩、课程编号、学生学号等字段的完整性，补考 / 重修数据同步记录；

关联表：验证学生 - 成绩关联表（如 student\_score）的外键约束，避免孤立数据。

**数据准确性测试**

数值校验：成绩范围（0-100）、学分绩点计算（如 85 分对应 3.5 绩点）的准确性；

逻辑校验：学生入学年份与年级的匹配性（如 2024 级学生入学年份为 2024）；

计算校验：总分、平均分等统计值与实际数据的一致性（如 5 门课成绩之和等于总分）。

**数据一致性测试**

字段一致性：学生性别在个人信息页、成绩页、数据库存储中均为统一表述（如 “男”“女”）；

跨模块一致性：修改学生班级后，成绩统计模块自动同步更新该生所属班级；

事务一致性：批量导入成绩时，确保 “全量成功” 或 “全量回滚”，无部分提交现象。

**数据合规性测试**

隐私保护：学生联系方式、身份证号等敏感信息是否加密存储；

权限控制：不同角色（管理员 / 教师 / 学生）对数据的访问范围是否符合设计要求（如学生仅能查看本人成绩）。

<a name="_toc20744"></a>2.4.3 测试指标与验收标准

**数据完整性**：必填字段缺失率为 0，批量导入记录数与源文件一致（误差≤0.1%）；

**数据准确性**：数值计算错误率为 0，逻辑校验失败率≤0.5%；

**数据一致性**：跨模块数据不一致问题修复率 100%；

**数据合规性**：敏感信息未加密问题清零，权限越界访问次数为 0。

<a name="_toc10508"></a>2.4.4 测试工具与方法

**工具**：

数据生成：DataFactory（批量生成测试数据）；

数据库校验：SQLyog（执行 SQL 查询验证数据一致性）；

加密验证：Jasypt（校验敏感信息加密算法）。

<a name="_toc30386"></a>2.5性能测试

**正常负载场景**

**模拟场景**：运用专业性能测试工具（如 JMeter、LoadRunner），模拟 50 个虚拟用户同时执行 “学生信息查询”“单课程成绩浏览” 等日常操作。在测试过程中，设定每个用户的操作间隔随机分布在 1 - 3 秒之间，模拟真实用户使用系统时的操作节奏。用户操作路径覆盖不同年级、不同班级的学生信息与课程成绩查询，确保测试数据的多样性。

**指标要求**：要求 90% 的请求响应时间控制在≤2 秒，这是为了保证用户在正常使用系统时，能获得流畅的操作体验，避免因响应过慢导致用户流失。服务器 CPU 利用率≤60%，内存占用≤50%，该指标旨在确保服务器在正常负载下，仍有足够的资源应对突发请求，维持系统稳定运行。同时，在测试过程中，实时监控服务器的日志文件，及时记录可能出现的错误。

**高并发负载场景**

**模拟场景**：模拟 200 个用户同时进行 “成绩批量录入”“学生信息批量修改” 等高 IO 操作。测试时，通过脚本控制用户操作的并发度，逐步增加并发用户数量，直至达到 200 个，观察系统在高并发压力下的表现。为了更贴近实际业务场景，批量录入的成绩数据包含不同科目、不同分数段，批量修改操作涉及学生姓名、联系方式等多种信息字段。​

**指标要求**：系统吞吐量需≥150 请求 / 秒，以满足大量用户同时操作时系统的数据处理能力需求。数据库连接池最大占用率≤80%，防止数据库连接池被耗尽，导致系统无法正常访问数据库。同时，严格要求测试过程中无请求超时或服务崩溃现象，确保系统在高并发场景下的可靠性。测试过程中，使用数据库性能监控工具，实时监测数据库的事务处理情况、锁等待时间等关键指标。​

大数据量场景

**模拟场景**：预先在数据库中填充 10 万条学生记录、100 万条成绩记录，数据涵盖不同学年、不同专业的学生信息与成绩情况。执行 “全年级成绩排名”“跨学期成绩趋势分析” 等复杂操作时，通过编写复杂的 SQL 查询语句，结合聚合函数、多表关联等操作，模拟实际业务中的大数据量分析场景。​

**指标要求**：复杂统计查询响应时间≤10 秒，以保证管理人员能快速获取数据分析结果，提高工作效率。要求内存溢出错误率为 0，确保系统在处理大数据量时的稳定性。磁盘 IO 吞吐量稳定在 50MB/s 以上，保证数据的快速读取与写入，避免因磁盘性能瓶颈影响系统整体性能。测试过程中，持续监控系统的内存使用曲线、磁盘 IO 队列长度等指标，分析系统性能变化趋势。

<a name="_toc23003"></a>3.测试设计说明 

<a name="_toc9343"></a>3.1功能测试设计

<a name="_toc7914"></a>
3\.1.1测试目的

验证系统各功能模块是否符合需求规格说明书的要求。

<a name="_toc15510"></a>3.1.2测试范围

学生信息管理、成绩录入、成绩查询、成绩统计等模块的所有功能点。

<a name="_toc10489"></a>3.1.3进度安排

2025 年 6 月 16 日：完成测试用例设计。

2025 年 6 月 17 日：执行功能测试。

2025 年 6 月 18 日：分析测试结果，修复缺陷。

<a name="_toc3850"></a>3.1.4测试条件

a.硬件：服务器（CPU：Intel Xeon E5-2620 v4，内存：16GB，硬盘：500GB SSD），b.客户端（CPU：Intel Core i5-7500，内存：8GB，硬盘：1TB）。

c.软件：JDK 1.8，Tomcat 8.5，MySQL 5.7，Selenium 3.141.59。

d.人员：测试人员 3 名，具备 JavaWeb 开发和软件测试经验。

<a name="_toc10463"></a>
3\.1.5测试资料  


` `a.《基于 JavaWeb 的学生成绩管理系统需求规格说明书》

《基于 JavaWeb 的学生成绩管理系统详细设计说明书》
` `b．被测试程序：基于 JavaWeb 的学生成绩管理系统的 war 包存储媒体：

光盘、U 盘或服务器存储
` `c．**学生信息管理模块**：

输入举例：学号 “2024140518”、姓名 “张三”、性别 “男”、班级 “计算机 4241”

输出举例：学生信息添加成功提示、学生信息列表

**成绩录入模块**：

输入举例：课程编号 “C001”、学生学号 “2024140518”、成绩 “85.5”

输出举例：成绩录入成功提示、成绩录入结果

**成绩查询模块**：

输入举例：学号 “2024140518”

输出举例：该学生的所有课程成绩查询结果

**成绩统计模块**：

输入举例：班级 “计算机 4241”、课程 “软件工程”

输出举例：该班级该课程的成绩统计报表和图表

d．测试流程图：展示测试的整体流程，包括测试计划、测试设计、测试执行、测试分析等阶段。

![](data:image/png;base64,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)

图3.1——测试流程图

<a name="_toc20019"></a>3.1.6功能测试用例表

列出各个功能模块的测试用例，包括测试编号、测试名称、测试目的、输入数据、预期结果、测试步骤等

|**测试编号**|**测试名称**|**测试目的**|**输入数据**|**预期结果**|**测试步骤**|
| - | - | - | - | - | - |
|FC-01|学生信息添加|验证学生信息添加功能的正确性|学号：2024140601、姓名：张三、性别：男、班级：计算机 4241|系统提示 “添加成功”，数据库可查询到该学生信息|1\. 登录系统，进入学生信息管理模块；2. 点击 “添加” 按钮；3. 输入学生信息；4. 点击 “提交”|
|FC-02|成绩录入验证|验证成绩录入功能的正确性|课程编号：C001、学号：2024140518、成绩：85.5|系统提示 “录入成功”，数据库可查询到成绩记录|1\. 进入成绩录入模块；2. 选择课程 C001；3. 输入学号及成绩；4. 提交数据|
|FC-03|单条件成绩查询|验证单条件查询功能的正确性|查询条件：学号 2024140518|显示该学号对应的所有课程成绩|1\. 进入成绩查询模块；2. 输入学号；3. 点击 “查询”|
|FC-04|班级成绩统计|验证成绩统计功能的正确性|统计条件：班级计算机 4241|生成该班级成绩统计报表及图表|1\. 进入成绩统计模块；2. 选择班级；3. 点击 “统计”|
|FC-05|学生信息修改|验证学生信息修改功能的正确性|已存在学号：2024140601、修改后姓名：张小三|系统提示 “修改成功”，数据库信息更新|1\. 搜索该学号学生；2. 点击 “修改”；3. 变更姓名；4. 提交|

<a name="_toc16617"></a>3.2 接口测试设计

<a name="_toc20262"></a>3.2.1 测试目的

验证系统各模块接口的功能正确性、数据交互一致性及异常处理能力，确保接口满足设计和业务需求。

<a name="_toc1541"></a>3.2.2 测试范围

学生信息管理模块接口：学生信息添加、删除、修改、查询接口（如/student/add、/student/query）。

成绩管理模块接口：成绩录入、查询、统计接口（如/score/upload、/score/statistics）。

数据交互接口：模块间数据调用接口（如学生信息与成绩关联接口）

<a name="_toc16018"></a>3.2.3接口测试用例表

列出各个接口的测试用例，包括测试编号、测试名称、测试目的、输入数据、预期结果、测试步骤等。

|**测试编号**|**测试名称**|**测试目的**|**输入数据**|**预期结果**|**测试步骤**|
| :- | :- | :- | :- | :- | :- |
|IC-01|学生信息添加接口测试|验证学生信息添加接口的正确性|<p>JSON 参数：<br>{"studentId": "2024140602",<br>"name": "李四",<br>"gender": "男",<br>"className": "计算机 4241"</p><p>}</p>|HTTP 状态码 200，返回 "添加成功"，数据库新增学生记录|1\. 使用 Postman 发送 POST 请求至/student/add接口<br>2\. 传入 JSON 格式学生信息3. 查看响应状态码及消息4. 查询数据库验证|
|IC-02|成绩录入接口测试|验证成绩录入接口的正确性|JSON 参数：<br>{"courseId": "C001",<br>"studentId": "2024140518","score": 88.5<br>}|HTTP 状态码 200，返回 "录入成功"，数据库新增成绩记录|1\. 发送 POST 请求至/score/add接口2. 传入课程 ID、学号、成绩参数3. 检查响应结果4. 验证数据库数据|
|IC-03|单条件成绩查询接口测试|验证单条件查询接口的正确性|URL 参数：<br>/score/query?studentId=2024140518|HTTP 状态码 200，返回该学号对应成绩列表|1\. 使用 GET 请求访问查询接口<br>2\. 携带学号参数<br>3\. 解析响应 JSON 数据4. 验证成绩信息准确性|
|IC-04|班级成绩统计接口测试|验证成绩统计接口的正确性|JSON 参数：<br>{<br>"className": "计算机 4241",<br>"courseId": "C001"<br>}|HTTP 状态码 200，返回统计结果（平均分、及格率等）|1\. 发送 POST 请求至/statistics/grade接口2. 传入班级和课程参数3. 检查响应中的统计指标4. 对比预期统计逻辑 |
|IC-05|学生信息删除接口测试|验证学生信息删除接口的正确性|URL 参数：<br>/student/delete?studentId=2024140602|HTTP 状态码 200，返回 "删除成功"，数据库记录删除|1\. 发送 DELETE 请求至删除接口2. 携带待删除学号参数3. 确认响应状态4. 查询数据库验证记录是否删除|

<a name="_toc14241"></a>3.2.4测试工具

**Postman**：用于发送 HTTP/REST 接口请求，验证接口响应。

**JMeter**：批量测试接口性能及压力场景（可选，也可用于性能测试）。

**Swagger**：对接接口文档，自动生成测试用例（若系统提供接口文档）。

<a name="_toc30145"></a>3.2.5测试流程

**接口文档分析**：根据《详细设计说明书》梳理接口清单及参数规范。

**用例设计**：针对正常场景、边界值（如成绩 0/100）、异常场景（空参数、格式错误）设计用例。

**执行测试**：使用 Postman 发送请求，对比响应结果与预期输出。

**缺陷记录**：记录接口返回错误、数据格式异常、权限校验缺失等问题。

<a name="_toc19236"></a>3.3 数据测试设计

<a name="_toc14499"></a>3.3.1 测试目的

验证系统数据持久化的正确性、完整性和一致性，确保数据在存储、读取和更新过程中无丢失、无损坏，且符合业务规则和约束条件。

<a name="_toc4190"></a>3.3.2 测试范围

学生信息数据的存储与读取，包括学号、姓名、性别、班级等字段。

成绩数据的存储与读取，包括课程编号、学生学号、成绩等字段。

数据之间的关联关系，如学生信息与成绩数据的关联。

数据的完整性约束，如主键约束、唯一约束、外键约束等。

数据的一致性，如事务处理过程中数据的一致性。

<a name="_toc11514"></a>3.3.3 进度安排

2025 年 6 月 17 日：完成测试用例设计。

2025 年 6 月 18 日：执行数据测试。

2025 年 6 月 19 日：分析测试结果，修复缺陷。

<a name="_toc7790"></a>3.3.4 测试条件

**硬件**：服务器（CPU：Intel Xeon E5-2620 v4，内存：16GB，硬盘：500GB SSD），客户端（CPU：Intel Core i5-7500，内存：8GB，硬盘：1TB）。

**软件**：JDK 1.8，Tomcat 8.5，MySQL 5.7，Postman 7.28.0，Selenium 3.141.59。

**人员**：测试人员 1 名，具备数据库测试经验。

<a name="_toc7206"></a>3.3.5 测试资料

**有关本项任务的文件**：《基于 JavaWeb 的学生成绩管理系统需求规格说明书》《基于 JavaWeb 的学生成绩管理系统详细设计说明书》《数据库设计说明书》。

**被测试程序及其所在的媒体**：被测试程序为基于 JavaWeb 的学生成绩管理系统的 war 包，存储媒体为光盘、U 盘或服务器存储。

**测试的输入和输出举例**

**学生信息管理模块**：

输入举例：学号 “2024140518”、姓名 “张三”、性别 “男”、班级 “计算机 4241”。

输出举例：数据库中存储的学生信息与输入一致。

**成绩录入模块**：

输入举例：课程编号 “C001”、学生学号 “2024140518”、成绩 “85.5”。

输出举例：数据库中存储的成绩信息与输入一致。

**有关控制此项测试的方法、过程的图表**：

![](data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAoYAAADkCAYAAAAFKeUBAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAGStSURBVHhe7b2HU1trmuc//9PObu1O71bt7Nb0Ttra+VX9uuc3vTPTYXo63NQ3OAcwmJyzhIQQEgIJgQRIAiWCBIgcRc45YxuMs42/v/c5hGvfK2ww2AL0fKqespGOTnh1znk/53mD/gwMwzAMwzBMyPNnxN7/GYZhGIZhmBCGxZBhGIZhGIaRYDFkGIZhGIZhJFgMGYZhGIZhGAkWQ4ZhGIZhGEaCxZBhGIZhGIaRYDFkGIZhGIZhJFgMD2Fh4gn6fVsXOoY7HuLFs529I2YYhmEYJtRhMTyEwbZtrCzvYGMDFzb8Ldt4+ujV3hEzDMMwDBPqsBgeAoshwzAMwzChBovhIbAYMgzDMAwTarAYHgKLIcMwDMMwoQaL4SGwGDIMwzAME2qwGB4CiyHDMAzDMKEGi+EhHEcM19d3MDf/GPOzjzEz9whL8+Kza7vvLYq/F+a24PfP7cUC5hfEsovPMTw4L/6ex+zCC+kzM9OPsLq/XrHOmekHmJ1/isXZ71+fHFv/fpmN15ge38D4+PrB+senH++99/5gMWQYhmEY5k1YDA/hOGK4ungfKZGJiLwaibCcSpTnqVBq9aGhoQsZsUnITsnBtZsxiIxMwJUvLsPRMoTcTBUi4lIgy5EjPbMceSlylFc0wFgzKK1zbekRCnRGVNtb0OaoQ35JNXQyJX79qy8hUxlgqGhBtbEISSlF0KkKUVLhgc1YihSd90f7d1iwGDIMwzAM8yYshodwrIzhylMo06Lxr//fZzBW+5CUUYgqjQH25nEUyVQoVJTB3jQCp8UBV3ULaqxOGEy1yM7ORnxWAZKi85AUmYYclR4l5V6U6AzIy9Xgy2+vQ6WyQS6To6l9CsV5Zegc30Kr04OukTUYc9Lxu88jYSqtglKpR55CC1PtaMB9DBQshgzDMAzDvAmL4SEcRwyXJpegUpuFxFWhUF2EYq0NmTItOtt7EX7lBhLjshF2JxZ//Ldf4+rtFDibZ1BlKkN2ViZS8gwHYuhqm4BBZ0OpthDZ4vOJcovUbLw8swinvR3KrDykpSmQmaGAuqwR9UYN/uXfL0FbZEJ2QgyuxeTBZm/B+hv79q5gMWQYhmEY5k1YDA/hOGK4MreOsBu3EXnjDrLLutBUqcHVaCNW11/DYSiHorACt8LDYLf7oIxXomVwHnK5HmZtLhLVDjS6WqSmZBLDjAQFmt1WGBsWYCowoMHdCU+tC8XWThTkOSDLKoBBkw9f+yzylTqolCZ4nPUHYujwTATcx0DBYsgwDMMwzJuwGB7CccRweWYNaSnpSE5VQaHMR47CjmqTCTq9FXG3opCmrUVM+BWoVAbEXEtCTY0d8Vl6aAz18NXVoEBnRfzteHjauhAdoYAqPRl3M3SoKrfj8rVkZMelI1erQ7V3HspsDco0MnxzIx3dfRPIV5lhK7UciGGlrQlr64H384fBYsgwDMMwzJuwGB7CccTwvAaLIcMwDMMwb8JieAgshgzDMAzDhBoshofAYsgwDMMwTKjBYngILIYMwzAMw4QaLIaHwGLIMAzDMEyowWJ4CCyGDMMwDMOEGiyGh0BiuLy0g/V1+i3kixkshgzDMAzDvAmL4SFM+B+hx7uF3oZPE51191BTPhnwvY8V3Z5NPH+6s3fEDMMwDMOEOiyGZ4Tt7W14PJ69vxiGYRiGYT49LIZnhIcPH7IYMgzDMAwTVFgMzwibm5uor6/f+4thGIZhGObTw2J4RuCmZIZhGIZhgg2L4RnhwYMHLIYMwzAMwwQVFsMzAmcMGYZhGIYJNiyGZwTKGHIfQ4ZhGIZhggmL4RmBM4YMwzAMwwQbFsMzAmUMa2tr9/5iGIZhGIb59LAYnhE4Y8gwDMMwTLBhMTwj0DyGdXV1e38xDMMwDMN8elgMzwicMWQYhmEYJtiwGJ4ReFQywzAMwzDBhsXwjMC/lcwwDMMwTLBhMTwD7Dcjq1QqDA8P773KMAzDMAzzaWExPAPMzc3hpz/9Kf7Tf/pPyMnJ2XuVYRiGYRjm08JieAbY2dlBXFwc/vqv/xpLS0t7rzIMwzAMw3xaWAyPwevXwPzYE4z3Pjr1qLcNIzOuLOB7J42xnkd4+nhn7ygYhmEYhmECw2J4DEgMe7ybWJx7da7C3/IID9Ze7B0FwzAMwzBMYFgMjwGJYW/jFjY2cK5iqOsJiyHDMAzDMO+FxfAYsBgyDMMwDHORYTE8BiyGDMMwDMNcZFgMjwGLIcMwDMMwFxkWw2PAYsgwDMMwzEWGxfAYsBgyDMMwDHORYTE8BscVw+XZZRQodbhxKwqJiSnIzChHmVKOcnsn9BWdWF2n5V6iUlEGjd4FT60HHYNbcBTkQ2vzieUL0eNfwvzcA+RE3sCliCyo8oqRkpyH7tHtH23vsGAxZBiGYRjmKLAYHoPjiuFkTwdMJS6kZSuRlKZBdmQOMqLioDO1QV/iQLnRApWqCNe/uoYspQGt/StwlFfgd//yG4SFxeBX//4FIuM1KNebYbHV4OrVu7gTdgtKow+Lq4G3GShYDBmGYRiGOQoshsfguGK4vvIYbosDGTKlCP2BGJY5hpBZUI4acxVU+QakJckwPA+sieXr6nqhyMlCa9soUnKK4R9cRKVOi1/94v/iRlg8IiPi8Kv/+yskKSxYXd0JuN0fBoshwzAMwzBHgcXwGBxfDJ9AmaVGRbEO8epq1JQ7oUxKRlXTMnJVhejsG0KRyod6iwkt7WMoMxphcM9DmXoXOTItvr58Byp9LWamFhEbFgN5UgyuRiuQcCcelqaFgNsMFCyGDMMwDMMcBRbDY3BcMVwYHkB6hgGKvHJUW2wosTVBn5kBR9sSEu7egLHUgogb6agodyAjJh7hSTqMLGFXDOU6xMRnQaUyo294ThJDRZYMCemaY4vhQMcjdLWOYHZ2Fvfu3cOzZ8/2johhGIZhGOZ7WAyPwXHFcG3lKcYnH2Bx+RU21ncwObGKAf8SllZfYW5+GxPDS5iefy6W3RH/X8Hiym7T8OzsFtakgSm7sb7+CvPzTw/+Xl58goWllwd/vy8GOx/D3z0Jv9+P5uZmuN1u2Gy2t8LpdKKtrQ3j4+OSPL569WrvqBmGYRiGCRVYDI/BccXwrER/+zYqSt2wWq1wuVzo7OzE9PS0eG8Djx8/Fsf1Gi9evMDa2hpGRkYkebTb7T+Sx9raWrS2tmJgYAAzMzNYXV3FgwcPpHXQ5xmGYRiGOd+wGB6D8yqGb/YxpGbkpaUl9Pf3SwJIslddXS39S3/T64uLi3j69Km0/Js8efIE6+vrklTScu3t7WhqakJdXZ2UcaT1UDaSoqWlBd3d3RgdHZXWt7m5yVlIhmEYhjnjsBgeg4sghoGgjCFJHzUhk/R1dXW91dxM0tfT0yNlFGnZw9jZ2ZHEkySQspEkhGNjY5Iger3eQ7OQ9D71f9ze3n7n+hmGYRiG+biwGB6DiyqG74Nkb35+XuqDWFVVdSB1JI99fX3Se9Sk/CGDWra2tiQppP6PHo/nLWk0mUySTDY0NEiySpJJsknSSfJJ2yMZZRiGYRjmdGAxPAahKoaHQXJGfQ0p49fY2Cj1XySRq6+vlySSRG5lZeVETcj0WZJHav6mZmnaFjVTUxaTxJSar+n/tE2fz4eOjg4MDg4ejMB+/vz53poYhmEYhnkfLIbHgMXw/ZDIUZMwDUyhgSzU/5DkjQa+UFBWcGJiQmq6Pi1o4MujR49w//59SURJSCkDSaJI0vhmFpKCmrWpaZzkkT7HMAzDMMwuLIZHhDJPY2PjqC4ZRJdn61yFr3oNPm+3lOELNg8fPsTQ0JCU7SNRrKyslLKMlOmj/o2U5aOBLx+zryHJIDV/UzM4ZRrflEaSWMp8klSSXE5NTb01+vrly5d7a2EYhmGYiweL4XsgGaApWkgYSCTO67Qsy8vLUvaOhIwE7CwN8qAypf2jkc4kZDU1NVJ5k7TRyGdqQqZs4KfaZ9ofElTKKFKz9Jujrx0OhySyNGiGXqP3aPoebrpmGIZhLgIshodA/doom0WDLSYnJy/MVCuUNaQ+enq9XprM+qyy3yRNTcO9vb2SJO43R1NmjzKMlMkLhuDSvlFTOGURaR9ItPcnD/9h0zWdPySQ1KxOU/1wxpFhGIY5y7AYvgFV2nNzcwfNmyQmFxUazUsZ0MLCQkm8KDN6XiAZpAwjDXAh+dqXRRoAQ7JLo5YDzcMYDGhfSSBpv0jI6bx6UxwpA/lmszVNCUTy/rGb0xmGYRgmECyGAqqAqVImwSC5OE+SdFLo2ElaysvLpSbz8zoYg7J41JQ7PDwsZe6oOZokjAaa0EjmhYWFM9nMSw8j1ExOTdGUVSTZpXNwf8Q19XekY6DXqamdspO0PGceGYZhmI9ByIshVchms1mqeM9KlikYUAaR5ImyWJTBugg/cUfyRNk3Oi6aB5FEi47PYrFI3zc1U59lSNpJZmnADjVD07m63w+TMo37WUd6oKEmbMr80rQ+F+G7YxiGYYJDSIohVbgkBdT/iwYPXJT+g6cFNdPS5NIkIRcxM0XfPzXZUoZ0XxQpw0jHS+fFaU6l86kggaSR1iTAb0pjRUWFNFCGfh+bMsN0fCTLlBWncmAYhmGYNwk5MaSsIM2lR5Ulz2H3bkiUSC5IOC46JIPUnYAeFKgZl+SKmnSpaZoyducZkkCSfWqqpuOj46Jmdsqg0nVAGUjqb0oZSRp0xcLIMAwTuoSUGFKTIg0soUqSK7+jQSJNP0lHEUrs9/2jLNv+XIeUYSa5uihNtdR9YP83smnQ1f7PEu7/7CFdK/S9UxmEUr9bhmGYUCYkxJAkkH7pgjryc7Pxh0FZQxqgct6zZyeBRIrEkLJt1ERLo4zpYeNDfiP6vEAj82lCcmpqJ2EkWaT+jNRkTQNhaMQ192lkGIa5OISEGFIlRh3zmZNBc/aVlZVx9mgPGhBC5xaJImUVqSma5PGiQyK4uLgoZRhpjkY6/v0Jv2nOTz4/GIZhzi8XXgzplytoMmTmdKA5AilbxrwNDeigfno0kIWaX0PtF1Aoa0qiTNlFksT9pnf6XWzO0jMMw5wfLrQYUmVF2QyumE4XqvwpmMDQiGeaQoYyaBe5mfl9UD9N+rlAugapCZrEOZS7IjAMw5wHLrQY0ihLau5iThcakEJ9zph3Q82tJIj0b6hDD2fUzEwjvmmACw3sYRiGYc4eF1oMae42rpQ/DjTVCf/6xvuh/nalpaUX+ucVjwuNgqbzhwbuMAzDMGeLCyuGNCUNjaLlPk6nD1XsJDs0xx/L4buheQEpS1ZXV8fNqHvQLAE0kEmpVEpyyKOaGYZhzg4XVgzDwsLwH//jf8Tf/u3fSgMDmNODKnQq27/6q7/iTNh7oMmj/+t//a9SedF0SczutD+XL1/Gn//5n+MXv/gFNyszDMOcIS6sGNLgCKqQs7KyeDLrU4aysH/5l3+JuLi4kJie5SRQf8wvvvgCf//3f8+/tPMGlEH9i7/4CxiNxr1XGIZhmLPAicRwbvQJpgcfn8mY7N9Gwp0CtHtnAr5/FmKy/xGePz25WL168Trg+j9WTPQ/REacAU3usYDvf6y4t3w6U8AsTj4NuP6PFZX6FmjljoDvfayYHTmduQQ3N15gaiDwNk4So70PEHVDgf721YDvnzTWFkJ3NDjDMMxJOJEYdtRsYm7qJccHRqf3IbYfnLyPHsllR+1WwG1cpOj3be0d8cno8Vz887bNdTrdJ+bHn2Ck52nAbZzVmBh6jvFezs4yDMN8CCcSw676LWxs0KTHHB8S/e2PT00Me5u2A27jIsVpiWFvw8U/bzvFtXkakBhOj74IuI2zGguzr1gMGYZhPhAWwyAGi+HxgsXw6MFiyGLIMAzzIbAYBjFYDI8XLIZHDxZDFkOGYZgPgcUwiMFieLxgMTx6sBiyGDIMw3wILIZBDBbD4wWL4dGDxZDFkGEY5kNgMQxisBgeL1gMjx4shiyGDMMwH8InE8O+Dj9kyfG4Fa9CVUMXyg0d0uuLkwvQaQxQqSiMKBD/VtQOY3ZkGtp8A3KzcxEZky69Xyle31/fUEsHKqvaodJWY0V67RmMeufB+1KsPEFltQ86oxnOSi+crhaMzT2X3nPa3WiuNiMhc3fbOSlJcPU92f3c+iuUm2yYnH8l/b0yfx8FxZWYmZlBUngmspMSUNawiB5vA3z++9Iy/sZWuFuWdj9/xAiWGHZ4ujC5/Ao+lxONbZPQlzYevFeYegeRSWqpTPI1ZgzNPIe9xI2SPCXScw1IyiqEp74VGkMlzNZGrK7tfq6vxoMykxM1DYOodjZDry6BzTmAquo6rOwtY7G60FxViuTs3TLPSk1D8/Czg22/L4IlhlaTG50tbQgPi4Q8S4bbd1JgtzUgPiIK2rI6KFMScDMuDxWuQYy3dKHEViPey0RmXKQ4l0egE2VlNFowPLe/zlUUZ1ei1NYGa6Eat2MLUGVxIkMsH5lSiEr3EIYabPD0PkRZkQFXvvgWmaK8fN3Lb+3XuyIoYiium2qLCwXiXMmWFSIpvQhNLd3Q5DlgM9diaW+5bl8LCorK0TJwHx6x/PiyKGN1OcbfWNeAKG9bfQ+MhWXSuaLSOjC/tHs9vi9YDBmGYT6cTyaGU909yMhRIzs7Hzq1Bl9+HQVb3SgWZoR0ZaaipMKDFGWFJCDl3gmYdGrExCQj8k40rlwNR9TdBNwNT4VWW4KM7EKkRN3FpdvpyMstgFLrwdLSImJFZSxVInuhL/dCEReGv/1//wXZijKkxaciKzEa19JKEX79BtJj7yIqzYDMDCWykhJR3TCMGzdvi21G4le/+iMiI8U2ozPh7ZzDyNgDeE16VLZsYLKtDrllbfD3jMIpPjPQPw+HoRQaUxv6+48uh0HLGM6vw2ww4zf/+mvcvJOA3/7bvyM6WQFFVgHUaj0Sk7ORI0Kha8Dq0kPYROWdE5eBDJn4/pQGJKckI0NZjAaLQ8ieVyrrhNs3EZ1eCF2JDZHffomf//PnyNeZESvKL+7WVSgMNbh25bqQpSjEZRiQnq5A5jkQw0n/GK5+ewM1bXNw2apQIM63ytpRLI+O4W6yBk6rE7L4O/j6Viq++PoWCisbYC7RIE2cO7lCGNNkOkSm56Pe0wmDEGyNdG7m4tuvYqEx1mFufAha84jY1mtoM+QYFZK0sfhAnLc3EZtXjZSbEaK81Pjm0jW4XAM/2r/DIhhiuL76AnaTC/FpcsTHyZGfX4jsnAJkpJSgzlGLinKXdK7ERwoBTsqHUZyDd8Q5kVdcjeuff4t0pQ56aw/mRidQWGyDwzuAfv8c/HuxuPwy4HZ/GCyGDMMwH84nE8PRvjHkq/SIi06BrkCN1MIWGEsqMTE4BbVcgWvf3cRXV27gdngcTPYutPn8yMvOQXxKHvKVBYiJToLR1onh4UWUWn3wex34/NtoyNLz0ODfxOrEJFIy8g+kUJ6ZjaqmCRjUpaJyzhfr0CNToYe9WAenqOQTY6IgS42XxPDSd5eEtBbB5fQhI68USeHhKCp1Qm90wunqg7+zA/FJRlRW2GG3+2C3OJGjMsNWUYmkvArUuFugzZYhRV4hKu+ugMcfKIImhquP0VjfBUVCBkptPiHdsahxNaOYhP2La5IQU3z39SVka+2oLq9FQVousnJ1kImyJTFMzclHlvhuOrpHpTIx5WXB2vFQiNQw8tVlkGVroMgT34fGhWKFDr1Dc4iOjEBuWoIkhl/96VsolCUYGFgLvI8BIhhiODe+hKhbkchVGxFxOxpZGTlIyiiEqaQamgI9VPk25JIYhmVBVliP6bERFGtMQgyTocpI2hNDFUwqFeS2IdTS+VPTAIXCJtb/DFUaIYlhCnG+t+Gz33yNUns7FteAAbcBro57UMrzds/n7DwhStM/2r/DIjgZw9fw2OuRKleL6yUPmj0xTE8QD29Z6fA0T0jnir6gGI7mZayvv4JGyHOpoxvJMbmYmV2G0tgkHkYeIDchCS6vePASy2uVeZCpqzE1+zTwdn8QLIYMwzAfzicTw4HuIeTlaRF+O0kISBFuRyqRmpklpCEVaQojKoR8KM0dmBpdQHpqGloa2xEfkyCE8Tt8+e0dSVRUJQ1YnNlAcaEZ6TGJyNdVITY+G83dszAcNEfvRpW+AKWeCUQL2UzJVCIuuwK9dTX45o4SCwsPodEUw6Iqxs0Ysf0MsS9x0fC0TkJTUYciUbFlJaeJfUyHWojN/PI9KGSlsJeZkCC3ojxPAY19CMsT05AZ6qXj666ph9U796PjflcESwzHW9zQucahjosXskxifAW6khrM+fvwu6/DcfXr7/AnUeaX//QlnL1P0GKxwVDiRFqqkGpvl/g3GdkFZlhLqlDX5IdWSGKT244CxyTqK8z49rs7SEtJQ76pD9V6BWJzXVia20RufjHs+RrciElDaroC6YmJ6J0MvI+BIhhiuDy/LeSsBH1tzUJ2FOLcKkZ8ggxd7mrcis1CSUXTbsYwIlf8vwXLS2tQ5OiRn5ADU4UDRVoSwwL0NXmRZepEKZ3rdcNCmnUYHJjFzOgANCXdyM/ToDhVDntDL4oqOuF36xGTasSduCTpfM5MToa+wh9wHwNFsPoYtrpdUJa6ESXKylfbCJkQw8wMCzyVBjg8w0gQD28+dz0qnaPwVldCmalG5/AKwi99Bl2RGwpDA0a7B6HNV6FvenedPU3NqG8/+gMEiyHDMMyH88nEcGliDjlJibgWk4esFAXaBtZQkKMUFWcJvPUNyMz3wFxWjTRRqXqq6uDz1KLYPQpDoRUNFgvMTj8KxLIORwumpx/Aba5EpWcW62uvUGtvPui/NDe1ITU7mZViG0PbMKly8Mt//jWyC2uQm5WDxMRcuJsnodOokZSsRVzkXeSKijcl8joKjV6UmGqlfo77YqjKL0V3/wLS4lNQWFKNpIhw/Ol6Mrxt05gcGseNsBgYq7vPlRiaCsswtvDkQAzDbkdCpbFipKMNlyJlyEhIQ3yyCvERN4UYbkGt0KDSWIJ8pQkKXT0GeoaQoa5AWX4aEnKroBIir6moR2ZCFlJySyCLuY5f/uYb6MvF95qSgxQhgR19q8gTlX1WqkrqHqCgMo8KR8d44H0MFMEQw9XFbeQpStHTWIOI6BxJDMPDE+Dvn8Y3n3+F2OgkhH33BX71ZTgiY4XY6bXIL2+EPLsI8iwtWgdWoVap0VFTjd9fS0FpgRHJqcUoVOmQoVBBKcpRrTKhc2gTbrFc2+wO/K3dMNq7MTs2h/ToMNy8fgO//MM1GPS7DyFHiQ8Rw1evXuHp06fY2trC+vo6FhcX0dEwKcRwt1/ue2P9JTQyDUy6YuQXV0JR7MDQ4CCyM22okKUgUelEcpIQXKMD2YpCKHLLUJJXBGtZKbRV49hYvAdZYhLkZX7xgGGQxHB2ah0uSzU8nRuBtxkgSAxbamYwPz8vHcfm5iaePHkiHR/DMAzzbj6ZGE6PLaGrfwPjQ3OYnX+M4dFNzE6uoLtvDsurT1BPTWwi2npWpeWX5+5hZOoRRodXMTO+iuamXnQN7FYOC5MLsLs7Mb3w4z5Ho327TZu1zRNYWdxCR8cs1sTrE4MzGJ1+IiqKHbS2dMPj7UH/8CYGBpak9xcml9DTN4+Fld31LC9sY2L60cF61xYeoM43tvf3c/hqOzA8fh+9e02h81P3MDFztKau/QhaU7IUrzE6sIjl9TdeW3qIwcm9ATgi5iYWMbnwDKP9c+jx734vI4NLaPI0S9/l6OgKxgcm0dA6g3Xx3uSg+L/PL76ne2LZHfR1TWBu6RVWlx9KXQO8jX0YHH6AgcEVafl5Uebz1Kdub3vvi2CI4dLUMupbpqRM9bg4f6RyG1zAYO8MZpdfoqOpe7d7wf752zKD3r4ZqTl4Y3ETTeK4axsGMTuxhNHJB6it7cPi8o44vx7A1zSG1TfKf0qsd271+7/H/GPis0NYXtqGW6y7o+fo/Vd/KIYkRQ8fPsTS0hLGx8fR29uL1tZWNDY2in2qFftuR3V1NWpqatDQ0CC9193djVbP+DGakncwIITZP7R7rkwMTcFb14muwXsYH1zF8MAEGtpmpPfGxf9bW4dR5x0S+zJ3UA4DnX6MzrzA9PiSdC0O94zAWduDOVFmB9t5T5AYNjomAh4jxQ+PcXR0VJJgEsiXL09+PTIMw5xnPpkYcvw4giuG5y+CIYbnNeoq5mGz2Q6ChMjr9aKrqwsjIyPvzKatrq5Ky1VVVaFc33D0jOEZCRJDq6FTOm6r1SoJb0dHhyTFL168+FFWlMSQBJEE0uFwvFVuFC6XS/r8xMQE7t27x5lHhmEuNCyGQQwWw+MFi+HR411NyZQVo+zh8vIyhoaGJCHalyj6t62tTRKmnZ2dCzGPIR0HHWtnZ6ckifvH6na7pdempqbE5zbw+PFjadkf8vz5c6ysrGB4eBjNzc0H63gzSLzpvb6+PkxOTkrbI4kkAX327Blev369tzaGYZizDYthEIPF8HjBYnj02BdDygbOzc1JzapNTU1S9oskxuPxHGTBKGt4mLhcBDE8DBI2yiIODAygpaUFdXV1B83p1PRMZTY7O4tHj96/LpJKEkEqa5JtEk4SRSprKnNar9PplLZB30N7ezv6+/slKV1bW5M+z/LIMMxZgMUwiMFieLxgMTx61FXMSZkskhxqJqXJ2e/fvy/J0HG4yGIYCJIzamqmsiLJowxgfX29lGHcz6iS8JEwUrP0caDlSTIfPHggySB9J4ODg1KGloSRmu5p/RaLRfqX/qZsLokrZXBJ8hmGYT42LIZBDBbD4wWL4dEjWNPVnIU4iRi+DxJHygz6/f6D/ogkjJQN3M/AkvSRAAZqlj4OtC3K5k5PT0tyT1lMs9l8IKgUlJEkedwfREN9R6lZnLoK0EPASfeBYZjQg8UwiMFieLxgMTx6sBh+HDE8DMoGUj9EakamDCNlAEnaKGPr8/mkZmnKQB6lWfo40EAYkkfKKI6NjUnboWZxGmhEfSj3m7Bpn6gJm0ZiUwaSspUkkJQdZRiGeZMTiWFHzQNJDjk+LJqs9/Bo83TEsLnqXsBtXKQY697eO+J3Q5kWyphQpoVG11IFWVlZibKyst0MT1F/wPUHKzrqNlFvWUBl4SAsuiGpGbij9mTXVpvz/l5pnIyFiSdocz2Q9seiG0arayPg9s5KeKyLcBgnhRge7Vz5mPywWZqkjYSRMn5Go1GSNuqLSBlGWvZj8WYTNskrXRfUv5EEkq6N/eyjyWRCeXn5QfaTBtHQZz7mvjEMc/Y4kRgyzKeAKiYaGbq9vS0149GIT5qLr6enR2pGo8wMid+bfbMoY0KZERpccNx+dR+DfVklCaDMDmVvaD9JEuhfOhY6trMGCQUJBEkDTe9y1qFypqwd7S+V6VnOiFG2j85P6mMoPbDsNRHTHIvU95DOczpnPvXcim9mP+n62u/7uP9wRUJL+7jffE3HsN98TeXNIskw5xsWQ+ZMQKMyqYKhLAVlVqjJK1BzGHX835+Lb2FhQWpGO2vzylHFSNOUUH8zqvRpv+kYqHKl46J9pyzSWa5A6fsgIaT9poEW562yp/2lhwfqB0jHcdpNuB8L2m86p+k6oHOdRo/vj2ymhwm6NqgfYTAHolC/RTq/9+eApOuR9pWuVzpfKGh/6W8qexJ0uhZoeZLH83YuMUyowWLIfBKoIiPBoEqC+l2RJL0ZJIBU8VGWgprdKDu1PwfcWe9AT1kSyvCQhOxnfahSpNdosmjKdJ6XQQBUaVMzIjUp0vdw3itxKncSkv0RvpR5Po/QdUDNuiSFJId0DdG5RkHnHZ1rJPNnATpnqJxJAimTSA9wNAckSSLt934Gcj9IIkkuqd8jfYZhmODCYsh8MJSpo8qIsl/7kyVT8xJlOX5486cRlXTzp0rivE67QZUdNfdS9oMGFOxXzFSxUXbqPE8nQs2HlMmkUa/UB+0iQs2j1BxKWVyS9YsCnZeUYaRrbP+cpL6M1I+Qrks61rP8ay0kvdRaQCO9Kbv+5n2D7iP0GvXFpKmDaDm6BikDzD9fyDAfBxZD5p3QTZuyXtQvjsSOnvip0qEsBTVx0f9JkujGTVmBi9JcRPtPFRAdE2UySf72R5iSANN75/0Y96EsFAkTVcxnWSBOC8qEkjyRIJ7XDOL7IHEiwafsL4kVCRaJI2Xt6CGGmqvPA3SN0bGQ4NK1SPcgugbpmPbvQXRc1OJA9yB6uKEHAJ7zkWE+HBZDRpI/qizppks3W8oaUVMiPbHTDZcqULrh7j+tU5bwIj6t0/FREzANYqGKlISQMjGUEb2IAkHHRP3BqH9YqE1bQk3M1LWBznM6ty86JFgkSzT4iY6XznO6vulcJ8misjiPDwW0z3Rc+60WJL0kwySL+8dHDz10ntOvzZAsn5Umd4Y5q7AYXnCoiZAyeFQhUBMoPVXv90+qqKg4kL/9ZppQaJ7Z7zxP0kejK6nioHKgCuW8ZFJOCokAjTAl0Q9lSJio3ytdD6Hav40ycnT9U/aNRIoGetHf+5m3i5AZJ4Gklg9qXqdWDrre6fyneyC1BpA0UqsILUP3houaSWaYo8BieEEgAaSbGmUD9kcz0g2PbvaUHaCsH3VQJ/kLxWYWyhJQUxSVxX6Z0HQ2oSKC+5AU08MBZUO5j9b30MAOkkN6eAp1SIpoIAjJEt1DqBWBzhf6XWdqXbho0DGRBNO9k+6T9LBIx7w/E8J+9xHKSF7E42eYH8JieA4hsaMmE2ouoWwXNXvSTYymQqEsGAkiZT9CveKnTvd0U6cyoiwpTa1BAnBeRgh/DKgcqMvARekfeZrQwxUJED0wMN9DokijiynbRsJE1xPJE11LFxnKMlI2lWZI2O+vSfdZut9SGdD/6WHzIg1kYhiCxfAMQ0+n1NRHT7I01cb+BM5UudMNKdT6hR0FqqzoBk5lReJMFRqzC1XmNNUJ824oU0YSxBwOXWeUdSdBIpkOlW4ob0IPEvSAvj+4h8qC/k8PFvxzg8x5hsXwDEE3VprOZX++L+owTf8nMeT5vQ6Hsl/UzLXf9HMR5t87bSjjQecSczRIoqkfJvN+aOAHyTTdr6jVIpTvVZRhJFmkJmnKrlL/beraQ03z3G+ROS+wGAYZEhgSP8pw0VMnzQNI/V34afNo0KAa6kROzaN0U2Z+DDWdU6XN/aOODpUVXZP8gHF0qOmVpquifon0kMb3sN1meLpH0VRQ+83wNLDnPI4AZ0IHFsMgQYMe9kfEUrNwKPd7+xCowqYmY7rZcuX9bqjZj/paMseDMqzcFeHDoPsb/V41ZfL5+nwbKpP9LkF0fnH5MGcNFsMgQM16NDKWBokwHwY115Ds8E31/dCAJO4zd3xo+hJ6aGM+DMqKUfaQ+h8yP4aa4Kl8uN8vc9ZgMfyEUFaQmlfoSZonWf1wqJmPJuGm0YAshu+GOshT/y8SHJ6e5uhQWZFQ08MH9w37MOjapIdf6mfH3RgCQ/VBaWmpNNMEtxoxZwUWw08I9R38u7/7O/yH//AfcPXq1b1XmePy2WefSWX4s5/9THrqZg6H+slRWf2X//JfpJHtzNGgvr4/+clPpLLT6/V7rzLHgWZU+Ou//mupDL/99tu9V5k3iYuLw5//+Z/jf/yP/yE1MTPMWYDF8BNCT9A5OTn4b//tv0lZHObDoGlo/uIv/gIGg2HvFeYwSJz/5m/+RpJpHgxwdChj+N133+GnP/2pNKCCOT6UAcvKypIeSmhwGPNjaG7V//k//yciIiJ4QApzZggZMXz66BWm+h8FPVprp3D9q1SM924FfP9TxuzIE7x6cfKm2KePP23ZjnY/kMqwu3Eh4PsfKx5tnk5T7Pzok4Dr/1iRFW9EaYE34HsfK1bnTqfp8P7K84Dr/xRRWdyC5Ls6TPQ9DPj+x47ZkdPpbrJ9/2XA9X+K8LnGceNPaUG7380OPcaL5ydvon3xbCfg+k8adG7dvZYLd0VfwPdPGqd1HTKhRciI4dr8Mwx0PMb8zCuOvehpfCgJ80mhsh3sehJwGxclhrufYGnqdDJuba7NgNu4SNHTsLV3tCej37cVcP2hEB01p1OGC+NPMNL3NOA2Lnr0+bbxaOvkD3SbGy/Q63sUcBtnObrrQ+snP5nTIaTEcGzgGWj2CY7d8Ldsn5oYXvSynRx+fmpi2Fm3FXAbFyl6G09PDAOtPxSCzpPTgMRwavRFwG1c9Bhof3xqYjjY+STgNs5srIvr8JQe0JjQgsUwhIPF8OjBYni8YDE8ebAYnjxYDFkMmePDYhjCwWJ49GAxPF6wGJ48WAxPHiyGLIbM8WExDOFgMTx6sBgeL1gMTx4shicPFkMWQ+b4sBiGcLAYHj1YDI8XLIYnDxbDkweLIYshc3xYDEM4WAyPHiyGxwsWw5MHi+HJg8WQxZA5PiyGh8T85ALc7iE0VXvROXofTpsPE4svxcX2Av6+RUx0t8PV9eBg+dmRSeiKrKiyuZGSrobd7kNL9yL6OwZgKy1DltomveZtmcKiWHe1uw0WexMamoZRU9uDNbGO9ZWncNR1okqvhSLPAJXKgDRFGerqWlFbLdabpUVGkg6dA7OoLK9FXW0LZpd2t780t4RKnRu1zbMH+/S+CL4Y7qDB0YAaUcbtw1uY6BmEwzcOX10zClU6FJXVobFlFB5x/HZrPRzOPrQ0tWNycUf6fFtTK9q9DUjLKZbKSpGZBUvDKGpdLUhPz0NuTgEqq3rRVGVDlbMb3SOPdre7/AiVYn0Vri6sv7U/h0ewxHB2bFaUQR8sRR7UtE6hVpw3u+89hNPaL77zIijFsatUemRnmjEn3nNZqjA+90o6TpuzHRsrT6CWyZCRko6kvErU1w2j1+tDgzg/neLcK8pXQZVXiPziWvT0DcNcVA9Pw7B0TtK2GrydKK9wYHxerHPvtfdFcMXwBSpLqsW1sXueLM+uw2n3ID27AHVNo7BXeLCwt6zFXIul1WfoH15Eubl+7/OvYDfViWVeosriweIKrWcHPS290jVsKauELN8s/d9Z1wWHpRXVJUZkKQzIUZWjq2sUJosbrrperK6/ltY55OuEzepF19D23jbeH8ERw9fw1TZLx2Yu1EJb1iD93904iuXF+8hLlaHc6pNeqxevrQj5CLSeSf8QslIykSPKpKl9FGZb98F7ZRUurC09xMDYNCrKOqXXOnwdaKxpQEJaAVJj1GjvnxFl70aVtRmLe5/bGB+FtrwRde0LB+t6XwRLDJenV2Gr7kZLQwfKjWYo8y3i/tWLntYWpGYVIjslVdy3DOJ+XoCeycfSZ9o9HnQNrqOsxImltd31DLR2odH/AF2ifOziHC4U9YJffJ9vbuvQYDFkPhAWw0Cx/go6uRypaUoo5GqkZ6qRlp6LuEwjRlqb8Ic/3URa9G1cjlJKQlLfugC9Mke6WcZGJyAycjfqOlegjonAjTv7r0Xj8i05hnr7cfvbP+LXX4ahRFRMsXeTEBERhzyNGd9di0Fs5G3IxQ2V1h0Wlw2X0w1ligJJCYVQpusgk+UjJVOLkf4xUZnVoUAsJ8/OwvUridCX1mL9kJv1DyNYYjjga0FaSr4kM2FXLyM9RyuO1wazxYVijRAdmRpf/eGP+PpqFEqr25B99w5+8cvPIFNXicomDRlx0cgpdiH69i2kxEQjIkUnlZUsIQK5ZY2oLCpFcm4hMtOFLEVmISs2BtWtq7BW1KJYZ4QqN19ad3p6AZbXdivu90WwxHC0dwDhX32G334egeJSK2JjcxB3JxYFRVpc/lM0IsMTkLsnhtG3ZGjvGUBW1G18djUN8aJ8rkfJYG8YR4kqU3zuFlK1LmRl5uKrzy5Bb2mGzVSLsKQcIYeFUItIzdZAmeNGvasJtgqbVK43b4QhOUWGhpa5gPsYKIIihpMTuB2dI+1zRloucpXFyE5LR7GlBtd+9y3+5Ve/wm//9U+4GZGIpLhE5OnsCL8ZgYLiEly5FoEvPr8sPlsOV41PSF03ctIViI/PQl5eMaIiE+Fr7BbXdxJuX7uK3/zxO3E9J8EgRMVW6UZmbDqUWhPkYttJotLPUpvQU98Aq5BB2p/EsDBEJStR7hwIvO8BIlhi2NfSKu5BCbj+p9/jy6txiIwV333XOLRZCsTdTYahqkfcjyKQU9iMtXfcaxx28fBltKG6SImbsUqEhaXB4/Xj2s3r4nxT4vqdaHz5x3CoNFVw19SgMEOJuMQi5MRokCtXiPtCAbpbe8R9oVEqQ1VqPL4Ky0aRQdzjAmwvUARFDNd3kJ8Ug8i4XNSJB12v3QaFuhZ+/zLa3FWISFIj5c4V3E0zIO5WMnzDm6i32VBk6cSquB/1NzUgKcuEiZFZJNy5g1vp5YhPShGfn8P03DEewFkMmQ+ExfDQeIHaCrtUKdz+5nOEZbuwsvoISnkhJhdeYaSxCsXeVWnZpYlR3A2PR3P7BDT5u0JH0Tf+CNqEBGTk7r+mwZ1IFXrb2oXgKBAVnYr4xFwUWhtQpDFi0D+NqJhkJAmZlMk0+Ozrq+IzZlhLKyETohgVkQt5auGeGOaiQFRA+toZDIgbRmd7JwqLG39wDO+OYIlhb109EuIV4jgMKCoqQltzG3I1DVAlRUFtEmWhLkH83VgkZGiQr6lAlqggEqKSkJKRjvQsKypFWXVObqJIkYnMlERJDG9du4LEDC0qLfXQ5euRLuQvPk5zIIYJOcVQFNVgaHAB/p4RyJQ2LAfYt8MiWGI44OtEWr4G0dfFeZSeBk1ZH3TifBoaH0XUlQTEi3MoV6USFexNUcFWoLVjVqpAGhwu6AzN0v8Hh9ahLyyApVgNeeUAPOV6aCq6xPk2D12RDTHioUeRJT8Qw+QIGaLj09E/tCZ9Xi++j25xLgfav8MiKGK4cA/26vqD649CU1AJX/sULEYjPv/mGqo8Q/DY3EhKVsOoq0R4TK74jBP905vi4apYWk+z247L16JQU9OD4bFtISEvYZQVwt/WhBRdI1pq3EhX29DZ2i1kvQE2fTnyU2XIlGuQk7crhknpOYiJSEXfyKpUhp4yDao6jpf9DI4YAi1WM8LFA0V65HXcSdYhLiIWNncn8unelleEjEyNeBgtlcq33NaJPnF8dIxvRUczbl2JR3JsKmRZ6ahoXIC2pB7lBjticpSoLnNgemkB8gyHtM2+GhdU6lJEioft9LvqXTFMT0di5C3YOx9K6xxoqUdu6eCP9vddEayM4XBLB+S6GjhMpoNEQYKiCn0NdYhL0xyIYXJUDlr7Z1BZWgV5Vh5y5OLBWKFFkalGlHkP1PFhuBmlxdVr34lrNAdfXI5ES8tEwG3+KFgMmQ+ExTBArK+9hNWwW7HkZmTh3//wnRAJUcmYmzHpH5ZeT38jY9g29BD1FQYpo5CZo5Vey0jMQdvoVkAx7Gnx4dp3t5AYnwKbbxHFmWnIKGpET2sHMgrMKM3ORGRCrpDEVGQLMRzvHxSCasHdeDmsJTXI28sYDrf6kFfRBllkuti3OqhEJb80d//MZwxbXPVo7NmALi0HBlMFbtzNQW/PGBSZMsgKXDBpCqXMiyIrGyX2dkRcCxOVbTYUxS1orCrF1VgN5idXhFgqUaktw92UXCQkpCIlIRnNA1uwiIq6MD8PqQVuFGWoJTG0Ns6hpNiMquJ83MmpgDKzFGMTy9ITeqB9/GEESwz9viZ89+0dpAjxqG6egzY7EfKiBvjbm5Gd7RaVSRoyUzMRfTcdWUoL2n3U3NkkKvXbuBGThyq7D7WeERQV5CL57i1xzhaIh5I4XL4jl153O72Q6UzIlKngtFQhjTKGslohUga4HI1CEFJhyNXB23sPC1MrAfcxUARLDAs1pdJ5kCkvQFyMTLrurM5GFMiKpOspXDxA/PHzG+J8yoHFOwVFthb+vkGxnBZfUsawsAr1VVXifQ1s3mHUl1tR17YAfU4Rxmbuw26pxq2vP8fP/vWPyNVWo2foPnrqGqBTm4TIFKG82iu+q92MYVVpNWrqW5EekySESA+tewbTk/cD73uACI4Y7ogHCKNUbvtimBQRgdvpFaJcHMhJScK3N5KRpyqGTF6JsdEFOMQ5Rq0lb4a3thmZCXG4cjsDTV2TyE6Wwezql7aRIVeLe0+b2EaulDHUlNRiemQU+XmViE1SwCCu2wK5EETxQOizlYjPDSMxORMGmwt5Co+49peOfI8Ljhi+Rmm+DsbKZpSI+49BKpN68bCVJ+7foh6Q5+HSF7/Dn25mSN1ATPZe6XPOEhNahx9ifmQcmeLcWpkcR3qiDNaWeaiUevH3LGT62h9s6x3BYsh8ICyG74n5Lh+Uth8/ob2ZMaQgMfQ4XIhKUkk31ZjrYSgyV0EpL4WpthNWvQVd3bMoKTDAXtuPOPEE+M+/+Qzu+m7ERcUgOrMcC/MPkZebiYJsBb66mSKt5258NirztVLllpQunqS14kl7ZkPcXESFVl+NL8LkqBJylJ5bKkRBC4+r6siyExQxXH8FvbhplpZZcetmqpDcJISlVMCkVEOt1SMrxwhLeSPUeQoo8/Rwta+iTJaAf/qnX0Fl7oIqORW376ZiYPoV3DYzEhI1iImMlJpTqSIrc/ahQNxUZakqyPMK0OpfRYNRJSRgEle/uIZOnxff3M0X34sKhbICzC8frd9csMRwYXYL8Vd/j1/+5hpsdZ1IvBuD5KwyzK0KCUoXlbaoMO/cihPnSjEiYvKxtLoNk7ZUVKYDGOjoglppx/zKExg0OmjlGUjSOKFQWGExlKGudUJUOGZUagqQqihEhTgvJ8dHhBi6oYoJQ35FD1LFg02pwYgMjQOtlpqA+xgogtXHcFlUnjkyI6aX11Ck8mFxfAqaimbM9XXD2roBTU4KsuVFKC71YmEVUIprZl767JODjOHC7APUVLlRYm7D8voLzEw9gCJVg6pqN7TFNehubkWBySP1+y0ocSJLyLS7yoL0ZDW05e0YF7JEYliuzkCssgZl8iyoSx1ITsoXgnD0rH6wMobdXt8PxDAaFd558WCwIM4dPfxjj7Cx+hg6le37/n8BYrS9CTeFGJZX1ODunbvILazHqnidxHB3maWDjKFXXwRLTYcQ8jxk51diauYeisX1W2/W4cpdI8xGPZIVVcgSD49OYwVWz7gYuqxVyFeXQBmbjXRRlipVIW7ezUZ5kQH5xXbUiHuS0TkAjboUxZYOTPn9yFCK8lx9jdGOZsiLfVKfc5/dCUOpVZzDAyyGzCeDxfAdMeEfli7cgemX0t9rq09gKtzP/n0fGnMLRv1jGB2axeDkU2nZ8b4JjM48x8baU1SVWaTlCsQNQWqG7vTD17MuLVdf1Sh1hF8Yn4XeWA2TtRljQwvi/TXp/b7uMTS3jqDJNygNBpgaGEa5yYlK5yAmB8fQ659FoaFOCMEOZsU6ahw9R+5/EwwxXF99ju6BXaGeHhwX4nYfo11+mBx+6bWVqXmoC8zoHtnGVP8ICgzVqK4bkd7r9nWiXyrfx3Bb62A02NDUsYzWlmGpwpkfmUBX3zxctT0Ym3uO1aVHqHXWQKuqwrioaAaEmBuLTRia2cHG0haMZV6s7Q0OeF8ErSlZnCvN/gfiPHqO2ppOIbKvMTc+D7MQuwpLLfwj99DdtyyWfY2ejhFU2xoxtfC9BKyvbaPCUIt5IUFLEzMotzRhmgYsre/AW+1BgyjT1j4613aE8LRDrzWjtX8dve2z8NV64GjYfShqrfOis3/zYL3vi2CI4erMCvSW/cE5L1FtNEvXndMlRO6NLh77UeUR51/zoJBA51uvFxeLh7yO3ezoWO8g1OI1m3f8YDvLsxuiLPazp8/RI9bh8U1Jf/d0jKGitBQ1zbPo7Z2Ev7ULpupe6dodau9CgyjX/fW8L4IlhhQj3X5RFmaM7Q30omvOamiQRHCyfxj5okzq2w4fBEKDT3R6r1j+GRo9g9I9aUy81jW4BZ+vBzbT7j1xN8S5XOdHY0PP7nU8PIZKIZMma494uJnC8NAEiso8QpqA2eEJWF1Hb04+qhg+f/4c29vbWF9fx/z8PIaGhtDa2gq32w2bzYaKMhf624/XnYJiuH1y78HjlbhP7d7HKGbFMY7O7/5/fe0ZHJU+LIn/NzodUGmtGKG6Q/w9NTwLV8OQ9ABLA3Zau+cP1vHeYDFkPhAWwxCOYDUln8cIlhie1whWxvAiRTDF8KLEvhi+fPkS9+/fx+zsLAYGBtDW1oaGhgbU1NSgqqoKLpcLHo8HLS0tQuh7MTk5KUnis2fPpDI8bh/DMxEshswHwmIYwsFiePRgMTxesBiePFgMTx4d3g1UmBxwOBzw+Xzw+/2Ynp7G2toaNjc38fTpU7x+/XqvpA6HxZAJJVgMQzhYDI8eLIbHCxbDkweL4cnjQ/oY7uzs4OHDh5iZmZFk0mq1otzoxEDH7nyD5yaEGFbph2CxWOB0OiUpJiHez4IyzGGwGIZwsBgePVgMjxcshicPFsOTx1HEkDKGlD2kJmav1wu73Y76+npJpDbESqT3z3nGkGSQRJeaykkSqemc+lDOzc3hxYsX0jIMsw+LYQgHi+HRg8XweMFiePJgMTx5HCaGT548kfoS0sASk8kk9TecmJiQBPHVqx/fEy9aUzI1oa+urkplQBnRyspK1NbWSgNvjtK0zlxsWAxDOFgMjx4shscLFsOTB4vhyYPE8P76Y2kgCWUAKVtGTavU55AGolCz8VEIhT6Gjx49Qk9PD8xmsySLjY2NUn/Mra2tI5cTczEIGTG8t/wc7e5N9Pm2Ofaiq24TTx+fXAzvrYiyrbnYZdtVv4XlmdMRwzbn/YDbuEjR33w6UjPaGXj9oRBtrvt7pXAy6IGmy7MVcBvvjodor12HxzaLauMILMUDcJkn0GhfQEf9BrobHqCncRO9TQ8DfDZ4QftUb52BzTAEh3ECdlsdmpubpabUD+1ft/3gJVqdDwJu78PiIRqqFwK8fsrxgQ9olDWkUdwk0zRam4SamtkHBwelrCpzsQkZMdzZeY0Xz3Y4fhCn0WrwWjxMBlr3RYtXL0+nieXl84t/Lr58cTplRWUeaP0hEc9PJ0vzrjJ8/vQVNtY3MTQwCk99E6wWO8wmixTlZitqXPXo7OjF5PgM1lbv48H9bTzafiZ9LtD6ghUryxuor22AuawSleU2aZ9XxWu0r3TvPyl0nwy03Q+NJ49foMrqCPjeacZp3bOoeZ2EkJrbSRApo1hdXS3N90hTATEXi5ARQ4ZhmFCBMj7Uj4yaAakv2dTUlNSfjPrS0bx91LduP+i1kZER3Lt371z0L6PBEtQ03NfXJ8kJNQ1TsydluM4LlL2k7+M8Q7I4Pj4uDdah74CEkZqeaUQ391M837AYMgzDnFOociahowmZu7q6pOlVaBDB/qTNJH00mTONuKWBBeexvxhJBo0OJhGkY6P+gXRcJFfndUQtNc/SMV0kHjx4IPVR3P+OmpqapPOSp8c5f7AYMgzDnHGoGY+yfjTdyJvZPsrWUP856vu1uLgoZc1oxO15HyxA+0/ZJxJcvV4vSQYNFqEBEhchG0UZtosMNS+T+NIDCfVPNBgM0kMLZROZsw+LIcMwTBChSnT/d3r3M391dXWSPJD87Wf/qJLdn1fvokGZz/2sIDUPUx82GvhwEefYo++vvLx876/Qgc5vOq9pahzKmJL4P378eO9d5izBYsgwDPMJ2Jcf6pdFzbvUJ4uEj0SIKsr9Jl/K/FHW76JDWUHKAlL/QMp8UlaQsqIXfcJlavqnYw1lKANOzc5ut1uK7u5uqZsDczZgMWQYhjllqMlzbGzsrczffrMvDfRYWVmRKsJQ/NWJ5eVlqSwoK0hCQLJM0hwq9Pf3S+cAsws9BNEDAvVNpGxiR0cHnj9/vvcuEwxYDBmGYT4Aqryowz01iXV2dkrZP5JAavolIRwdHQ2JzN/72B88QlJcUVEh9ZMM5QEJVA6UNWQCQxnz/ZHzdA3R6Hrm08JiyDAM8x5Ibmhgx/DwsNT0Sc1flPWiDvXUL25paSkks3/vgpqKKTNGTeVUZpQlvYj9I48LSQ83m74fevCiQVV0nZ236YjOOyyGDMMwAaDKm5q1qHmLKnPqE0iDQyjbw01d74aEkLKDVLFzxudt6KGCp3A5OvQwsba2hvr6ekkSqZsG83FhMWQYJuShyodGBlPTFVXc1CRMFRFluZijQxU4lR2NKGZ+DJ1ndH5x5vTDICmkh7T29nbO0H9EWAwZhglZqHKhrBbNtUad36mpmPsFfhhUjjSxMZff4VDzOjWtMyeDRvZTH0Se7ubjwGLIMEzIQRkbmi6Dmju5g/vJIaGm6XY4E/Zu6EGE5Jk5OdTVg7KH3Cx/+rAYMgwTUtDUKNTcyVOGnA4k1TT1zHn/tZVPAfVNpew0czrQzyLSKHfmdGExZBgmZKCf5KImT2o25maok0NSSPM11tTUcHm+B3ogoT6rZrNZmuaIORmUnaZpkPbLkx9MTg8WQ4ZhQgYSwr/6q7/C3/zN30jNn8zJaG1txU9/+lOpTClryBwONSP/4z/+I/7yL/8Sly5d2nuV+VCoL+vvf/97/Pf//t/x2WefcTeGU4TFkGGYkIFGzf7d3/0dfvvb30qjkJmTQZXzP/3TP+HnP/85Z8HeA4mLXC7HT37yE2n+S+bklJWV4T//5/8Mk8m09wpzGrAYMgzz0bi/+gIL40/OVCRHF8CgdgV8L5ix/eDlXqmdjPXF5wHX/7FCI7dBllIS8L2PFatzpzPg4NHWq4Dr/1jR7p3A5S/jMNF/P+D7HyUmnuDl85Nn017vvA68/iDGQMcSvv1jNIa7VwO+H6xYmnqKV6/ObwaTxZBhmI9GX9MWJoefY3rsxRkK2p+ztU+jfU8x3ns6E/d21m1iKsA2Pl58+vJsc51OdnK8dxvjA88CbuPjxactq+6GbWyun3zOvxfPdtDi2Ay4jWDG1Bm7lik667bwZPv8/v43iyHDMB+N/paHWF2hTuLgeEcszL7ChP90xLCnYQvr64G3c1Giy3M6PylHYri0sBNwGxclhrqenJoYkmQG2gbH29Hf+ojFkGEYJhAshkcLFsPjBYvh0YPF8NMHiyHDMMwhsBgeLVgMjxcshkcPFsNPHyyGDMMwh8BieLRgMTxesBgePVgMP32wGDIMwxwCi+HRgsXweMFiePRgMfz0wWLIMAxzCCyGRwsWw+MFi+HRg8Xw0weLIcMwzCEcRwxnRueRl6NF7I3LsLeuwe+fQ1RCGoZ6BiAz1EvLOEq1aBt9jvWVp2iurUPMzVv47m4utHIlvvxTOJT5RqgyMnEjLAPhX99GdnYuwhPy8FV4uvR5XYEazR3T0rop+vuXMTy8IP2/NCsN6soe6f9DgwtICPsacYlaFGjEOlUGKRp9vcjOVCMmKQeFpjro8krRJZYfHVlAfooWKrkMN8PiEBmZgK///TZGZ5+9dYyHRdDEcOk+NMUudDe3Hxxj9K3raB17BoNMIR2HFNcuQ+GYRUedG2p5FiJTdLA7G6DQ1aKvfx49Xo/4jpoReeX3B5+5JsquqcqCpFT1wbq/+S4W06uv0N/ehYikXFRqZCi1+HDnbjZGpx8F3scAESwxXF99Bmt1C9ptNrja1w5en5ncPV/9He1ISjciIyUTDQ09KDBWIeybS0iOjoBcVwWtpgz6Ui9KnP6Dz2p0RlQYzRgYnkNCbDbKVOmITddBlVeMm9czMDA4jus3b0OWnoFvrkdJ5ehsnDz4/PsiWGI4M9iLhFQD/P0rWJzbRG6BUXo9Jz4Wt8Ni4aptwzeXbiMnUwlH6yLmRoZRbGrDQJsXl76LFtdZPnz+++jx2PHFtxEH51B2Vh7K7IOYHR9Gbo794FqmGGxy49s7WQfLUty5E4W29mE0dMxBk69BUQG9rodSbYaryonCPCW+E/eHK9eSYbF4kC8rQmmxBb3TT3aPZagHKQV2lNcOvnV87woWQ4ZhmEM4jhguz22gpKwOhjwDquw1KLf6cPXmHfi8bYhOVsDhbIVBq0bbwD2UiJu63e3Bv/7Tr1Fc3QlVWpyQyBykJxahpdmLCCGGkZdSYLWa3hDD59Dl5IuK3Qe7fTec1UKICgqhUOjEsh5oiyrF6zVQqx3ISY6FsdwL596yFJ66dkRd+gw//7crKCt3IEVIT1JcGpz1HUgKT0NaugIllfXQqUtRIC/H9MKLHx1noAiWGE6PL8NsbsTMwibq9o5RlhANR+MY5GmZb4nh7Uwr6hw1+OrX/4jPruWgWFUEZXE17sSkwG4qQ2yWFclJsaiqdEBdVA1tWf2PxDA2UYO2zn5RbtlIjEs5WH9qdg6K5PaA+xgogiWGtXo1bkZlIf72bdyJz5OOqaFrBXm5qr1zxIGo8FyYSyqRX2gRAuNBekwCcjISociVIzJZC7UQw0JjLawlu2Xy1TeXxb/ifHZ6hAjGQJuXi8bOOeg0BhSU1KO61ILSSi8qDEbcSZBL26n39GDtiN9xsMSwrkJ89ykpKDB3wmLQ4ctvriAjtwyZyXIotKUwacuRp7MhLjIcNu8UKpTJiE/LgaxAnEdxsbgWLsf4+BKyEyNxLWa3rCnCI6LRPLiNiXYv0vKde+W+G81OK5TWQWjyCqRl0/NtsBq08NXWoNg1CqPJtbd/OzAX5KFI50CZToPIxAJxjykU25IjR4hneaUVlbbW3W2mJeGLWxkwVzRh/QfHeFiwGDIMwxzCcZuSu912fBOdj8HmZphrR5EhV2N5YhopyjL4/fMw5WcgMbcS/rGHmJtah0qWiah4GQzGclSZa2FRVsD7phhW2+HoWJPWs7H2GKUFJcjbq2BUuQooDN2YGh6HraoJ9to+FJY6pf1YEaKkzc9FcVEdrKUO5OUokSdkx+dthdFgFtJTi0KFGfkmN1RKPbpb2nHj69tISMgQ29LgyheXkKkwYGTq8Y+OMVAESwwX57chVxVjfHAIWrUHfX1zqKt2o6d3BqWWVvS09yFCiFC3eL3SVo/6mmboNRqYKhtE5atDqbsVclk5LJpcXI4rQuydq8hNS8GX1xOEZLvg7xuDOq/ooFKnMNl7MDswgG8uh+2JYSxux2kwN3O0sqIIihgub8NS3YNV8f8fZgyVWVlQyvKQIC+GLMsEo7oQ3SMbcLm8khiqZGkHYmhwdkGZVwGH2SyVx+24fKyIdbTVOfHl55eRlXQXf/zqJuSKYsQnFsLfO/iWRN+NSkZt6/yRv+NgiaHXWYPo8FtIyNCha2AdBUVGFORqkZGWD2ORGbkZ6fD2rGFseBTqTA0Sb91GckYuZHlliItKgDpfiLUo74neLlz6+goy5bvnT7ayBEPTzzDS0Yrs3OKD8yopKQudDR4oLX6oDG5pH7LleQdiWOQcQKG4/o1FpWL5YnGPiEZr8xBKinWITClEQlrxrhhma1BVqkFF7ZC0Xq1GDVlh14+O713BYsgwDHMIxxPD12ixVeDWHTnGV55joG8F6XtiSE3JS/NbsIibPDUlryyuo6VlCeUGA6bnHqPcZEJOuh72IoeQORWu3UrCja9uIzU9HXFpWnx1OxnVpt2KeD/slVZk6NtRKdZx65sriIyOxZff3JHeKzJ5UKjVQasqFPtgRrW+HJXFRhSbbPjqi8u4celbaBwTqMhJQFxhC9Y3XqBCZRMVkg43v7kqreN2WCom558HOM4fRzD7GBYL0R3o7kFl5bD099LcGhqaJuCzlCBHUYY/XRVlczcFte2LcOny8O3lSHwnyss3eA/Rn38Oa/cDLA31w+BoROLlW7hyVy4df1yKEuZiA658e11U9npR6cbhO1EmelsXOurrkCuEOu72FcRn6qEQ4t08fLRmd4pgZQzHe/ulY/s+Y2hC1/B9KIVoLE/NIyNfnFOx36DQ1IVaVzMycgpRXqCATFkOr92G5KwiWJqnoc7Vo62hFcW2dlQX6tHav4TOvhnIs4vgNhWiqMQOra4cmso2bIwNS+fp/j70OPSwdB5dooMmhiVq5BZakZCSL2Q5GT//+e/F+aTHrVsRSMrQIDUpBqrSRlSZipGYVIE2u5A+dbmQNz1SstRITcuGu3kK5XoN/vDrP+BGZCri7sbiD1/dgc/Xg3zxPdB3QaHR2qBVFqCrsUESw8T4JOn1azEySQybvT7oLQ0wVO434VPGUI2WmlrkFNcKqUyD0daDkjwDcmVFaHVXotDchcoqN9qGl8RDpBVjXd1HztKyGDIMwxzCccRwenQOakMNJvr9qGtZQENdC2LSNVgYGROiFwVDaS0qirXIN3pg0Rciv6wFOTlq+LvGoFYbYCi2QKEph728Fo7mCbjLqoWQmNHe6sfdFNXBdpaml6VmJ5OmABVN8/DV+2Auq0J+QTnKjRboiqvR2DyC/FwFqs0V0Jc3QZlTjKm5VahkOqTcjcblW4ko1pUhU6w/T65DfUMXMiPTkKO3IvLKdalSOh9iuANVRjLKbLXISi2UysUsRFmea4Zaq0duhgpp6ko0VrtRXFKHuqpa3PzuNiISsqHL16JAX4lseZnUlBwdnQStxnggholZOnR5W6AUFS/tz1RXKwrtA9J2WxtbMDH/SjwIGFArvitlag7aJ47W7E4RFDFcfYq6mjapjNSpqchUV0v/d5ZboCqphV6mRlxSDpRZMmTJDEhJTIVMaxJlWS49lFRYu6DNzYO9bQyRV2NhNJThbmw67BY7IoTAFOsdSE4vgdOgQI7KjNTYTGhKvZj29+Pbm8kHEpQcce1ciGGH04bouAyEhacgN9cIl6cFOr0byalqzIv3tZnxkCvomAoOxLBp+Dmsej0y5UUw27swNbEOU5kd1jIrLl++iSvfXIa2xIOG1um9c/wlOsS1Z6/2CKk2YbSzAXdEGWp01CXEh3xxX0hPTkG3OLfK8mWw13TvleNuxjBXXgK9oRR6s0csq0ODWFdGUgE8FWbEpOqF0GYiWWFHcb4SeeI7ZTFkGIY5IccRw5XlF1hcfiX9f33tBYYH5zE6+RBrK88wtdfMuDz/UGrupGblycl7GB69j/np+5jd68u3NLeJ0QnxGVrH+ivMzz/BzMQKRsY3D7azurgtraOvfxkrq68wMjiH8envK9oV8f7YxBbm5ndfm51cx/DYA6l/0fL8FqZmn0qvT42vY0VUFOsrTzA2uorBoVXMLz7DzMxD6f0F8fm19aMde/DE8DVmxf6ur+2WA5VL/8AipqYfYmXtJYb9c5ic2e2EP0PlII5xYeWVtPzExH2pTJbmNzEgPjMiymh56THmll5Ky8/NibIa38DS6q54ra08xdzC26K8vLCN5bU39+doERQxFOUxOLCwe+68EQP9i1gUZUJlOT31AKtrrzExuoyJyU0hNhtYXBWfXX+JSXG++AeWsLTyArNzTzE+vIgZqTxeY3x0BeNjaxifeoSFmXsYEuc1le3M1H0sLj7FtFh+fz9WFh9iYfno3TOCJYb7QQPFxqd2Pzc7dU9c05vSsc3Pbe+dpzuYm30inQtLohxHhxYxv/z9dzI5toSh4XvSNS2V8fjqwfVInx0fXpK+hwlxDS+PT6DC3f/W9+NyeDAuHkJmZrYO1kkxP7uF6dlt8T09kP5eXniI0RFa9yYW5x6Lcn+EAXG+r4p9XF16JM7v7+8h7wsWQ4ZhmEM4bh/DUI1gNiWfxwhWU/J5jGCLYSgGiyHDMMwhsBgeLVgMjxcshkePDxXDly9f4t69e5iamkJPTw+aGlrQKco90DY43g4WQ4ZhmENgMTxasBgeL1gMjx7vEsPXr1/j/v37GBkZgdfrhc1mOwi73Y7m5mYMDg5ifn4eK0sb6PJyxvAowWLIMAxzCCyGRwsWw+MFi+HRY7DzMYb9s+jv70dTUxOqqqoO5I/+T6+Nj4/j4cOHe6USGG5KPnqwGDIMwxwCi+HRgsXweMFiePQY6HiEjuZBzMzM4MGDB9jZ2dk7+uPBYnj0YDFkGIY5BBbDowWL4fGCxfDowYNPPn2wGDIMwxyC37clKpOH6Gnc5nhHdNZvYbL/dMSws/ZBwG1cpGhzPtg72pMxPfhISGbwzs9uIfGtNRtoqF5ETcU06iyzaLQvidfW0VF/H13eTWmZQJ89arTYH2Dr3snF8OXzHTRU3Au4jUDR6XkgjmcO7XVH/8xZjPa6+9Jx0PEEej9QUJk/fcRiyDAM8yNevXiNF6JC4Xh/vHr5eq/UTsbLECnz02Dn1acpq6dPXmBleR0jQ2NobelAfW0D7FUuOKrdcDlr0dTYgp6uPoyPTWF1eQOPHz0LuJ4PjZ2d0zm3Aq37h7G2ck86HqulGj3dfjzcfBxwufMS21tP0N3ZB0uFDb7GVty/txVwuR/G61Mq82DAYsgwDMMwp8CLFy+kEbw0oKO6uhpWq/WtEb5DQ0NYWlqS+vo9ffpUGhV8UVhdXYXD4ZCOlcqApru5SNDxLCwsHAzcWVtb23vn4sFiyDAMwzDv4dWrV3j8+LE0tx+JD43ybWhoeGuUr9PplOb8o/dp+YsOyS1NZ0MC7PP58OTJk713LjbPnz+X5J++c5J9KoeLBIshwzAMw7wBCQ5lh/x+vyQ8NTU1kgTQv/R3b2+vNMp3c3Pzg0f5nmcePXokZUBJhEmMQkGCA0FZRJoDkrKkdF5sb2/vvXO+YTFkGIZhQhISGmrabWlpkTJ/+02/JICU+ZudnZUyhCSKF6nZ90Mh8XG5XFJZUbmFohQHgsphcXFRKhdqTidxPs+wGDIMwzAXFur3R5k9at7t6+tDfX09KisrJQGkfoCdnZ3Seyw5h0PlR0JoNpuxtXU6UwVdVEieKyoqpPKivqTnERZDhmEY5kJATXuUySIBbGxslCpnauajvoCUAaTm3/OezfmUUN85aiKlJuN1mhyTOTIbGxtSP0Qqv2fPnu29ej5gMWQYhmHOJZTJov5+1ARMQRJIGUBqAqbfAKZBAsyHQT+TR5lVaiLlZvQPg8ptbm5OyiBOTU3tvXr2YTFkGIZhzjRUwVITHWX8qD+gxWKRmoK9Xq/UDMycHiTTVK5tbW3cvH5K0PlL5y31XT0P0/iwGDIMwzBnDhoYQtkWagamDv11dXXSKGFq0uQM1seBmjxJuicnJ/deYU4TymSTHJ71TDaLIcMwDHNmWF5elgaF0AhP6hdIfbUu2mTJZxEScWrypPJnPh40ETj1fT3LDzcshgzDMExQoUEOHR0dkphQkxuNJGY+LTRgh+bkYz4+w8PD0kPPWYXFkGEYhgkKlDUZGxuTBo5MT0/vvcp8aqgvIfXZDNWJqoMBNdmfVVgMGYZhmKBAWSrKEHJTcXChvoXUh5P5dNAUQGd16iQWQ4ZhGOaTQiJIEyWXlJTwvIJBhqSQsrZutztkfus42FA5008KntXfWWYxZBiGYT4pNNr4l7/8JX76058iPT2dRxkHka6uLvzDP/wD/v7v//5MN29eJOjXd/73//7f+D//5//A4/HsvXp2YDFkGIZhPinUly08PBw/+clPpF+GYILHw4cP8bOf/UySFB6R/Gmg39/+X//rf+EXv/jFmfzZPBZDhmGYC8jL569xb/k5NpbOZnQ2j+LynyKwNPMw4PtnITbXX5xKNvPJw1cB139WQq+xIjO5IOB7ZyUebZ3OwJiH914EXP+nDnmGDoWqioDvfep4tPl2H18WQ4ZhmAsI3fA76h5ipO/p2Y3eJ4FfPyPR4X6A1zsnF8OJvkfob3sccBtnJs7wdzHQ8QRD7Q/3SvNkdNU9wHDPGTnW3gCvfeIY7n6CvsatvdLZhcWQYRjmAkJiOCpu/Bsb9IP+HB8SvQ1bpyKGk/5HWJx7FXAbHO+PpYUdjHRt75Xmyehr2sLa2uuA2wnFWFt9jYGWt6WbxZBhGOYCwmJ48mAxPBvBYvjxgsWQYRgmRGAxPHmwGJ6NYDH8eMFiyDAMEyKwGJ48WAzPRrAYfrxgMWQYhgkRWAxPHiyGZyNYDD9esBgyDMOECCyGJ4+LKIYL0ytw2X2wvxHNXUsBlz0rwWL48YLFkGEYJkQ4nhi+RmutF6l37yA2vQiKrBxcj0xDmdOPZnc9ukYfwVRux3j/MAqL3Vh6o2L1VZnRNva99LTXupGWoYFKZYBKoUKuvk163eOswcjMSziK8xCdUgCV0Yv1jVcoy8oS2xR/0/J7UVXVhPKiGhhK7WhoGUeNd0hax9z4HJx1rZDF5OwtW4R0hQ2z/aMoNDXDXm7ZfV2RB0VJB7KSYqDIyESSzID0xGTEpBagvn7yYF/fF0ERw7VnMFXUIi8jG7mqQmRmm+DxdqNQqYetugure8s5HR6U6EvRP/Vy93PrL2HQWLGwvx4RVoMWObnFUpnkpovvs2ERtRXluBuZgDtht/G7315BpPh/Ulo+qivrce3qDcjzjHA7msRnSqEzt2JSlG2dbwoLo8MosYwcrHtxeh76kkbUVVXvfRcGtPatoNxoR3Z8OuRyLXLVJvicVdBUNqPS5d/77A7qK6ugK7Bjemlv398TwRLDmdEFaMVx6coaUKlWIDJBgVJHHzZWH6NEY8P06g42ZhZgcO6en0NdvXBYxDlo8KC5pQ+VNaO765KWGYYqK+WgrKIyjZifmIWmxIP6asfB69n5ZdClZUOmVCEpXo3MpDTEpeTBJK7Fam0B4mVW+DvaEBaWCFlKAkwNsxgYeLC7v8MTcNdMHez/UYLFkGEYJkT4kIxhR2UhnL3PsTQ8iFwTCd1LGOW5kMnzcPla7EHlVWztPPhMQWIEPAPPDv4OJIarwlbkSZmIjkpGZooGiUmpUBY3obfRhT/99jKyFXqxPEU+7sQUw2s145//4ef45lYydBo14lLzcenbGBRrlLgRk4SIS/FQKrTIyMyTxNCgrUBnSztyc4uQKy9Ca9egqHDbcffq5wi/dgWJqTn46nYGUmNiUOAYP9jX90UwxHBl/h6qyl2IiZEhN1MplWNqkho5mVpYK2tgrbBJr924dgtp2UWoa12QPre2/BiRN+5iZu37df1IDBuXsTS9CrVSjVxZNq5dThbfkQaW+im01bqgLLIiIUENrTwb7uY59PXOQpeTgrB4FYrkafj6SjKy5GUYn92Eqagc1c5m9PbNw++f242uAZQK6UsT+5uSKIcqMwHyTPEZYyuqzNUwl1SJfSnG7UtXkSE3YGBs+2Bf3xXBzBhadSaUlhTjm++ikRwVjqtRMtT6JtHb3oue8adYHR9Dbtnuw09HnRu//7//D353KRnavCIkpinx9dVwGNWF+PpmJsIj70ClLBTXh1oSQ4fJgM7WXmSLc1Yhy4e3awNFhRrIwsJwK+IOboRlISY8CulZGUiKycd3317BV19dRkmpE/lqCxSJ6fCNbMGot8Lt9CAjPg5XxHl+Iy4Xa+uBj+eHwWLIMAwTIhxHDNdXX8BWXoWEm1dxN00nScSX1+Oh1Tkxuwp01VhxO0pU9EIwYqOzMCwqaumzq5uIDY+Bwdazt64XMOdpkJu3K5AUURnF6PM4EB6djcTEQpToShB2R4idqDjbhu6hNDsLYTHZe8sXQlZQg/pKO3KytcgTr8ly8+GweqAytKHeUoa7SemIupKInPR0XLoaA5WuBjohhq4yGzz+e+irr4HJ3Q6N1o2c1FQpI1YkhDO5wIFSVQlK6s54xnD9BZxWL5KTlFDI1VCLMiAxzEjKRlZmLlpaBqTmX7mQ7+6Jxwefm+5uwc3wNHh77u++tnAfsqw8qQx3y7YIOWo75oZGEBYRIwR6Twwz0pCo8aFUrcTv/+13uHYnEWkJachMSkS62oPmGjcS5OVwmw0otIyhzGjB6OwTVBdqoatsg9vVDGtpGaLTtWhoHRUCaBXfjZD2VNWBGCbnVyMtswTtLb1i35uhypHBP7V3Dh0hgiWGSxOL0vkTk5yDyDt38NXX3yItORV5RXWIiM/EkpCqya4ORCYbhIi9RktdAzS5uchRmJCdoUC5txcKZTk6axy4FJ6H6LgIqLKz8ZmQzILCSpQUGdBYZYKx+T6Gmuthdk9Cm5+HIlE+tN30XANK8jXQ2WvFa05E3bom5PwGIm4nCfGWQVFYhe6B++ht7kFNbTuMBWokZZkRFZOIkZG98+A9wWLIMAwTInxIxtCl1qN95sUbGUPx+tIWjJYGuKtr0N45CLmyCqt72QinoRC13Ruw6c3oHX8iXnsmKrBiTK8Aer1YTiyToyjA4swaCvMLkZKshkzITUpiPvJVdkzOC3mJvIR//kO4JDCJkWFIyPeiQp2JG5HZuHEtFgNTG4i6dh31PffQ4/OhrNQqlovEnUQVMjLlyFF5pYyhq8yImBQVkqPjdsWwuBHVVW5EXLsFdYEZEeFRSE0vQG3X2sHxvi+C1cfQVW6HzliDlGwlGut9QgrVUCrKUVligMfbI17XwmUwob57Hd09JLpPoUxSYXT6Pgp0FiyKyn5jdhnZBS4srzyB0WQXyzyBPLvo+4zhvhgqtLB4ptDkdCM6KhF5BjfKCyrhsZthbVqBw1iM0uouVOWl4FpcMRQqIwZGFmBUl8HVMC/t7/L4JIrt4uFg/RWsxSWoKLMgKd0ijsMoiaHS2g+9rgJNvh4hTDrYSkvR1LuIyYmznTEcG11ATq5JyJ0St2/eOBDDsppZKJWZ0KmqYRFSrFbp0TP5HLVGHa6HJSM2MgqN/g2kXr+B8uYlTHR1Qm5uR07sbdyNyJAyhklCGCljSGJ4PS4fabF3UeyYhE6jhdfRgtiwS0hRmpARHYNkcd46qnsOxPCb33+JP372Nf79qxvQlgqhrPRieHAB6oR4pGjc6Oubw/z80a59FkOGYZgQ4bh9DAc7upCjdkpNUPtiuL72HNbCEtQ0jqJUoxcVWi5KylvRP7yGmjITCq2dWBefX53fgCIzG56OeVSZamCrtCA+Nge5ucWIS9VicvoeiopqsLh4H/KkLOTlG1Hh6kFHQztqG8dQlBGLX/3qd/jtd4lo65lFk7cNX/zhX/DddRksxnKkxmdAaahHl68NyVHJKJALSYmn5up8ISAOSQz7+6YxOvsUq0uP0d/aAnmRHeFXbyIqQQurEJGK2mF0NLaivmVXZo4SQRHDlW3ky4thEoKg0FZCa25Af88AcpUVUN4Nh6LEi/iYBFSYbUiR6aFRV6BAoUZ9567wTvh7kJZWisH+KZRVNqBMo0RcilhXnhYZYh0t3ibERCbiTvh+H8NEZOULiXO1Ik+mRZ3bjQxtA3pdZtiaN9BZ50G52YWEpCKIrxmOYhPuJKjR2dyJGiGGq4vb6GpsRpG9DysL65DlaKGW56NYU4ZSez+63RVQ2QYRH3EDxRYf4sJjpKyjKjML3u7VHx9/gAheU/ILaMR5LcuWo7CiT0hxAZrcTlTWzaA4V4bKCid01g7xnW1CkV6EtpYufPNvv8cXN+JgM1cgOU2GdKUDQ20dSEzJQI68CNdvpksZ3Ij0QkkMe3sm0DvxDGsrT6XvTJ6dh9SoWNy6m4Yaiw1GWyf8XX6UGF0o0Fejrs4Fc/kIlob6YagZxdTAoPgeZuA0laLUOYKe5mboTQ2YW3gR4Hh+HCyGDMMwIcJxxXCobx5Lq7t/ry1tY2BsU1RWzzA4MANPbRdml3cr0+nhGbi9A+ju+8FI1tVHaGjsk0a89o0+PHh9emgG/pFNjIxsYHF6GXVCBNf23ltZ3EQtLb/X12xt8SHqaprh8PoxvbgjrbNFyCa9N+afQUNDF1o6pzA9vogxSbReYGTwHkaHVw7WuTS7Lu2Df2ABg1OPRUVJzZffj8Dt7l6RljtKBEUMV5+jv1+U2fDugIKR4Sl4XW3oG76P0cENqR9fe9/uMfR3DaKjYxKDE4/eWsf8xAJ8TX5xvK2YfEMQaHl3fedb5UHhcLZieHQFtd4hLM7ex8jMcyxMrWBi5ilaRZl3d09gfHZ3PcuifJs7Z8Vym5gS7y+J79Rub8HQ5CMhGU/QJ/ZnZPIxNtZ30Ns1jtoaH8bEcsOjq+hp6UXP0KZYz474fw9mF8/24JP9mB6ZF+c/MDgoHlrq2g/Kwt87c3DezU8sw1PTh4n556IcnqNLlANlzGfG5+Gp60ZT0zhmZh9gZHz3uxoeWcHk2BKW9665lXlxLbh86OhZwvjEJga6+t/6jmqahjA99wK9bd0YmHwqrtGHGJ3aXde8+K56h75vOqZrbpS+g72/3xUshgzDMCHChzQlc7wdwWpK5ng7gi2GFzlYDBmGYUIEFsOTB4vh2QgWw48XLIYMwzAhAovhyYPF8GwEi+HHCxZDhmGYEIHF8OTBYng2gsXw4wWLIcMwTIjAYnjyYDE8G8Fi+PGCxZBhGCZEYDE8ebAYno1gMfx4wWLIMAwTIpAYesrvoc29xfGB0WS9dypiON6zDV/Vg4Db4Hh/+Oyb6G/Z2ivNk9HmvI9W12bA7YRiUFn0eDf3SmcXFkOGYRiGYRhGgsWQYRiGYRiGkWAxZBiGYRiGYSRYDBmGYRiGYRgJFkOGYRiGYRhGgsWQYRiGYRiGkWAxZBiGYRiGYSRYDBmGYRiGYRgJFkOGYRiGYRhG4s/+7M/+7P8HcbxqEJHK8zgAAAAASUVORK5CYII=)

图3.3—— 数据测试流程图


<a name="_toc29937"></a>3.3.6 测试用例

数据测试用例表。

|测试编号|测试名称|测试目的|输入数据|预期结果|测试步骤|
| - | - | - | - | - | - |
|DT-01|学生信息数据完整性测试|验证学生信息数据的完整性|学号：2024140603、姓名：王五、性别：女、班级：计算机 4242|数据库中存储的学生信息字段完整，无缺失|1\. 登录系统，进入学生信息管理模块；2. 添加学生信息；3. 查询数据库，检查学生信息字段是否完整|
|DT-02|成绩数据完整性测试|验证成绩数据的完整性|课程编号：C002、学生学号：2024140603、成绩：90.0|数据库中存储的成绩信息字段完整，无缺失|1\. 登录系统，进入成绩录入模块；2. 录入成绩信息；3. 查询数据库，检查成绩信息字段是否完整|
|DT-03|学生信息唯一性测试|验证学生学号的唯一性|学号：2024140603（已存在）、姓名：赵六、性别：男、班级：计算机 4242|系统提示 “学号已存在”，数据库中无新增学生记录|1\. 登录系统，进入学生信息管理模块；2. 添加学生信息，使用已存在的学号；3. 查看系统提示，查询数据库|
|DT-04|成绩数据一致性测试|验证成绩数据与学生信息的一致性|课程编号：C001、学生学号：2024140518（不存在）、成绩：85.5|系统提示 “学生不存在”，数据库中无新增成绩记录|1\. 登录系统，进入成绩录入模块；2. 录入成绩信息，使用不存在的学号；3. 查看系统提示，查询数据库|
|DT-05|事务完整性测试|验证事务处理的完整性|执行学生信息添加和成绩录入的事务操作|学生信息添加和成绩录入操作要么全部成功，要么全部失败|1\. 登录系统，开启事务；2. 添加学生信息，录入该学生成绩；3. 提交事务；4. 查询数据库，检查学生信息和成绩是否都存在或都不存在。|

<a name="_toc23593"></a>3.4性能测试设计

<a name="_toc23405"></a>3.4.1 测试目的

评估系统在不同负载下的响应速度、资源利用率及稳定性，确保满足实际业务场景的性能需求。

<a name="_toc25036"></a>3.4.2 测试指标

**响应时间**：核心接口（如成绩查询、统计）在正常负载下响应时间≤2 秒。

**并发性能**：支持 100 个用户同时在线操作时，系统不崩溃、响应时间≤5 秒。

**资源占用**：服务器 CPU 利用率≤80%，内存占用≤70%，数据库连接数≤最大配置的 80%。

<a name="_toc6489"></a>3.4.3测试工具

**JMeter**：设计并发场景、生成负载压力，监控响应时间和成功率。

**Prometheus + Grafana**：实时监控服务器 CPU、内存、磁盘 I/O 等资源指标。

**MySQL 监控工具**：如mysqltuner，分析数据库连接、查询性能。

<a name="_toc19743"></a>3.4.4测试流程

**环境准备**：部署测试环境（同 2.3.4 硬件配置），清空测试数据。

**脚本开发**：使用 JMeter 编写接口压测脚本，模拟用户操作流程。

**基准测试**：单用户执行核心接口，记录基准响应时间。

**负载测试**：逐步增加并发用户，记录不同负载下的性能指标。

**结果分析**：生成性能测试报告，定位瓶颈（如数据库索引缺失、接口响应慢）。

<a name="_toc13113"></a>3.4.5 注意事项

测试前关闭无关服务，确保环境纯净。

性能测试需在功能测试通过后进行，避免功能缺陷影响结果。

若发现响应时间过长或资源占用过高，需结合代码分析（如 SQL 查询优化、接口缓存策略）。

<a name="_toc2714"></a>3.4.6性能测试场景设计图

展示性能测试的各个场景，包括用户数量、操作类型、数据量等。

![](data:image/png;base64,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)

图3.4 性能测试场景设计图

4. <a name="_toc32151"></a>测试实施

<a name="_toc13999"></a>
4\.1测试数据准备 

<a name="_toc7406"></a>4.1.1数据准备目的

为功能测试、接口测试、性能测试及数据测试提供全面、有效的输入数据和预期结果，确保测试的充分性和准确性，覆盖系统各种业务场景和边界条件，以验证系统在不同数据条件下的功能正确性、性能表现及数据处理能力。

<a name="_toc7823"></a>4.1.2 测试数据类型

基础数据：用于系统基础功能测试，如学生基本信息、课程信息等。

边界数据：用于测试系统在数据边界条件下的处理能力，如成绩的最大值、最小值等。

异常数据：用于测试系统对异常情况的处理能力，如非法的学号、成绩等。

大数据量：用于性能测试和大数据量场景下的测试，包含大量学生记录和成绩记录的数据集。

<a name="_toc446"></a>4.1.3 测试数据内容

学生信息数据

合法数据：

学号：2024140518、2024140525、2024140485 等，符合学号编码规则，8-12 位数字。

姓名：张三、李四、王五等，长度在 2-20 个字符之间。

性别：男、女。

班级：计算机 4241、计算机 4242 等。

非法数据：

学号：长度不足 8 位（如 2024140）、超过 12 位（如 202414051812345）、包含非数字字符（如 2024140518a）。

姓名：为空、长度超过 20 个字符。

性别：非法值（如 “中”）。

成绩数据

合法数据：课程编号：C001、C002、C003 等。

学生学号：存在于学生信息表中的学号。

成绩：0、85.5、100 等，在 0-100 之间，最多保留 1 位小数。

非法数据：课程编号：不存在于课程表中的编号。

学生学号：不存在于学生信息表中的学号。

成绩：-5、105、100.5 等，超出 0-100 范围或小数位数超过 1 位。

课程数据: 合法数据：课程编号：C001、C002、C003 等。

课程名称：软件工程、数据库原理、Java 程序设计等，长度在 2-50 个字符之间。

非法数据：课程编号：重复、格式不正确（如 C00）。

课程名称：为空、长度超过 50 个字符。

大数据量测试数据

学生记录：10 万条，包含不同班级、不同入学年份的学生信息。

成绩记录：100 万条，对应不同学生、不同课程的成绩。

<a name="_toc20589"></a>4.1.4 数据生成方法

人工生成：对少量的基础数据和异常数据，采用人工方式生成，确保数据的准确性和针对性。

工具生成：使用 DataFactory 数据生成工具，批量生成大量测试数据，提高数据生成效率。

数据库导入：从现有数据库或数据文件中导入相关数据，作为测试数据的一部分。

<a name="_toc10845"></a>4.1.5 数据验证方法

人工验证：对生成的测试数据进行人工检查，确保数据的格式、内容符合测试要求。

程序验证：编写程序对生成的数据进行验证，如检查学号的唯一性、成绩的范围等。

数据库查询验证：将数据导入数据库后，通过 SQL 查询语句验证数据的完整性和准确性。

<a name="_toc32390"></a>4.1.6数据存储与管理

存储方式：将测试数据存储在独立的数据库中，与生产环境数据库隔离，避免对生产环境造成影响。

管理方法：建立测试数据管理台账，记录数据的生成时间、用途、版本等信息，便于数据的维护和管理。

备份与恢复：定期对测试数据进行备份，确保数据的安全性和可恢复性。

<a name="_toc74"></a>4.1.7 数据准备进度安排

2025 年 6 月 16 日：完成基础数据和异常数据的设计与生成。

2025 年 6 月 17 日：完成边界数据的设计与生成。

2025 年 6 月 18 日：使用工具生成大数据量测试数据，并进行验证。

2025 年 6 月 19 日：将所有测试数据导入测试环境数据库，并进行最终验证。

<a name="_toc21429"></a>4.2硬件环境

服务器：CPU：Intel Xeon E5-2620 v4，内存：16GB，硬盘：500GB SSD。

客户端：CPU：Intel Core i5-7500，内存：8GB，硬盘：1TB。

网络：100Mbps 局域网。

<a name="_toc5378"></a>4.3软件环境

操作系统：服务器：CentOS 7.6，客户端：Windows 10。

开发环境：JDK 1.8，Tomcat 8.5，MySQL 5.7。

测试工具：Selenium 3.141.59，Postman 7.28.0，JMeter 5.4.1，Grafana 7.5.10，Prometheus 2.24.1。

<a name="_toc3422"></a>4.4测试步骤

<a name="_toc25266"></a>4.4.1功能测试步骤

搭建测试环境，部署系统。

执行测试用例，记录测试结果。

分析测试结果，提交缺陷报告。

开发人员修复缺陷，测试人员进行回归测试。

<a name="_toc3567"></a>4.4.2接口测试步骤

搭建测试环境，部署系统。

使用 Postman 等工具发送接口请求，记录测试结果。

分析测试结果，提交缺陷报告。

开发人员修复缺陷，测试人员进行回归测试。

<a name="_toc5943"></a>4.4.3数据测试步骤

搭建测试环境，部署系统。

**搭建测试环境，部署系统**：依据 4.2 硬件环境和 4.3 软件环境的要求，完成服务器、客户端的配置搭建，并部署基于 JavaWeb 的学生成绩管理系统。

**准备测试数据**：制作包含正确数据、边界数据（如成绩为 0 和 100）、异常数据（如重复学号、非法字符成绩）的学生信息表和成绩表，作为测试数据来源。

**执行数据测试**：

利用系统界面或接口录入测试数据，通过 Navicat 或 MySQL Workbench 等数据库管理工具，查询数据库验证数据准确性。​

对数据进行增删改操作，检查数据完整性和一致性，同时使用 Beyond Compare 对比原始数据与系统存储数据。​

模拟数据安全攻击场景，尝试非法访问数据库，使用 MySQL 自带的mysqldump命令或 Navicat 进行数据备份与恢复，验证数据安全性和恢复可用性。​

**记录测试结果**：详细记录数据差异、异常情况、系统响应信息，以及使用工具检测到的数据问题。​

**分析测试结果，提交缺陷报告**：深入分析数据问题产生的原因，可能涉及代码逻辑错误、数据库约束设置不当等，整理形成缺陷报告提交给开发人员。

<a name="_toc29783"></a>4.4.4性能测试步骤

搭建测试环境，部署系统。

准备测试数据，设计测试场景。

使用 JMeter 等工具执行性能测试，收集性能数据。

分析性能数据，生成性能测试报告。

提出性能优化建议，开发人员进行优化。

重新执行性能测试，验证优化效果。

<a name="_toc25665"></a>5. 评价准则

<a name="_toc26642"></a>5.1 范围

<a name="_toc26022"></a>5.1.1功能测试

所有功能点的测试用例执行通过率达到 95% 以上，严重缺陷和主要缺陷全部修复。

<a name="_toc1727"></a>5.1.2接口测试

所有接口的测试用例执行通过率达到 95% 以上，接口返回数据正确，接口调用无异常。

<a name="_toc30497"></a>5.1.3数据测试

**数据准确性**：录入数据与系统存储数据一致性达 100%，统计计算误差≤0.1%。

**数据完整性**：批量操作数据丢失率为 0，关联数据删除时无残留记录。

**数据一致性**：多端同步修改数据时，最终一致性时间≤5 秒。

**数据安全性**：敏感数据加密率 100%，非法访问拦截率 100%，备份恢复成功率 100%。

<a name="_toc1895"></a>5.1.5性能测试

在设定的测试场景下，系统响应时间、吞吐量、资源利用率等指标满足性能要求（如 100 用户并发时核心接口响应时间≤2 秒，CPU 利用率≤85%）。

<a name="_toc23812"></a>5.2 数据整理

<a name="_toc2255"></a>5.2.1功能测试和接口测试

使用 Excel 表格记录用例执行结果、缺陷类型（功能异常 / 数据错误）、修复状态，按模块生成通过率统计图表（如柱状图展示各模块用例通过率）。

<a name="_toc32697"></a>5.2.2数据测试

采用 MySQL Workbench 导出数据库表结构及数据快照，通过 Beyond Compare 对比原始数据与系统存储数据，生成差异分析报告（标注学号、成绩等关键字段差异）。

对数据完整性、一致性测试结果分类记录（如 “学生 - 成绩关联测试”“批量导入成功率”），形成数据质量评估表（含测试项、通过标准、实测结果）。

<a name="_toc27536"></a>5.2.3性能测试

使用 JMeter 生成吞吐量、响应时间趋势图，Grafana 可视化服务器资源占用曲线，结合 Prometheus 数据生成性能瓶颈分析报告（附 SQL 优化建议）。

<a name="_toc14333"></a>5.3 尺度

<a name="_toc24016"></a>5.3.1功能测试

重点验证学生信息管理、成绩录入等核心流程，覆盖 90% 以上用户高频操作场景，确保表单校验、提示信息等细节功能正常。

<a name="_toc23097"></a>5.3.2接口测试

关注接口鉴权机制（Token 有效性）、参数边界处理（超长字符串 / 非法格式），模拟网络波动测试接口重试机制。

<a name="_toc11720"></a>5.3.3数据测试

**准确性**：人工核对 10% 的录入数据与数据库记录，重点检查学号、成绩等字段，确保系统存储与原始数据一致。

**完整性**：通过数据库约束（外键 / 非空字段）验证数据缺失情况，批量导入 1000 条数据时无记录丢失。

**一致性**：模拟 3 个客户端同时修改同一学生成绩，验证数据同步延迟≤5 秒。

**安全性**：通过渗透测试工具尝试注入攻击、越权访问，验证敏感数据（如身份证号）加密存储。

**性能测试**：重点关注 100 用户并发（正常负载）和 200 用户峰值负载下的系统表现，确保响应时间、资源占用等指标达标。


17
![](data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAcAAAAaCAYAAAB7GkaWAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAAgSURBVChTYxhg8B9KYwCQBE5JEBg6kiAJGB4FAwsYGABH8Av1Ac/E5AAAAABJRU5ErkJggg==)
