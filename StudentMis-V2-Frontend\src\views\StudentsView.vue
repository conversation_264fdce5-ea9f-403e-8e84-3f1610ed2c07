<template>
  <div class="students-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1>学生管理</h1>
        <p>管理学生基本信息、学籍状态和档案资料</p>
      </div>
      <div class="header-actions">
        <button class="btn btn-primary" @click="showAddDialog = true">
          <span class="btn-icon">➕</span>
          添加学生
        </button>
        <button class="btn btn-secondary">
          <span class="btn-icon">📤</span>
          导出数据
        </button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-section">
      <div class="search-bar">
        <div class="search-input-group">
          <span class="search-icon">🔍</span>
          <input
            v-model="searchQuery"
            type="text"
            placeholder="搜索学生姓名、学号或专业..."
            class="search-input"
            @input="handleSearch"
          >
        </div>
        <div class="filter-group">
          <select v-model="filters.grade" class="filter-select">
            <option value="">全部年级</option>
            <option value="2024">2024级</option>
            <option value="2023">2023级</option>
            <option value="2022">2022级</option>
            <option value="2021">2021级</option>
          </select>
          <select v-model="filters.major" class="filter-select">
            <option value="">全部专业</option>
            <option value="计算机科学与技术">计算机科学与技术</option>
            <option value="软件工程">软件工程</option>
            <option value="数据科学与大数据技术">数据科学与大数据技术</option>
            <option value="人工智能">人工智能</option>
          </select>
          <select v-model="filters.status" class="filter-select">
            <option value="">全部状态</option>
            <option value="在读">在读</option>
            <option value="休学">休学</option>
            <option value="毕业">毕业</option>
          </select>
        </div>
      </div>
    </div>

    <!-- 学生列表 -->
    <div class="students-list">
      <div class="list-header">
        <div class="list-stats">
          <span class="stat-item">
            <strong>{{ filteredStudents.length }}</strong> 名学生
          </span>
          <span class="stat-item">
            在读: <strong>{{ activeStudents }}</strong>
          </span>
        </div>
        <div class="view-controls">
          <button
            class="view-btn"
            :class="{ active: viewMode === 'grid' }"
            @click="viewMode = 'grid'"
          >
            📱
          </button>
          <button
            class="view-btn"
            :class="{ active: viewMode === 'table' }"
            @click="viewMode = 'table'"
          >
            📋
          </button>
        </div>
      </div>

      <!-- 网格视图 -->
      <div v-if="viewMode === 'grid'" class="students-grid">
        <div
          v-for="student in paginatedStudents"
          :key="student.id"
          class="student-card"
          @click="viewStudent(student)"
        >
          <div class="card-header">
            <div class="student-avatar">
              <img :src="student.avatar || '/default-avatar.svg'" :alt="student.name">
            </div>
            <div class="student-status" :class="student.status">
              {{ student.status }}
            </div>
          </div>
          <div class="card-body">
            <h3 class="student-name">{{ student.name }}</h3>
            <p class="student-id">学号: {{ student.studentId }}</p>
            <p class="student-major">{{ student.major }}</p>
            <p class="student-class">{{ student.className }}</p>
          </div>
          <div class="card-footer">
            <button class="card-btn" @click.stop="editStudent(student)">
              编辑
            </button>
            <button class="card-btn" @click.stop="viewGrades(student)">
              成绩
            </button>
          </div>
        </div>
      </div>

      <!-- 表格视图 -->
      <div v-else class="students-table-container">
        <table class="students-table">
          <thead>
            <tr>
              <th>头像</th>
              <th>姓名</th>
              <th>学号</th>
              <th>专业</th>
              <th>班级</th>
              <th>年级</th>
              <th>状态</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="student in paginatedStudents" :key="student.id">
              <td>
                <div class="table-avatar">
                  <img :src="student.avatar || '/default-avatar.svg'" :alt="student.name">
                </div>
              </td>
              <td class="student-name-cell">{{ student.name }}</td>
              <td>{{ student.studentId }}</td>
              <td>{{ student.major }}</td>
              <td>{{ student.className }}</td>
              <td>{{ student.grade }}级</td>
              <td>
                <span class="status-badge" :class="student.status">
                  {{ student.status }}
                </span>
              </td>
              <td>
                <div class="table-actions">
                  <button class="action-btn view" @click="viewStudent(student)">
                    👁️
                  </button>
                  <button class="action-btn edit" @click="editStudent(student)">
                    ✏️
                  </button>
                  <button class="action-btn grades" @click="viewGrades(student)">
                    📊
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页 -->
      <div class="pagination">
        <button
          class="page-btn"
          :disabled="currentPage === 1"
          @click="currentPage--"
        >
          上一页
        </button>
        <span class="page-info">
          第 {{ currentPage }} 页，共 {{ totalPages }} 页
        </span>
        <button
          class="page-btn"
          :disabled="currentPage === totalPages"
          @click="currentPage++"
        >
          下一页
        </button>
      </div>
    </div>

    <!-- 学生成绩查看对话框 -->
    <div v-if="showGradesDialog" class="modal-overlay" @click="closeGradesDialog">
      <div class="modal-content grades-modal" @click.stop>
        <div class="modal-header">
          <h3>{{ selectedStudent?.name }} ({{ selectedStudent?.studentId }}) - 成绩查看</h3>
          <button class="close-btn" @click="closeGradesDialog">×</button>
        </div>
        <div class="modal-body">
          <div class="student-info">
            <div class="info-item">
              <span class="info-label">专业：</span>
              <span class="info-value">{{ selectedStudent?.major }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">班级：</span>
              <span class="info-value">{{ selectedStudent?.className }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">年级：</span>
              <span class="info-value">{{ selectedStudent?.grade }}级</span>
            </div>
          </div>

          <div class="grades-table">
            <table>
              <thead>
                <tr>
                  <th>课程代码</th>
                  <th>课程名称</th>
                  <th>学期</th>
                  <th>平时成绩</th>
                  <th>期中成绩</th>
                  <th>期末成绩</th>
                  <th>总评成绩</th>
                  <th>等级</th>
                  <th>绩点</th>
                  <th>学分</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="grade in studentGrades" :key="grade.id">
                  <td>{{ grade.courseCode }}</td>
                  <td>{{ grade.courseName }}</td>
                  <td>{{ grade.semester }}</td>
                  <td>{{ grade.regularScore }}</td>
                  <td>{{ grade.midtermScore }}</td>
                  <td>{{ grade.finalScore }}</td>
                  <td>
                    <span class="score-badge" :class="getScoreLevel(grade.totalScore)">
                      {{ grade.totalScore }}
                    </span>
                  </td>
                  <td>
                    <span class="grade-level" :class="getScoreLevel(grade.totalScore)">
                      {{ getGradeLevel(grade.totalScore) }}
                    </span>
                  </td>
                  <td>
                    <span class="gpa-badge" :class="getScoreLevel(grade.totalScore)">
                      {{ getGPA(grade.totalScore) }}
                    </span>
                  </td>
                  <td>{{ grade.credits }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" @click="closeGradesDialog">关闭</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, onMounted } from 'vue'

// 数据类型定义
interface Student {
  id: number
  name: string
  studentId: string
  major: string
  className: string
  grade: string
  status: '在读' | '休学' | '毕业'
  avatar?: string
  phone?: string
  email?: string
}

// 响应式数据
const searchQuery = ref('')
const viewMode = ref<'grid' | 'table'>('grid')
const currentPage = ref(1)
const pageSize = 12
const showAddDialog = ref(false)

const filters = reactive({
  grade: '',
  major: '',
  status: ''
})

// 模拟学生数据
const students = ref<Student[]>([
  {
    id: 1,
    name: '张三',
    studentId: '2024140520',
    major: '计算机科学与技术',
    className: '计算机4241班',
    grade: '2024',
    status: '在读',
    phone: '18626425051',
    email: '<EMAIL>'
  },
  {
    id: 2,
    name: '李四',
    studentId: '2024140521',
    major: '软件工程',
    className: '软件4241班',
    grade: '2024',
    status: '在读',
    phone: '18626425052',
    email: '<EMAIL>'
  },
  {
    id: 3,
    name: '王五',
    studentId: '2023140520',
    major: '数据科学与大数据技术',
    className: '数据4231班',
    grade: '2023',
    status: '在读',
    phone: '18626425053',
    email: '<EMAIL>'
  },
  {
    id: 4,
    name: '赵六',
    studentId: '2023140521',
    major: '人工智能',
    className: 'AI4231班',
    grade: '2023',
    status: '休学',
    phone: '18626425054',
    email: '<EMAIL>'
  },
  {
    id: 5,
    name: '钱七',
    studentId: '2022140520',
    major: '计算机科学与技术',
    className: '计算机4221班',
    grade: '2022',
    status: '在读',
    phone: '18626425055',
    email: '<EMAIL>'
  }
])

// 计算属性
const filteredStudents = computed(() => {
  let result = students.value

  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(student =>
      student.name.toLowerCase().includes(query) ||
      student.studentId.includes(query) ||
      student.major.toLowerCase().includes(query)
    )
  }

  // 年级过滤
  if (filters.grade) {
    result = result.filter(student => student.grade === filters.grade)
  }

  // 专业过滤
  if (filters.major) {
    result = result.filter(student => student.major === filters.major)
  }

  // 状态过滤
  if (filters.status) {
    result = result.filter(student => student.status === filters.status)
  }

  return result
})

const paginatedStudents = computed(() => {
  const start = (currentPage.value - 1) * pageSize
  const end = start + pageSize
  return filteredStudents.value.slice(start, end)
})

const totalPages = computed(() => {
  return Math.ceil(filteredStudents.value.length / pageSize)
})

const activeStudents = computed(() => {
  return filteredStudents.value.filter(student => student.status === '在读').length
})

// 方法
const handleSearch = () => {
  currentPage.value = 1
}

const viewStudent = (student: Student) => {
  console.log('查看学生详情:', student)
}

const editStudent = (student: Student) => {
  console.log('编辑学生:', student)
}

// 成绩查看对话框相关
const showGradesDialog = ref(false)
const selectedStudent = ref(null)
const studentGrades = ref([])

const viewGrades = (student: Student) => {
  selectedStudent.value = student
  generateStudentGrades(student)
  showGradesDialog.value = true
}

// 生成学生成绩数据
const generateStudentGrades = (student: any) => {
  const coursesData = [
    { id: 1, code: 'CS101', name: '计算机科学导论', credits: 3 },
    { id: 2, code: 'MATH101', name: '高等数学A', credits: 5 },
    { id: 3, code: 'ENG101', name: '大学英语', credits: 2 },
    { id: 4, code: 'CS201', name: '数据结构与算法', credits: 4 }
  ]

  studentGrades.value = coursesData.map(course => {
    // 生成成绩
    const baseScore = 70 + Math.random() * 25
    const variation = (Math.random() - 0.5) * 10

    const regularScore = Math.max(60, Math.min(100, Math.round(baseScore + variation)))
    const midtermScore = Math.max(60, Math.min(100, Math.round(baseScore + variation)))
    const finalScore = Math.max(60, Math.min(100, Math.round(baseScore + variation)))
    const totalScore = Math.round(regularScore * 0.3 + midtermScore * 0.3 + finalScore * 0.4)

    return {
      id: `${student.studentId}-${course.code}`,
      studentId: student.studentId,
      studentName: student.name,
      courseCode: course.code,
      courseName: course.name,
      regularScore,
      midtermScore,
      finalScore,
      totalScore,
      credits: course.credits,
      semester: '2024-1'
    }
  })
}

const closeGradesDialog = () => {
  showGradesDialog.value = false
  selectedStudent.value = null
  studentGrades.value = []
}

// 成绩相关工具方法
const getScoreLevel = (score: number) => {
  if (score >= 90) return 'excellent'
  if (score >= 80) return 'good'
  if (score >= 70) return 'average'
  if (score >= 60) return 'pass'
  return 'fail'
}

const getGradeLevel = (score: number) => {
  if (score >= 95) return 'A+'
  if (score >= 90) return 'A'
  if (score >= 85) return 'A-'
  if (score >= 80) return 'B+'
  if (score >= 75) return 'B'
  if (score >= 70) return 'B-'
  if (score >= 65) return 'C+'
  if (score >= 60) return 'C'
  return 'F'
}

const getGPA = (score: number) => {
  if (score >= 95) return '4.0'
  if (score >= 90) return '4.0'
  if (score >= 85) return '3.7'
  if (score >= 80) return '3.3'
  if (score >= 75) return '3.0'
  if (score >= 70) return '2.7'
  if (score >= 65) return '2.3'
  if (score >= 60) return '2.0'
  return '0.0'
}

onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
.students-container {
  padding: 2rem;
  background: #f5f7fa;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  background: white;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.header-content h1 {
  font-size: 2rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.header-content p {
  color: #666;
  font-size: 1rem;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

.btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 10px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
  background: #f8f9fa;
  color: #666;
  border: 1px solid #e1e5e9;
}

.btn-secondary:hover {
  background: #e9ecef;
}

/* 搜索区域 */
.search-section {
  background: white;
  padding: 1.5rem;
  border-radius: 15px;
  margin-bottom: 2rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.search-bar {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.search-input-group {
  position: relative;
  flex: 1;
  min-width: 300px;
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 3rem;
  border: 2px solid #e1e5e9;
  border-radius: 10px;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filter-group {
  display: flex;
  gap: 1rem;
}

.filter-select {
  padding: 0.75rem;
  border: 2px solid #e1e5e9;
  border-radius: 10px;
  background: white;
  cursor: pointer;
}

/* 学生列表 */
.students-list {
  background: white;
  border-radius: 15px;
  padding: 2rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e1e5e9;
}

.list-stats {
  display: flex;
  gap: 2rem;
}

.stat-item {
  color: #666;
}

.view-controls {
  display: flex;
  gap: 0.5rem;
}

.view-btn {
  padding: 0.5rem;
  border: 1px solid #e1e5e9;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.view-btn.active {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

/* 网格视图 */
.students-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
}

.student-card {
  border: 1px solid #e1e5e9;
  border-radius: 15px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fafbfc;
}

.student-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.student-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
}

.student-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.student-status {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.student-status.在读 {
  background: #e8f5e8;
  color: #2e7d32;
}

.student-status.休学 {
  background: #fff3e0;
  color: #f57c00;
}

.student-status.毕业 {
  background: #e3f2fd;
  color: #1976d2;
}

.card-body {
  margin-bottom: 1rem;
}

.student-name {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.student-id,
.student-major,
.student-class {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
}

.card-footer {
  display: flex;
  gap: 0.5rem;
}

.card-btn {
  flex: 1;
  padding: 0.5rem;
  border: 1px solid #e1e5e9;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.card-btn:hover {
  background: #f8f9fa;
}

/* 表格视图 */
.students-table-container {
  overflow-x: auto;
}

.students-table {
  width: 100%;
  border-collapse: collapse;
}

.students-table th,
.students-table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #e1e5e9;
}

.students-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #333;
}

.table-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
}

.table-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.student-name-cell {
  font-weight: 500;
  color: #333;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.status-badge.在读 {
  background: #e8f5e8;
  color: #2e7d32;
}

.status-badge.休学 {
  background: #fff3e0;
  color: #f57c00;
}

.status-badge.毕业 {
  background: #e3f2fd;
  color: #1976d2;
}

.table-actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  padding: 0.5rem;
  border: none;
  background: #f8f9fa;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: #e9ecef;
}

/* 分页 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #e1e5e9;
}

.page-btn {
  padding: 0.5rem 1rem;
  border: 1px solid #e1e5e9;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.page-btn:hover:not(:disabled) {
  background: #f8f9fa;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  color: #666;
  font-size: 0.9rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .students-container {
    padding: 1rem;
  }
  
  .page-header {
    flex-direction: column;
    gap: 1rem;
  }
  
  .search-bar {
    flex-direction: column;
  }
  
  .search-input-group {
    min-width: auto;
  }
  
  .students-grid {
    grid-template-columns: 1fr;
  }
}

/* 成绩查看对话框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  max-width: 90vw;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.grades-modal {
  width: 1000px;
}

.modal-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e1e5e9;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.close-btn:hover {
  background: #f5f5f5;
}

.modal-body {
  padding: 1.5rem;
  overflow-y: auto;
  flex: 1;
}

.modal-footer {
  padding: 1.5rem;
  border-top: 1px solid #e1e5e9;
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

.student-info {
  display: flex;
  gap: 2rem;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.info-label {
  font-weight: 600;
  color: #666;
}

.info-value {
  color: #333;
}

.grades-table {
  overflow-x: auto;
}

.grades-table table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;
}

.grades-table th,
.grades-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #e1e5e9;
}

.grades-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #333;
  position: sticky;
  top: 0;
}

/* 成绩徽章样式 */
.score-badge,
.grade-level,
.gpa-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
}

.score-badge.excellent,
.grade-level.excellent,
.gpa-badge.excellent {
  background: #e8f5e8;
  color: #2e7d32;
}

.score-badge.good,
.grade-level.good,
.gpa-badge.good {
  background: #e3f2fd;
  color: #1976d2;
}

.score-badge.average,
.grade-level.average,
.gpa-badge.average {
  background: #fff3e0;
  color: #f57c00;
}

.score-badge.pass,
.grade-level.pass,
.gpa-badge.pass {
  background: #fce4ec;
  color: #c2185b;
}

.score-badge.fail,
.grade-level.fail,
.gpa-badge.fail {
  background: #ffebee;
  color: #d32f2f;
}
</style>
