{"version": 3, "file": "lang/summernote-ko-KR.js", "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAT,EAAY;AACXA,EAAAA,CAAC,CAACC,MAAF,CAASD,CAAC,CAACE,UAAF,CAAaC,IAAtB,EAA4B;AAC1B,aAAS;AACPC,MAAAA,IAAI,EAAE;AACJC,QAAAA,IAAI,EAAE,IADF;AAEJC,QAAAA,MAAM,EAAE,MAFJ;AAGJC,QAAAA,SAAS,EAAE,IAHP;AAIJC,QAAAA,KAAK,EAAE,QAJH;AAKJC,QAAAA,MAAM,EAAE,MALJ;AAMJC,QAAAA,IAAI,EAAE,IANF;AAOJC,QAAAA,WAAW,EAAE,MAPT;AAQJC,QAAAA,SAAS,EAAE,OARP;AASJC,QAAAA,aAAa,EAAE,KATX;AAUJC,QAAAA,IAAI,EAAE;AAVF,OADC;AAaPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,IADF;AAELC,QAAAA,MAAM,EAAE,OAFH;AAGLC,QAAAA,UAAU,EAAE,aAHP;AAILC,QAAAA,UAAU,EAAE,YAJP;AAKLC,QAAAA,aAAa,EAAE,YALV;AAMLC,QAAAA,UAAU,EAAE,OANP;AAOLC,QAAAA,SAAS,EAAE,OAPN;AAQLC,QAAAA,UAAU,EAAE,QARP;AASLC,QAAAA,SAAS,EAAE,SATN;AAULC,QAAAA,YAAY,EAAE,aAVT;AAWLC,QAAAA,WAAW,EAAE,SAXR;AAYLC,QAAAA,cAAc,EAAE,SAZX;AAaLC,QAAAA,SAAS,EAAE,SAbN;AAcLC,QAAAA,aAAa,EAAE,uBAdV;AAeLC,QAAAA,SAAS,EAAE,mBAfN;AAgBLC,QAAAA,eAAe,EAAE,OAhBZ;AAiBLC,QAAAA,eAAe,EAAE,UAjBZ;AAkBLC,QAAAA,oBAAoB,EAAE,mBAlBjB;AAmBLC,QAAAA,GAAG,EAAE,QAnBA;AAoBLC,QAAAA,MAAM,EAAE,OApBH;AAqBLC,QAAAA,QAAQ,EAAE;AArBL,OAbA;AAoCPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,KADF;AAELC,QAAAA,SAAS,EAAE,QAFN;AAGLrB,QAAAA,MAAM,EAAE,QAHH;AAILiB,QAAAA,GAAG,EAAE,SAJA;AAKLK,QAAAA,SAAS,EAAE;AALN,OApCA;AA2CPC,MAAAA,IAAI,EAAE;AACJA,QAAAA,IAAI,EAAE,IADF;AAEJvB,QAAAA,MAAM,EAAE,OAFJ;AAGJwB,QAAAA,MAAM,EAAE,OAHJ;AAIJC,QAAAA,IAAI,EAAE,IAJF;AAKJC,QAAAA,aAAa,EAAE,YALX;AAMJT,QAAAA,GAAG,EAAE,SAND;AAOJU,QAAAA,eAAe,EAAE;AAPb,OA3CC;AAoDPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,GADF;AAELC,QAAAA,WAAW,EAAE,SAFR;AAGLC,QAAAA,WAAW,EAAE,UAHR;AAILC,QAAAA,UAAU,EAAE,UAJP;AAKLC,QAAAA,WAAW,EAAE,WALR;AAMLC,QAAAA,MAAM,EAAE,OANH;AAOLC,QAAAA,MAAM,EAAE,OAPH;AAQLC,QAAAA,QAAQ,EAAE;AARL,OApDA;AA8DPC,MAAAA,EAAE,EAAE;AACFpC,QAAAA,MAAM,EAAE;AADN,OA9DG;AAiEPqC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,KADF;AAELC,QAAAA,CAAC,EAAE,IAFE;AAGLC,QAAAA,UAAU,EAAE,KAHP;AAILC,QAAAA,GAAG,EAAE,IAJA;AAKLC,QAAAA,EAAE,EAAE,MALC;AAMLC,QAAAA,EAAE,EAAE,MANC;AAOLC,QAAAA,EAAE,EAAE,MAPC;AAQLC,QAAAA,EAAE,EAAE,MARC;AASLC,QAAAA,EAAE,EAAE,MATC;AAULC,QAAAA,EAAE,EAAE;AAVC,OAjEA;AA6EPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,SAAS,EAAE,QADN;AAELC,QAAAA,OAAO,EAAE;AAFJ,OA7EA;AAiFPC,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,KADC;AAEPC,QAAAA,UAAU,EAAE,OAFL;AAGPC,QAAAA,QAAQ,EAAE;AAHH,OAjFF;AAsFPC,MAAAA,SAAS,EAAE;AACTA,QAAAA,SAAS,EAAE,OADF;AAETC,QAAAA,OAAO,EAAE,MAFA;AAGTC,QAAAA,MAAM,EAAE,MAHC;AAITC,QAAAA,IAAI,EAAE,OAJG;AAKTC,QAAAA,MAAM,EAAE,QALC;AAMTC,QAAAA,KAAK,EAAE,QANE;AAOTC,QAAAA,OAAO,EAAE;AAPA,OAtFJ;AA+FPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,MAAM,EAAE,aADH;AAELC,QAAAA,IAAI,EAAE,SAFD;AAGLC,QAAAA,UAAU,EAAE,KAHP;AAILC,QAAAA,UAAU,EAAE,KAJP;AAKLC,QAAAA,WAAW,EAAE,IALR;AAMLC,QAAAA,cAAc,EAAE,SANX;AAOLC,QAAAA,KAAK,EAAE,IAPF;AAQLC,QAAAA,cAAc,EAAE,UARX;AASLC,QAAAA,QAAQ,EAAE;AATL,OA/FA;AA0GPC,MAAAA,QAAQ,EAAE;AACRC,QAAAA,SAAS,EAAE,SADH;AAERC,QAAAA,KAAK,EAAE,IAFC;AAGRC,QAAAA,cAAc,EAAE,WAHR;AAIRC,QAAAA,MAAM,EAAE,IAJA;AAKRC,QAAAA,mBAAmB,EAAE,WALb;AAMRC,QAAAA,aAAa,EAAE,WANP;AAORC,QAAAA,SAAS,EAAE;AAPH,OA1GH;AAmHP3B,MAAAA,IAAI,EAAE;AACJ,2BAAmB,OADf;AAEJ,gBAAQ,WAFJ;AAGJ,gBAAQ,YAHJ;AAIJ,eAAO,GAJH;AAKJ,iBAAS,MALL;AAMJ,gBAAQ,WANJ;AAOJ,kBAAU,aAPN;AAQJ,qBAAa,WART;AASJ,yBAAiB,YATb;AAUJ,wBAAgB,OAVZ;AAWJ,uBAAe,SAXX;AAYJ,yBAAiB,UAZb;AAaJ,wBAAgB,UAbZ;AAcJ,uBAAe,WAdX;AAeJ,+BAAuB,cAfnB;AAgBJ,6BAAqB,cAhBjB;AAiBJ,mBAAW,YAjBP;AAkBJ,kBAAU,YAlBN;AAmBJ,sBAAc,uBAnBV;AAoBJ,oBAAY,wBApBR;AAqBJ,oBAAY,wBArBR;AAsBJ,oBAAY,wBAtBR;AAuBJ,oBAAY,wBAvBR;AAwBJ,oBAAY,wBAxBR;AAyBJ,oBAAY,wBAzBR;AA0BJ,gCAAwB,QA1BpB;AA2BJ,2BAAmB;AA3Bf,OAnHC;AAgJP4B,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,OADC;AAEPC,QAAAA,IAAI,EAAE;AAFC,OAhJF;AAoJPC,MAAAA,WAAW,EAAE;AACXA,QAAAA,WAAW,EAAE,MADF;AAEXC,QAAAA,MAAM,EAAE;AAFG;AApJN;AADiB,GAA5B;AA2JD,CA5JD,EA4JGC,MA5JH", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-ko-KR.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, function() {\nreturn ", "(function($) {\n  $.extend($.summernote.lang, {\n    'ko-KR': {\n      font: {\n        bold: '굵게',\n        italic: '기울임꼴',\n        underline: '밑줄',\n        clear: '서식 지우기',\n        height: '줄 간격',\n        name: '글꼴',\n        superscript: '위 첨자',\n        subscript: '아래 첨자',\n        strikethrough: '취소선',\n        size: '글자 크기',\n      },\n      image: {\n        image: '그림',\n        insert: '그림 삽입',\n        resizeFull: '100% 크기로 변경',\n        resizeHalf: '50% 크기로 변경',\n        resizeQuarter: '25% 크기로 변경',\n        resizeNone: '원본 크기',\n        floatLeft: '왼쪽 정렬',\n        floatRight: '오른쪽 정렬',\n        floatNone: '정렬하지 않음',\n        shapeRounded: '스타일: 둥근 모서리',\n        shapeCircle: '스타일: 원형',\n        shapeThumbnail: '스타일: 액자',\n        shapeNone: '스타일: 없음',\n        dragImageHere: '텍스트 혹은 사진을 이곳으로 끌어오세요',\n        dropImage: '텍스트 혹은 사진을 내려놓으세요',\n        selectFromFiles: '파일 선택',\n        maximumFileSize: '최대 파일 크기',\n        maximumFileSizeError: '최대 파일 크기를 초과했습니다.',\n        url: '사진 URL',\n        remove: '사진 삭제',\n        original: '원본',\n      },\n      video: {\n        video: '동영상',\n        videoLink: '동영상 링크',\n        insert: '동영상 삽입',\n        url: '동영상 URL',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion, Youku 사용 가능)',\n      },\n      link: {\n        link: '링크',\n        insert: '링크 삽입',\n        unlink: '링크 삭제',\n        edit: '수정',\n        textToDisplay: '링크에 표시할 내용',\n        url: '이동할 URL',\n        openInNewWindow: '새창으로 열기',\n      },\n      table: {\n        table: '표',\n        addRowAbove: '위에 행 삽입',\n        addRowBelow: '아래에 행 삽입',\n        addColLeft: '왼쪽에 열 삽입',\n        addColRight: '오른쪽에 열 삽입',\n        delRow: '행 지우기',\n        delCol: '열 지우기',\n        delTable: '표 삭제',\n      },\n      hr: {\n        insert: '구분선 삽입',\n      },\n      style: {\n        style: '스타일',\n        p: '본문',\n        blockquote: '인용구',\n        pre: '코드',\n        h1: '제목 1',\n        h2: '제목 2',\n        h3: '제목 3',\n        h4: '제목 4',\n        h5: '제목 5',\n        h6: '제목 6',\n      },\n      lists: {\n        unordered: '글머리 기호',\n        ordered: '번호 매기기',\n      },\n      options: {\n        help: '도움말',\n        fullscreen: '전체 화면',\n        codeview: '코드 보기',\n      },\n      paragraph: {\n        paragraph: '문단 정렬',\n        outdent: '내어쓰기',\n        indent: '들여쓰기',\n        left: '왼쪽 정렬',\n        center: '가운데 정렬',\n        right: '오른쪽 정렬',\n        justify: '양쪽 정렬',\n      },\n      color: {\n        recent: '마지막으로 사용한 색',\n        more: '다른 색 선택',\n        background: '배경색',\n        foreground: '글자색',\n        transparent: '투명',\n        setTransparent: '투명으로 설정',\n        reset: '취소',\n        resetToDefault: '기본값으로 설정',\n        cpSelect: '선택',\n      },\n      shortcut: {\n        shortcuts: '키보드 단축키',\n        close: '닫기',\n        textFormatting: '글자 스타일 적용',\n        action: '기능',\n        paragraphFormatting: '문단 스타일 적용',\n        documentStyle: '문서 스타일 적용',\n        extraKeys: '추가 키',\n      },\n      help: {\n        'insertParagraph': '문단 삽입',\n        'undo': '마지막 명령 취소',\n        'redo': '마지막 명령 재실행',\n        'tab': '탭',\n        'untab': '탭 제거',\n        'bold': '굵은 글자로 설정',\n        'italic': '기울임꼴 글자로 설정',\n        'underline': '밑줄 글자로 설정',\n        'strikethrough': '취소선 글자로 설정',\n        'removeFormat': '서식 삭제',\n        'justifyLeft': '왼쪽 정렬하기',\n        'justifyCenter': '가운데 정렬하기',\n        'justifyRight': '오른쪽 정렬하기',\n        'justifyFull': '좌우채움 정렬하기',\n        'insertUnorderedList': '글머리 기호 켜고 끄기',\n        'insertOrderedList': '번호 매기기 켜고 끄기',\n        'outdent': '현재 문단 내어쓰기',\n        'indent': '현재 문단 들여쓰기',\n        'formatPara': '현재 블록의 포맷을 문단(P)으로 변경',\n        'formatH1': '현재 블록의 포맷을 제목1(H1)로 변경',\n        'formatH2': '현재 블록의 포맷을 제목2(H2)로 변경',\n        'formatH3': '현재 블록의 포맷을 제목3(H3)로 변경',\n        'formatH4': '현재 블록의 포맷을 제목4(H4)로 변경',\n        'formatH5': '현재 블록의 포맷을 제목5(H5)로 변경',\n        'formatH6': '현재 블록의 포맷을 제목6(H6)로 변경',\n        'insertHorizontalRule': '구분선 삽입',\n        'linkDialog.show': '링크 대화상자 열기',\n      },\n      history: {\n        undo: '실행 취소',\n        redo: '재실행',\n      },\n      specialChar: {\n        specialChar: '특수문자',\n        select: '특수문자를 선택하세요',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "superscript", "subscript", "strikethrough", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "resizeNone", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "cpSelect", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}