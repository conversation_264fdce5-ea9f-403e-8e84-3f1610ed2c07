<template>
  <div class="dashboard-container">
    <!-- 顶部欢迎区域 -->
    <div class="welcome-section">
      <div class="welcome-content">
        <div class="welcome-text">
          <h1>欢迎回来，{{ userInfo.name }}</h1>
          <p>{{ welcomeMessage }}</p>
        </div>
        <div class="welcome-stats">
          <div class="stat-item">
            <div class="stat-icon">👥</div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.totalStudents }}</div>
              <div class="stat-label">在校学生</div>
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-icon">📚</div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.totalCourses }}</div>
              <div class="stat-label">开设课程</div>
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-icon">📊</div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.totalGrades }}</div>
              <div class="stat-label">成绩记录</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 快速操作卡片 -->
      <div class="quick-actions">
        <h2 class="section-title">快速操作</h2>
        <div class="action-cards">
          <div class="action-card" @click="navigateTo('/students')">
            <div class="card-icon student-icon">👨‍🎓</div>
            <h3>学生管理</h3>
            <p>查看和管理学生信息</p>
            <div class="card-arrow">→</div>
          </div>
          <div class="action-card" @click="navigateTo('/grades')">
            <div class="card-icon grade-icon">📝</div>
            <h3>成绩管理</h3>
            <p>录入和查询学生成绩</p>
            <div class="card-arrow">→</div>
          </div>
          <div class="action-card" @click="navigateTo('/analytics')">
            <div class="card-icon analytics-icon">📈</div>
            <h3>数据分析</h3>
            <p>查看统计报表和分析</p>
            <div class="card-arrow">→</div>
          </div>
          <div class="action-card" @click="navigateTo('/settings')">
            <div class="card-icon settings-icon">⚙️</div>
            <h3>系统设置</h3>
            <p>配置系统参数和权限</p>
            <div class="card-arrow">→</div>
          </div>
        </div>
      </div>

      <!-- 数据概览 -->
      <div class="data-overview">
        <div class="overview-left">
          <h2 class="section-title">成绩分布概览</h2>
          <div class="chart-container">
            <div class="grade-distribution-chart">
              <div class="chart-bars">
                <div class="bar-item">
                  <div class="bar-container">
                    <div
                      class="bar excellent"
                      :style="{ height: gradeDistributionPercentage.excellent + '%' }"
                    ></div>
                  </div>
                  <div class="bar-label">优秀</div>
                  <div class="bar-value">{{ gradeDistribution.excellent }}</div>
                  <div class="bar-percentage">{{ gradeDistributionPercentage.excellent }}%</div>
                </div>
                <div class="bar-item">
                  <div class="bar-container">
                    <div
                      class="bar good"
                      :style="{ height: gradeDistributionPercentage.good + '%' }"
                    ></div>
                  </div>
                  <div class="bar-label">良好</div>
                  <div class="bar-value">{{ gradeDistribution.good }}</div>
                  <div class="bar-percentage">{{ gradeDistributionPercentage.good }}%</div>
                </div>
                <div class="bar-item">
                  <div class="bar-container">
                    <div
                      class="bar average"
                      :style="{ height: gradeDistributionPercentage.average + '%' }"
                    ></div>
                  </div>
                  <div class="bar-label">中等</div>
                  <div class="bar-value">{{ gradeDistribution.average }}</div>
                  <div class="bar-percentage">{{ gradeDistributionPercentage.average }}%</div>
                </div>
                <div class="bar-item">
                  <div class="bar-container">
                    <div
                      class="bar pass"
                      :style="{ height: gradeDistributionPercentage.pass + '%' }"
                    ></div>
                  </div>
                  <div class="bar-label">及格</div>
                  <div class="bar-value">{{ gradeDistribution.pass }}</div>
                  <div class="bar-percentage">{{ gradeDistributionPercentage.pass }}%</div>
                </div>
                <div class="bar-item">
                  <div class="bar-container">
                    <div
                      class="bar fail"
                      :style="{ height: gradeDistributionPercentage.fail + '%' }"
                    ></div>
                  </div>
                  <div class="bar-label">不及格</div>
                  <div class="bar-value">{{ gradeDistribution.fail }}</div>
                  <div class="bar-percentage">{{ gradeDistributionPercentage.fail }}%</div>
                </div>
              </div>
              <div class="chart-legend">
                <div class="legend-item">
                  <span class="legend-color excellent"></span>
                  <span>优秀 (90-100分)</span>
                </div>
                <div class="legend-item">
                  <span class="legend-color good"></span>
                  <span>良好 (80-89分)</span>
                </div>
                <div class="legend-item">
                  <span class="legend-color average"></span>
                  <span>中等 (70-79分)</span>
                </div>
                <div class="legend-item">
                  <span class="legend-color pass"></span>
                  <span>及格 (60-69分)</span>
                </div>
                <div class="legend-item">
                  <span class="legend-color fail"></span>
                  <span>不及格 (0-59分)</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="overview-right">
          <h2 class="section-title">最近活动</h2>
          <div class="activity-list">
            <div class="activity-item" v-for="activity in recentActivities" :key="activity.id">
              <div class="activity-icon" :class="activity.type">
                {{ activity.icon }}
              </div>
              <div class="activity-content">
                <div class="activity-title">{{ activity.title }}</div>
                <div class="activity-time">{{ activity.time }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 通知公告 -->
      <div class="notifications-section">
        <h2 class="section-title">通知公告</h2>
        <div class="notification-cards">
          <div class="notification-card important">
            <div class="notification-header">
              <span class="notification-badge">重要</span>
              <span class="notification-date">2024-06-19</span>
            </div>
            <h3>期末考试成绩录入通知</h3>
            <p>请各任课教师在6月25日前完成期末考试成绩录入工作...</p>
          </div>
          <div class="notification-card normal">
            <div class="notification-header">
              <span class="notification-badge">通知</span>
              <span class="notification-date">2024-06-18</span>
            </div>
            <h3>系统维护公告</h3>
            <p>系统将于本周六晚上进行例行维护，预计维护时间2小时...</p>
          </div>
          <div class="notification-card normal">
            <div class="notification-header">
              <span class="notification-badge">提醒</span>
              <span class="notification-date">2024-06-17</span>
            </div>
            <h3>学期总结报告提交</h3>
            <p>请各学院在月底前提交本学期教学总结报告...</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { getAllStudents } from '@/data/studentsData.js'

const router = useRouter()

// 用户信息 - 从localStorage获取当前登录用户信息
const userInfo = reactive({
  name: '张教授',
  role: '教务管理员',
  avatar: '/default-avatar.png'
})

// 初始化用户信息
const initUserInfo = () => {
  const currentUser = localStorage.getItem('currentUser')
  if (currentUser) {
    const user = JSON.parse(currentUser)
    userInfo.name = user.name
    // 正确映射用户角色
    if (user.role === '超级管理员') {
      userInfo.role = '系统管理员'
    } else if (user.role === '教务管理员') {
      userInfo.role = '教务管理员'
    } else if (user.role === '教师') {
      userInfo.role = '教师'
    } else if (user.role === '学生') {
      userInfo.role = '学生'
    } else {
      userInfo.role = user.role // 保持原值
    }
  }
}

// 页面加载时初始化用户信息
initUserInfo()

// 当前日期和欢迎信息
const currentDate = ref('')
const welcomeMessage = ref('')

// 数据缓存
let cachedData = null

// 获取真实数据（优化性能版本）
const getRealData = () => {
  // 如果已有缓存，直接返回
  if (cachedData) {
    return cachedData
  }

  // 获取学生数据（限制数量以提高性能）
  const allStudents = getAllStudents()
  const studentsData = allStudents.slice(0, 1000) // 限制为1000个学生

  // 获取课程数据（减少课程数量）
  const coursesData = [
    { id: 1, code: 'CS101', name: '计算机科学导论', credits: 3 },
    { id: 2, code: 'MATH101', name: '高等数学A', credits: 5 },
    { id: 3, code: 'ENG101', name: '大学英语', credits: 2 },
    { id: 4, code: 'CS201', name: '数据结构与算法', credits: 4 }
  ]

  // 生成成绩数据（减少数据量）
  const gradesData = []
  studentsData.forEach((student, index) => {
    // 每个学生只选择2-3门课程，减少数据量
    const courseCount = Math.floor(Math.random() * 2) + 2
    const selectedCourses = coursesData.slice(0, courseCount)

    selectedCourses.forEach(course => {
      // 生成成绩（使用更简单的算法）
      const baseScore = 70 + Math.random() * 25 // 70-95分
      const variation = (Math.random() - 0.5) * 10 // ±5分变化

      const regularScore = Math.max(60, Math.min(100, Math.round(baseScore + variation)))
      const midtermScore = Math.max(60, Math.min(100, Math.round(baseScore + variation)))
      const finalScore = Math.max(60, Math.min(100, Math.round(baseScore + variation)))
      const totalScore = Math.round(regularScore * 0.3 + midtermScore * 0.3 + finalScore * 0.4)

      gradesData.push({
        id: `${student.studentId}-${course.code}`,
        studentId: student.studentId,
        studentName: student.name,
        courseCode: course.code,
        courseName: course.name,
        regularScore,
        midtermScore,
        finalScore,
        totalScore,
        credits: course.credits
      })
    })
  })

  // 缓存数据
  cachedData = {
    students: studentsData,
    courses: coursesData,
    grades: gradesData
  }

  return cachedData
}

// 统计数据
const realData = getRealData()
const stats = reactive({
  totalStudents: realData.students.length,
  totalCourses: realData.courses.length,
  totalGrades: realData.grades.length
})

// 成绩分布数据
const gradeDistribution = computed(() => {
  const distribution = {
    excellent: 0,  // 90-100
    good: 0,       // 80-89
    average: 0,    // 70-79
    pass: 0,       // 60-69
    fail: 0        // 0-59
  }

  realData.grades.forEach(grade => {
    const score = grade.totalScore
    if (score >= 90) distribution.excellent++
    else if (score >= 80) distribution.good++
    else if (score >= 70) distribution.average++
    else if (score >= 60) distribution.pass++
    else distribution.fail++
  })

  return distribution
})

// 成绩分布百分比
const gradeDistributionPercentage = computed(() => {
  const total = realData.grades.length
  if (total === 0) return { excellent: 0, good: 0, average: 0, pass: 0, fail: 0 }

  return {
    excellent: Math.round((gradeDistribution.value.excellent / total) * 100),
    good: Math.round((gradeDistribution.value.good / total) * 100),
    average: Math.round((gradeDistribution.value.average / total) * 100),
    pass: Math.round((gradeDistribution.value.pass / total) * 100),
    fail: Math.round((gradeDistribution.value.fail / total) * 100)
  }
})

// 最近活动
const recentActivities = ref([
  {
    id: 1,
    type: 'grade',
    icon: '📝',
    title: '高等数学A成绩已录入',
    time: '2小时前'
  },
  {
    id: 2,
    type: 'student',
    icon: '👨‍🎓',
    title: '新增学生信息 - 李明',
    time: '4小时前'
  },
  {
    id: 3,
    type: 'system',
    icon: '⚙️',
    title: '系统备份完成',
    time: '6小时前'
  },
  {
    id: 4,
    type: 'analytics',
    icon: '📊',
    title: '月度报表生成完成',
    time: '1天前'
  }
])

// 页面导航
const navigateTo = (path: string) => {
  router.push(path)
}

// 初始化
onMounted(() => {
  // 初始化用户信息
  initUserInfo()

  // 设置当前日期和欢迎信息
  const now = new Date()
  const year = now.getFullYear()
  const month = now.getMonth() + 1
  const day = now.getDate()
  const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
  const weekday = weekdays[now.getDay()]

  currentDate.value = now.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  })

  welcomeMessage.value = `今天是 ${year}年${month}月${day}日${weekday}，祝您工作愉快！`
})
</script>

<style scoped>
.dashboard-container {
  min-height: 100vh;
  background: #f5f7fa;
  padding: 2rem;
}

/* 欢迎区域 */
.welcome-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  padding: 2rem;
  margin-bottom: 2rem;
  color: white;
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.welcome-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 2rem;
}

.welcome-text h1 {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.welcome-text p {
  font-size: 1.1rem;
  opacity: 0.9;
}

.welcome-stats {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 1rem 1.5rem;
  border-radius: 15px;
  backdrop-filter: blur(10px);
}

.stat-icon {
  font-size: 2rem;
}

.stat-number {
  font-size: 1.5rem;
  font-weight: 700;
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.8;
}

/* 主要内容 */
.main-content {
  display: grid;
  gap: 2rem;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1.5rem;
}

/* 快速操作卡片 */
.action-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.action-card {
  background: white;
  border-radius: 15px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
}

.action-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.card-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
}

.action-card h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.action-card p {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 1rem;
}

.card-arrow {
  position: absolute;
  top: 1rem;
  right: 1rem;
  font-size: 1.2rem;
  color: #ccc;
  transition: all 0.3s ease;
}

.action-card:hover .card-arrow {
  color: #667eea;
  transform: translateX(5px);
}

/* 数据概览 */
.data-overview {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
}

.overview-left,
.overview-right {
  background: white;
  border-radius: 15px;
  padding: 2rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 成绩分布图表样式 */
.grade-distribution-chart {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.chart-bars {
  display: flex;
  align-items: end;
  justify-content: space-around;
  height: 200px;
  padding: 1rem 0;
  border-bottom: 2px solid #e1e5e9;
  position: relative;
}

.bar-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
  max-width: 80px;
}

.bar-container {
  height: 150px;
  width: 40px;
  display: flex;
  align-items: end;
  position: relative;
}

.bar {
  width: 100%;
  border-radius: 4px 4px 0 0;
  transition: all 0.8s ease;
  animation: growUp 1.5s ease-out;
  position: relative;
}

@keyframes growUp {
  from {
    height: 0 !important;
  }
  to {
    height: var(--final-height);
  }
}

.bar.excellent {
  background: linear-gradient(to top, #2e7d32, #4caf50);
}

.bar.good {
  background: linear-gradient(to top, #1976d2, #2196f3);
}

.bar.average {
  background: linear-gradient(to top, #f57c00, #ff9800);
}

.bar.pass {
  background: linear-gradient(to top, #c2185b, #e91e63);
}

.bar.fail {
  background: linear-gradient(to top, #d32f2f, #f44336);
}

.bar-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: #333;
}

.bar-value {
  font-size: 1.1rem;
  font-weight: 700;
  color: #1976d2;
}

.bar-percentage {
  font-size: 0.8rem;
  color: #666;
}

.chart-legend {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: center;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-color.excellent {
  background: #4caf50;
}

.legend-color.good {
  background: #2196f3;
}

.legend-color.average {
  background: #ff9800;
}

.legend-color.pass {
  background: #e91e63;
}

.legend-color.fail {
  background: #f44336;
}

.chart-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

/* 活动列表 */
.activity-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border-radius: 10px;
  background: #f8f9fa;
  transition: all 0.3s ease;
}

.activity-item:hover {
  background: #e9ecef;
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
}

.activity-icon.grade {
  background: #e3f2fd;
}

.activity-icon.student {
  background: #f3e5f5;
}

.activity-icon.system {
  background: #e8f5e8;
}

.activity-icon.analytics {
  background: #fff3e0;
}

.activity-title {
  font-weight: 500;
  color: #333;
}

.activity-time {
  font-size: 0.8rem;
  color: #999;
}

/* 通知公告 */
.notification-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.notification-card {
  background: white;
  border-radius: 15px;
  padding: 1.5rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  border-left: 4px solid #ddd;
  transition: all 0.3s ease;
}

.notification-card.important {
  border-left-color: #e74c3c;
}

.notification-card.normal {
  border-left-color: #3498db;
}

.notification-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.12);
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.notification-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.notification-card.important .notification-badge {
  background: #fee;
  color: #e74c3c;
}

.notification-card.normal .notification-badge {
  background: #e3f2fd;
  color: #3498db;
}

.notification-date {
  font-size: 0.8rem;
  color: #999;
}

.notification-card h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.notification-card p {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard-container {
    padding: 1rem;
  }
  
  .welcome-content {
    flex-direction: column;
    text-align: center;
  }
  
  .welcome-stats {
    justify-content: center;
  }
  
  .data-overview {
    grid-template-columns: 1fr;
  }
  
  .action-cards {
    grid-template-columns: 1fr;
  }
  
  .notification-cards {
    grid-template-columns: 1fr;
  }
}
</style>
