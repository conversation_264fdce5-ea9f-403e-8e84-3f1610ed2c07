/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: Apache Tomcat/7.0.47
 * Generated at: 2025-06-20 03:14:05 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp;

import javax.servlet.*;
import javax.servlet.http.*;
import javax.servlet.jsp.*;
import com.ntvu.studentmis.entity.User;
import com.ntvu.studentmis.db.DBManager;

public final class register_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent {

  private static final javax.servlet.jsp.JspFactory _jspxFactory =
          javax.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  static {
    _jspx_dependants = new java.util.HashMap<java.lang.String,java.lang.Long>(1);
    _jspx_dependants.put("/include/foot_js.jsp", Long.valueOf(1685610773249L));
  }

  private javax.el.ExpressionFactory _el_expressionfactory;
  private org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public void _jspInit() {
    _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
    _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
  }

  public void _jspDestroy() {
  }

  public void _jspService(final javax.servlet.http.HttpServletRequest request, final javax.servlet.http.HttpServletResponse response)
        throws java.io.IOException, javax.servlet.ServletException {

    final javax.servlet.jsp.PageContext pageContext;
    javax.servlet.http.HttpSession session = null;
    final javax.servlet.ServletContext application;
    final javax.servlet.ServletConfig config;
    javax.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    javax.servlet.jsp.JspWriter _jspx_out = null;
    javax.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html; charset=UTF-8");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write("\n");
      out.write("\n");
      out.write("\n");
      out.write("<!DOCTYPE html>\n");
      out.write("<html lang=\"en\">\n");
      out.write("<head>\n");
      out.write("  <meta charset=\"utf-8\">\n");
      out.write("  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\n");
      out.write("  <title>注册用户</title>\n");
      out.write("  <!-- Google Font: Source Sans Pro -->\n");
      out.write("  <link rel=\"stylesheet\" href=\"https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback\">\n");
      out.write("  <!-- Font Awesome -->\n");
      out.write("  <link rel=\"stylesheet\" href=\"plugins/fontawesome-free/css/all.min.css\">\n");
      out.write("  <!-- icheck bootstrap -->\n");
      out.write("  <link rel=\"stylesheet\" href=\"plugins/icheck-bootstrap/icheck-bootstrap.min.css\">\n");
      out.write("  <!-- Theme style -->\n");
      out.write("  <link rel=\"stylesheet\" href=\"dist/css/adminlte.min.css\">\n");
      out.write("</head>\n");
      out.write("<body class=\"hold-transition register-page\">\n");

  request.setCharacterEncoding("utf-8");
  String user_num = request.getParameter("user_num");
  String phone = request.getParameter("phone");

      out.write("\n");
      out.write("<div class=\"register-box\">\n");
      out.write("  <div class=\"register-logo\">\n");
      out.write("    <a href=\"#\"><b>注册</b>用户</a>\n");
      out.write("  </div>\n");
      out.write("  <div class=\"card\">\n");
      out.write("    <div class=\"card-body register-card-body\">\n");
      out.write("      <p class=\"login-box-msg\">注册一个新用户</p>\n");
      out.write("\n");
      out.write("      <form name=\"form1\" id=\"form1\" method=\"post\" action=\"");
      out.print( request.getContextPath() + "/admin/user/UserServlet?action=register");
      out.write("\" onsubmit=\"return verify()\">\n");
      out.write("        <div class=\"input-group mb-3\">\n");
      out.write("          <input type=\"hidden\" name=\"user_num\" value=\"");
      out.print(user_num);
      out.write("\">\n");
      out.write("          <input type=\"hidden\" name=\"phone\" value=\"");
      out.print(phone);
      out.write("\">\n");
      out.write("          <input type=\"text\" class=\"form-control\" placeholder=\"用户名\" name=\"txtLoginName\">\n");
      out.write("          <div class=\"input-group-append\">\n");
      out.write("            <div class=\"input-group-text\">\n");
      out.write("              <span class=\"fas fa-user\"></span>\n");
      out.write("            </div>\n");
      out.write("          </div>\n");
      out.write("        </div>\n");
      out.write("        <div class=\"input-group mb-3\">\n");
      out.write("          <input type=\"password\" class=\"form-control\" placeholder=\"密码\" name=\"txtLoginPassword\"\n");
      out.write("                 pattern=\"(?=.*\\d)(?=.*[a-z])(?=.*[A-Z]).{6,}\"\n");
      out.write("                 title=\"密码必须包含至少一个大写字母、一个小写字母和一个数字，且长度至少6位\"\n");
      out.write("                 required>\n");
      out.write("          <div class=\"input-group-append\">\n");
      out.write("            <div class=\"input-group-text\">\n");
      out.write("              <span class=\"fas fa-lock\"></span>\n");
      out.write("            </div>\n");
      out.write("          </div>\n");
      out.write("        </div>\n");
      out.write("        <div class=\"input-group mb-3\">\n");
      out.write("          <input type=\"password\" class=\"form-control\" placeholder=\"确认密码\" name=\"txtLoginPassword2\"\n");
      out.write("                 pattern=\"(?=.*\\d)(?=.*[a-z])(?=.*[A-Z]).{6,}\"\n");
      out.write("                 title=\"密码必须包含至少一个大写字母、一个小写字母和一个数字，且长度至少6位\"\n");
      out.write("                 required>\n");
      out.write("          <div class=\"input-group-append\">\n");
      out.write("            <div class=\"input-group-text\">\n");
      out.write("              <span class=\"fas fa-lock\"></span>\n");
      out.write("            </div>\n");
      out.write("          </div>\n");
      out.write("\n");
      out.write("        </div>\n");
      out.write("        <div class=\"input-group mb-3\">\n");
      out.write("          <input type=\"text\" class=\"form-control\" placeholder=\"真实用户名\" name=\"txtRealName\">\n");
      out.write("          <div class=\"input-group-append\">\n");
      out.write("            <div class=\"input-group-text\">\n");
      out.write("              <span class=\"fas fa-user\"></span>\n");
      out.write("            </div>\n");
      out.write("          </div>\n");
      out.write("        </div>\n");
      out.write("\n");
      out.write("        <div class=\"input-group mb-3\">\n");
      out.write("          <input type=\"text\" class=\"form-control\" placeholder=\"手机号\" name=\"txtTelephone\"  pattern=\"^1[3-9]\\d{9}$\"\n");
      out.write("                 title=\"请输入11位有效手机号（以13-19开头）\"\n");
      out.write("                 required>\n");
      out.write("          <div class=\"input-group-append\">\n");
      out.write("            <div class=\"input-group-text\">\n");
      out.write("              <span class=\"fas fa-phone\"></span>\n");
      out.write("            </div>\n");
      out.write("          </div>\n");
      out.write("        </div>\n");
      out.write("\n");
      out.write("        <div class=\"input-group mb-3\">\n");
      out.write("          <label>身份</label>\n");
      out.write("          <label for=\"selectList\"></label><select id=\"selectList\" name=\"selectList\" >\n");
      out.write("          <option name=\"txtRole\" value=\"\">请选择身份</option>\n");
      out.write("          <option name=\"txtRole\" value=\"0\">学生</option>\n");
      out.write("          <option name=\"txtRole\" value=\"1\">教师</option>\n");
      out.write("          <option name=\"txtRole\" value=\"2\">管理员</option>\n");
      out.write("        </select>\n");
      out.write("        </div>\n");
      out.write("\n");
      out.write("        <div class=\"row\">\n");
      out.write("          <div class=\"col-3\"></div>\n");
      out.write("          <!-- /.col -->\n");
      out.write("          <div class=\"col-6\">\n");
      out.write("            <input type=\"submit\" class=\"btn btn-primary btn-block\" value=\"注册\">\n");
      out.write("          </div>\n");
      out.write("          <div class=\"col-3\"></div>\n");
      out.write("          <!-- /.col -->\n");
      out.write("        </div>\n");
      out.write("      </form>\n");
      out.write("      <a href=\"login.jsp\" class=\"text-center\">我已经有账号了</a>\n");
      out.write("    </div>\n");
      out.write("    <!-- /.form-box -->\n");
      out.write("  </div><!-- /.card -->\n");
      out.write("</div>\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("<!-- jQuery -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/jquery/jquery.min.js\"></script>\r\n");
      out.write("<!-- jQuery UI 1.11.4 -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/jquery-ui/jquery-ui.min.js\"></script>\r\n");
      out.write("<!-- Resolve conflict in jQuery UI tooltip with Bootstrap tooltip -->\r\n");
      out.write("<script>\r\n");
      out.write("    $.widget.bridge('uibutton', $.ui.button)\r\n");
      out.write("</script>\r\n");
      out.write("\r\n");
      out.write("<!-- Bootstrap 4 -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/bootstrap/js/bootstrap.bundle.min.js\"></script>\r\n");
      out.write("<!-- ChartJS -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/chart.js/Chart.min.js\"></script>\r\n");
      out.write("<!-- Sparkline -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/sparklines/sparkline.js\"></script>\r\n");
      out.write("<!-- JQVMap -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/jqvmap/jquery.vmap.min.js\"></script>\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/jqvmap/maps/jquery.vmap.usa.js\"></script>\r\n");
      out.write("<!-- jQuery Knob Chart -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/jquery-knob/jquery.knob.min.js\"></script>\r\n");
      out.write("<!-- daterangepicker -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/moment/moment.min.js\"></script>\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/daterangepicker/daterangepicker.js\"></script>\r\n");
      out.write("<!-- Tempusdominus Bootstrap 4 -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/tempusdominus-bootstrap-4/js/tempusdominus-bootstrap-4.min.js\"></script>\r\n");
      out.write("<!-- Summernote -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/summernote/summernote-bs4.min.js\"></script>\r\n");
      out.write("<!-- overlayScrollbars -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/overlayScrollbars/js/jquery.overlayScrollbars.min.js\"></script>\r\n");
      out.write("<!-- AdminLTE App -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/dist/js/adminlte.js\"></script>\r\n");
      out.write("<!-- AdminLTE for demo purposes -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/dist/js/demo.js\"></script>\r\n");
      out.write("<!-- AdminLTE dashboard demo (This is only for demo purposes) -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/dist/js/pages/dashboard.js\"></script>\r\n");
      out.write("<script type=\"text/javascript\" src=\"");
      out.print( request.getContextPath());
      out.write("/js/jquery-3.6.1.min.js\"></script>\r\n");
      out.write("<script type=\"text/javascript\" src=\"");
      out.print( request.getContextPath());
      out.write("/js/jquery.min.js\"></script>");
      out.write("\n");
      out.write("<script type=\"text/javascript\">\n");
      out.write("  // 假设从后端返回的user数据\n");
      out.write("  function verify() {\n");
      out.write("    //对数据进行检验\n");
      out.write("    let user_num=$(`input[name=user_num]`).val();\n");
      out.write("    if(user_num!=='')\n");
      out.write("    {\n");
      out.write("      alert(`用户名已存在，请选择其他用户名`);\n");
      out.write("      return false;\n");
      out.write("    }\n");
      out.write("    let phone=$(`input[name=phone]`).val();\n");
      out.write("    if(phone!=='')\n");
      out.write("    {\n");
      out.write("      alert(`手机号已被注册！`);\n");
      out.write("      return false;\n");
      out.write("    }\n");
      out.write("    //对数据进行检验\n");
      out.write("    let txtLoginName=$(`input[name=txtLoginName]`).val();\n");
      out.write("    if(txtLoginName==='')\n");
      out.write("    {\n");
      out.write("      alert(`登录名称不能为空`);\n");
      out.write("      $(`input[name=txtLoginName]`).focus();//光标选中\n");
      out.write("      return false;\n");
      out.write("    }\n");
      out.write("    let txtLoginPassword=$(`input[name=txtLoginPassword]`).val();\n");
      out.write("    if(txtLoginPassword==='')\n");
      out.write("    {\n");
      out.write("      alert(`密码不能为空`);\n");
      out.write("      $(`input[name=txtLoginPassword]`).focus();//光标选中\n");
      out.write("      return false;\n");
      out.write("    }\n");
      out.write("    let txtLoginPassword2=$(`input[name=txtLoginPassword2]`).val();\n");
      out.write("    if(txtLoginPassword2==='')\n");
      out.write("    {\n");
      out.write("      alert(`确认密码不能为空`);\n");
      out.write("      $(`input[name=txtLoginPassword2]`).focus();//光标选中\n");
      out.write("      return false;\n");
      out.write("    }\n");
      out.write("    if(txtLoginPassword!==txtLoginPassword2)\n");
      out.write("    {\n");
      out.write("      alert(`两次密码必须相同`);\n");
      out.write("      $(`input[name=txtLoginPassword]`).focus();//光标选中\n");
      out.write("      $(`input[name=txtLoginPassword2]`).focus();//光标选中\n");
      out.write("      return false;\n");
      out.write("    }\n");
      out.write("\n");
      out.write("    let txtRealName=$(`input[name=txtRealName]`).val();\n");
      out.write("    if(txtRealName==='')\n");
      out.write("    {\n");
      out.write("      alert(`姓名不能为空`);\n");
      out.write("      $(`input[name=txtRealName]`).focus();//光标选中\n");
      out.write("      return false;\n");
      out.write("    }\n");
      out.write("    let txtTelephone=$(`input[name=txtTelephone]`).val();\n");
      out.write("    if(txtTelephone==='')\n");
      out.write("    {\n");
      out.write("      alert(`手机号不能为空`);\n");
      out.write("      $(`input[name=txtTelephone]`).focus();//光标选中\n");
      out.write("      return false;\n");
      out.write("    }\n");
      out.write("    let selectValue = $('select').val();\n");
      out.write("    if(selectValue==='')\n");
      out.write("    {\n");
      out.write("      alert(`请选择你的身份`);\n");
      out.write("      return false;\n");
      out.write("    }\n");
      out.write("  }\n");
      out.write("</script>\n");
      out.write("</body>\n");
      out.write("</html>\n");
    } catch (java.lang.Throwable t) {
      if (!(t instanceof javax.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try { out.clearBuffer(); } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }
}
