/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: Apache Tomcat/7.0.47
 * Generated at: 2025-06-20 03:14:03 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp;

import javax.servlet.*;
import javax.servlet.http.*;
import javax.servlet.jsp.*;
import com.ntvu.studentmis.entity.User;
import com.ntvu.studentmis.db.DBManager;
import com.ntvu.studentmis.util.WebTools;

public final class forgot_005fpassword_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent {

  private static final javax.servlet.jsp.JspFactory _jspxFactory =
          javax.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  static {
    _jspx_dependants = new java.util.HashMap<java.lang.String,java.lang.Long>(1);
    _jspx_dependants.put("/include/foot_js.jsp", Long.valueOf(1685610773249L));
  }

  private javax.el.ExpressionFactory _el_expressionfactory;
  private org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public void _jspInit() {
    _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
    _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
  }

  public void _jspDestroy() {
  }

  public void _jspService(final javax.servlet.http.HttpServletRequest request, final javax.servlet.http.HttpServletResponse response)
        throws java.io.IOException, javax.servlet.ServletException {

    final javax.servlet.jsp.PageContext pageContext;
    javax.servlet.http.HttpSession session = null;
    final javax.servlet.ServletContext application;
    final javax.servlet.ServletConfig config;
    javax.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    javax.servlet.jsp.JspWriter _jspx_out = null;
    javax.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html; charset=UTF-8");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write("\n");
      out.write("\n");
      out.write("\n");
      out.write("\n");
      out.write("<!DOCTYPE html>\n");
      out.write("<html lang=\"en\">\n");
      out.write("<head>\n");
      out.write("  <meta charset=\"utf-8\">\n");
      out.write("  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\n");
      out.write("  ");
      org.apache.jasper.runtime.JspRuntimeLibrary.include(request, response, "include/header_css.jsp", out, true);
      out.write("\n");
      out.write("  <title>忘记密码</title>\n");
      out.write("</head>\n");
      out.write("<body class=\"hold-transition login-page\">\n");
      out.write("<div class=\"login-box\">\n");
      out.write("  <div class=\"login-logo\">\n");
      out.write("    <b>忘记</b>密码\n");
      out.write("  </div>\n");
      out.write("  ");

    Object txtLoginName = request.getSession().getAttribute("txtLoginName");
    Object password = request.getSession().getAttribute("password");
    if (txtLoginName==null||password==null)
    {
      txtLoginName="";
      password="";
    }
  
      out.write("\n");
      out.write("  <!-- /.login-logo -->\n");
      out.write("  <div class=\"card\">\n");
      out.write("    <div class=\"card-body login-card-body\">\n");
      out.write("      <p class=\"login-box-msg\">忘记密码可以通过输入学工号找回。</p>\n");
      out.write("      <form id=\"form1\" name=\"form1\" action=\"");
      out.print( request.getContextPath() + "/admin/user/UserServlet?action=forgot_password");
      out.write("\" method=\"post\" onsubmit=\"return verify()\">\n");
      out.write("           要查找密码的账号:\n");
      out.write("        <div class=\"input-group mb-3\">\n");
      out.write("           <input name=\"txtLoginName\" type=\"text\" class=\"form-control\" placeholder=\"请输入学工号\" value=\"");
      out.print(txtLoginName);
      out.write("\">\n");
      out.write("          <div class=\"input-group-append\">\n");
      out.write("            <div class=\"input-group-text\">\n");
      out.write("              <span class=\"fas fa-user\"></span>\n");
      out.write("            </div>\n");
      out.write("          </div>\n");
      out.write("        </div>\n");
      out.write("        <div class=\"row\">\n");
      out.write("          <div class=\"col-12\">\n");
      out.write("            该账号密码为: <input name=\"password\" type=\"text\" class=\"form-control\" placeholder=\"点击查询密码后生成\" value=\"");
      out.print(password);
      out.write("\" disabled=\"disabled\">\n");
      out.write("          </div>\n");
      out.write("          <!-- /.col -->\n");
      out.write("        </div>\n");
      out.write("        <div class=\"row\">\n");
      out.write("          <div class=\"col-12\">\n");
      out.write("            <input type=\"submit\" class=\"btn btn-primary btn-block\" value=\"查询密码\">\n");
      out.write("          </div>\n");
      out.write("          <!-- /.col -->\n");
      out.write("        </div>\n");
      out.write("      </form>\n");
      out.write("      <p class=\"mt-3 mb-1\">\n");
      out.write("        <a href=\"login.jsp\">去登录</a>\n");
      out.write("      </p>\n");
      out.write("      <p class=\"mb-0\">\n");
      out.write("        <a href=\"register.jsp\" class=\"text-center\">注册一个新账号</a>\n");
      out.write("      </p>\n");
      out.write("    </div>\n");
      out.write("  </div>\n");
      out.write("</div>\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("<!-- jQuery -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/jquery/jquery.min.js\"></script>\r\n");
      out.write("<!-- jQuery UI 1.11.4 -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/jquery-ui/jquery-ui.min.js\"></script>\r\n");
      out.write("<!-- Resolve conflict in jQuery UI tooltip with Bootstrap tooltip -->\r\n");
      out.write("<script>\r\n");
      out.write("    $.widget.bridge('uibutton', $.ui.button)\r\n");
      out.write("</script>\r\n");
      out.write("\r\n");
      out.write("<!-- Bootstrap 4 -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/bootstrap/js/bootstrap.bundle.min.js\"></script>\r\n");
      out.write("<!-- ChartJS -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/chart.js/Chart.min.js\"></script>\r\n");
      out.write("<!-- Sparkline -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/sparklines/sparkline.js\"></script>\r\n");
      out.write("<!-- JQVMap -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/jqvmap/jquery.vmap.min.js\"></script>\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/jqvmap/maps/jquery.vmap.usa.js\"></script>\r\n");
      out.write("<!-- jQuery Knob Chart -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/jquery-knob/jquery.knob.min.js\"></script>\r\n");
      out.write("<!-- daterangepicker -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/moment/moment.min.js\"></script>\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/daterangepicker/daterangepicker.js\"></script>\r\n");
      out.write("<!-- Tempusdominus Bootstrap 4 -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/tempusdominus-bootstrap-4/js/tempusdominus-bootstrap-4.min.js\"></script>\r\n");
      out.write("<!-- Summernote -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/summernote/summernote-bs4.min.js\"></script>\r\n");
      out.write("<!-- overlayScrollbars -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/overlayScrollbars/js/jquery.overlayScrollbars.min.js\"></script>\r\n");
      out.write("<!-- AdminLTE App -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/dist/js/adminlte.js\"></script>\r\n");
      out.write("<!-- AdminLTE for demo purposes -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/dist/js/demo.js\"></script>\r\n");
      out.write("<!-- AdminLTE dashboard demo (This is only for demo purposes) -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/dist/js/pages/dashboard.js\"></script>\r\n");
      out.write("<script type=\"text/javascript\" src=\"");
      out.print( request.getContextPath());
      out.write("/js/jquery-3.6.1.min.js\"></script>\r\n");
      out.write("<script type=\"text/javascript\" src=\"");
      out.print( request.getContextPath());
      out.write("/js/jquery.min.js\"></script>");
      out.write("\n");
      out.write("</body>\n");
      out.write("</html>\n");
      out.write("<script>\n");
      out.write("  function verify()\n");
      out.write("  {\n");
      out.write("      let txtLoginName=$('input[name=txtLoginName]').val();\n");
      out.write("    console.log(txtLoginName);\n");
      out.write("    if(txtLoginName==='')\n");
      out.write("    {\n");
      out.write("      alert(\"请输入学工号\");\n");
      out.write("      return false;\n");
      out.write("    }\n");
      out.write("  }\n");
      out.write("</script>\n");
    } catch (java.lang.Throwable t) {
      if (!(t instanceof javax.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try { out.clearBuffer(); } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }
}
