<%@ page import="com.ntvu.studentmis.entity.User" %>
<%@ page import="com.ntvu.studentmis.db.DBManager" %>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <jsp:include page="../include/header_css.jsp" flush="true"/>
    <title>个人信息</title>
</head>
<body class="hold-transition sidebar-mini layout-fixed">
<div class="wrapper">
    <%@include file="header_nav.jsp"%>
    <%@include file="left_menu.jsp"%>
    <div class="content-wrapper" style="min-height: 1345.6px;">
        <!-- Content Header (Page header) -->
        <section class="content-header">
            <div class="container-fluid">
                <div class="row mb-2">
                    <div class="col-sm-6">
                        <h1>个人信息</h1>
                    </div>
                </div>
            </div><!-- /.container-fluid -->
        </section>
        <%
            String txtloginName=(String)session.getAttribute("curUserName");
            User user=new DBManager().getDetails(txtloginName);
        %>
        <!-- Main content -->
        <section class="content">
            <div class="container-fluid">
                <div class="row">
                    <!-- left column -->
                    <div class="col-md-12">
                        <!-- jquery validation -->
                        <div class="card card-primary">
                            <div class="card-header">
                                <h3 class="card-title">查看<small>个人信息</small></h3>
                            </div>
                            <!-- /.card-header -->
                            <!-- form start -->
                            <form name="form1" id="form1" method="post" action="<%= request.getContextPath() + "/teacher/teacher.jsp"%>" onsubmit="return verify()">
                                <div class="card-body">
                                    <input type="hidden" name="id" value="<%= user.getUser_id()%>">
                                    <div class="form-group">
                                        <label for="exampleInputPassword1">学工号</label>
                                        <input type="text" name="txtLoginName" class="form-control" id="exampleInputUser"  readonly="readonly" value="<%= user.getUser_num()%>">
                                    </div>
                                    <div class="form-group">
                                        <%--@declare id="exampleinputpassword1"--%><label for="exampleInputPassword1">姓名</label>
                                        <input type="text" name="txtRealName" class="form-control" id="exampleInputRealName"  readonly="readonly" value="<%= user.getUser_name()%>">
                                    </div>
                                    <div class="form-group">
                                        <label for="exampleInputPassword1">密码</label>
                                        <input type="text" name="txtLoginPassword" class="form-control" id="exampleInputPassword1" readonly="readonly" value="<%= user.getPassword()%>">
                                    </div>
                                    <div class="form-group">
                                        <label for="exampleInputPassword1">手机号</label>
                                        <input type="tel" name="txtTelephone" class="form-control" id="exampleInputTelephone" readonly="readonly" value="<%= user.getPhone()%>">
                                    </div>
                                    <div class="form-group">
                                        <label>身份</label>
                                        <%
                                            String sf="";
                                            if(user.getRole_id()==0)
                                            {
                                                sf="学生";
                                            }else if(user.getRole_id()==1)
                                            {
                                                sf="教师";
                                            }else
                                            {
                                                sf="管理员";
                                            }
                                        %>
                                        <input type="text" name="txtTelephone" class="form-control" id="exampleInputRole" readonly="readonly" value="<%=sf%>">
                                    </select>
                                    </div>
                                </div>
                                <!-- /.card-body -->
                                <div class="card-footer">
                                    <input type="submit" class="btn btn-primary" name="btnSubmit" value="退出">
                                </div>
                            </form>
                        </div>
                        <!-- /.card -->
                    </div>
                    <!--/.col (left) -->
                    <!-- right column -->
                    <div class="col-md-6">

                    </div>
                    <!--/.col (right) -->
                </div>
                <!-- /.row -->
            </div><!-- /.container-fluid -->
        </section>
        <!-- /.content -->
    </div>
    <!-- footer -->
    <%@include file="../include/foot_js.jsp"%>
</div>
</body>
</html>
