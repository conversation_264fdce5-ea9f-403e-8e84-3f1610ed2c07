<%@ page import="com.ntvu.studentmis.pager.PagerHelper" %>
<%@ page import="com.ntvu.studentmis.db.DBTeacher" %>
<%@ page import="com.ntvu.studentmis.entity.Teacher" %>
<%@ page import="com.ntvu.studentmis.util.WebTools" %>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <jsp:include page="../../include/header_css.jsp" flush="true"/>
    <title>教师信息列表</title>
</head>
<body class="hold-transition sidebar-mini layout-fixed">
<div class="wrapper">
    <%@include file="../../include/header_nav.jsp"%>
    <%@include file="../../include/left_menu.jsp"%>
    <%
        request.setCharacterEncoding("utf-8");
        String tea_num = request.getParameter("tea_num");
        String tea_name = request.getParameter("tea_name");

        PagerHelper<Teacher> pager = new PagerHelper(request);
        if (tea_num != null && !tea_num.trim().equals("")) {
            pager.getQueryParams().put("tea_num", tea_num);
        }
        if (tea_name != null && !tea_name.trim().equals("")) {
            pager.getQueryParams().put("tea_name", tea_name);
        }

        new DBTeacher().getList(pager);
    %>
    <!-- Content Wrapper. Contains page content -->
    <form id="form1" name="form1" method="post"
          action="<%= request.getContextPath() + "/admin/teacher/TeacherServlet?action=add"%>">
        <div class="content-wrapper">
            <section class="content">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <div class="row">

                                        <div class="col-lg-2">学号：<input type="text" name="tea_num"
                                                                          class="form-control" value="<%= WebTools.parseNullorEmpty(tea_num)%>"></div>
                                        <div class="col-lg-2">姓名：<input type="text" name="tea_name"
                                                                          class="form-control" value="<%= WebTools.parseNullorEmpty(tea_name)%>"></div>
                                        <div class="col-lg-2">
                                            <input type="button" class="btn btn-block btn-primary btn-xs;form-control"
                                                    style="margin-top: 25px;" name="btnFind" value="查询">
                                        </div>
                                        <div class="col-lg-2">
                                            <input type="button" class="btn btn-block btn-danger btn-xs;form-control"
                                                    style="margin-top: 25px;" name="btnDelSel" value="删除所勾选的">
                                        </div>

                                    </div>
                                    <!-- /.card-header -->
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-sm-12">
                                                <table id="example1"
                                                       class="table table-bordered table-striped dataTable dtr-inline"
                                                       aria-describedby="example1_info">
                                                    <thead>
                                                    <tr>
                                                        <th class="sorting sorting_asc" tabindex="0"
                                                            aria-controls="example1" rowspan="1" colspan="1"
                                                            aria-sort="ascending"
                                                            aria-label="Rendering engine: activate to sort column descending">
                                                            <input type="checkbox" class="form-check" title="全选" name="checkAll">
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="Platform(s): activate to sort column ascending">
                                                            序号
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="Browser: activate to sort column ascending">
                                                            工号
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="Platform(s): activate to sort column ascending">
                                                            姓名
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="CSS grade: activate to sort column ascending">
                                                            性别
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="CSS grade: activate to sort column ascending">
                                                            年龄
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="CSS grade: activate to sort column ascending">
                                                            所任课程
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="CSS grade: activate to sort column ascending">
                                                            专业
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="CSS grade: activate to sort column ascending">
                                                            院系
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="CSS grade: activate to sort column ascending">
                                                            操作
                                                        </th>
                                                    </tr>
                                                    </thead>
                                                    <tbody>
                                                    <%
                                                        int index = 0;
                                                        for (Teacher teacher : pager.getData()) {
                                                            index++;
                                                    %>
                                                    <tr class="<%= index % 2 == 1 ? "odd" : "even"%>">
                                                        <td><input type="checkbox" class="form-check" name="checkItem" value="<%= teacher.getTea_id()%>"></td>
                                                        <td><%= index%></td>
                                                        <td class="dtr-control sorting_1"
                                                            tabindex="0"><%= teacher.getTea_num()%>
                                                        </td>
                                                        <td><%= teacher.getTea_name()%>
                                                        </td>
                                                        <td><%= teacher.getTea_sex()%>
                                                        </td>
                                                        <td><%= teacher.getTea_age()%>
                                                        </td>
                                                        <td><%= teacher.getTea_course()%></td>
                                                        <td><%= teacher.getMajor()%></td>
                                                        <td><%= teacher.getDepartment()%></td>
                                                        <td style="width: 200px">
                                                            <div class="row">
                                                                <div class="col-6">
                                                                    <button type="button"
                                                                            class="btn btn-block btn-primary btn-xs"
                                                                            style="width: 80px" onclick="window.location.href = '<%= contextPath +"/admin/teacher/edit.jsp?id=" + teacher.getTea_id()%>';">编辑
                                                                    </button>
                                                                </div>
                                                                <div class="col-6">
                                                                    <button type="button"
                                                                            class="btn btn-block btn-danger btn-xs"
                                                                            style="width: 80px" onclick="if(confirm('当前操作不可恢复，确认删除吗？')){
                                                                            window.location.href='<%= contextPath +"/admin/teacher/TeacherServlet?action=delete&id=" + teacher.getTea_id()%>';}">删除
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    <%
                                                        }
                                                    %>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-5">
                                                <div class="dataTables_info" id="example1_info" role="status"
                                                     aria-live="polite">每页显示10条记录
                                                </div>
                                            </div>
                                            <div class="col-sm-12 col-md-7">
                                                <%@ include file="../../include/pager_footer.jsp"%>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- /.card-body -->
                            </div>
                            <!-- /.card -->
                        </div>
                        <!-- /.col -->
                    </div>
                    <!-- /.row -->
                </div>
                <!-- /.container-fluid -->
            </section>
        </div>
    </form>
</div>
<%@include file="../../include/foot_js.jsp"%>
<script>
    $(function (){
        //绑定勾选框按钮事件
        $('input[name=checkAll]').bind('change',function (){
            console.log('checkAll');
            let checked=$(this).prop('checked');
            //更改表格中所有chkItem
            $('input[name=checkItem]').each(function (){
                console.log('checkItem');
                $(this).prop('checked',checked);
            });
        });
        //绑定删除所有按钮事件
        $('input[name=btnDelSel]').bind('click',function (){
            let ids='';
            $('input[name=checkItem]').each(function (){
                if( $(this).prop('checked')===true)
                {
                    ids+=$(this).val()+',';
                }
            });
            if(ids.length>0)
            {
                if(confirm('当前操作不可恢复,确认要删除吗?'))
                {
                    console.log(ids);
                    window.location.href='<%=contextPath+"/admin/teacher/TeacherServlet?action=deleteSelected&ids="%>'+ids;
                }
            }else {
                alert('请选择待删除项');
            }

        });
        $('input[name=btnFind]').bind('click',function (){
            $('#form1').attr('action','<%= request.getContextPath() + "/admin/teacher/list.jsp"%>');
            $(`#form1`).submit();
        });
    });
    /**
     * 跳转到指定的页
     * @param toPageIndex
     */
        //本页地址
    let pageListUrl = '/admin/teacher/list.jsp';
    function doPager(toPageIndex)
    {
        $('#form1').attr('action','<%= request.getContextPath() %>' + pageListUrl + '?pageIndex=' + toPageIndex);
        $('#form1').submit();
    }
</script>

</body>
</html>
