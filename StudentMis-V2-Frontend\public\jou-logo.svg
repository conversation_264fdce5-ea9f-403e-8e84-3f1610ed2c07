<?xml version="1.0" encoding="UTF-8"?>
<svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 渐变定义 -->
  <defs>
    <linearGradient id="oceanGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0EA5E9;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#0284C7;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0369A1;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="waveGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#38BDF8;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#0284C7;stop-opacity:1" />
    </linearGradient>
    <radialGradient id="centerGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#FFFFFF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F0F9FF;stop-opacity:0.95" />
    </radialGradient>
  </defs>

  <!-- 外圆背景 -->
  <circle cx="60" cy="60" r="58" fill="url(#oceanGradient)" stroke="#0369A1" stroke-width="2"/>

  <!-- 海浪图案 - 第一层 -->
  <path d="M15 75 Q30 65 45 75 T75 75 Q90 65 105 75 L105 105 Q105 110 100 110 L20 110 Q15 110 15 105 Z" fill="url(#waveGradient)"/>

  <!-- 海浪图案 - 第二层 -->
  <path d="M10 85 Q25 75 40 85 T70 85 Q85 75 100 85 L100 110 L10 110 Z" fill="#0284C7"/>

  <!-- 海浪图案 - 第三层（泡沫效果） -->
  <path d="M20 90 Q35 82 50 90 T80 90 Q90 85 100 90" stroke="#FFFFFF" stroke-width="2" fill="none" opacity="0.6"/>

  <!-- 船舶轮廓 -->
  <path d="M35 65 L85 65 L80 55 L40 55 Z" fill="#FFFFFF" stroke="#0284C7" stroke-width="1"/>
  <rect x="50" y="45" width="4" height="10" fill="#FFFFFF" stroke="#0284C7" stroke-width="1"/>
  <rect x="65" y="40" width="4" height="15" fill="#FFFFFF" stroke="#0284C7" stroke-width="1"/>

  <!-- 船帆 -->
  <path d="M52 45 Q58 35 52 25 L52 45" fill="#F0F9FF" stroke="#0284C7" stroke-width="1"/>
  <path d="M67 40 Q73 30 67 20 L67 40" fill="#F0F9FF" stroke="#0284C7" stroke-width="1"/>

  <!-- 中心文字区域 -->
  <circle cx="60" cy="35" r="25" fill="url(#centerGradient)" stroke="#0284C7" stroke-width="1"/>

  <!-- 江苏海洋大学文字 -->
  <text x="60" y="30" text-anchor="middle" font-family="serif" font-size="9" font-weight="bold" fill="#0369A1">江苏海洋</text>
  <text x="60" y="42" text-anchor="middle" font-family="serif" font-size="9" font-weight="bold" fill="#0369A1">大学</text>

  <!-- 装饰性海洋元素 -->
  <circle cx="25" cy="30" r="3" fill="#38BDF8" opacity="0.7"/>
  <circle cx="95" cy="35" r="2" fill="#0EA5E9" opacity="0.8"/>
  <circle cx="30" cy="45" r="2" fill="#0284C7" opacity="0.6"/>
  <circle cx="90" cy="50" r="2" fill="#0369A1" opacity="0.7"/>

  <!-- 海鸥装饰 -->
  <path d="M20 25 Q22 23 24 25 Q22 27 20 25" fill="#FFFFFF" opacity="0.8"/>
  <path d="M95 25 Q97 23 99 25 Q97 27 95 25" fill="#FFFFFF" opacity="0.8"/>

  <!-- 边框装饰 -->
  <circle cx="60" cy="60" r="58" fill="none" stroke="#38BDF8" stroke-width="1" stroke-dasharray="3,2" opacity="0.4"/>

  <!-- 高光效果 -->
  <circle cx="45" cy="25" r="8" fill="#FFFFFF" opacity="0.2"/>
</svg>
