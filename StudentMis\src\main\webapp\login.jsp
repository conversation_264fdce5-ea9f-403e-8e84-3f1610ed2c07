<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <jsp:include page="include/header_css.jsp" flush="true"/>
  <title>学生成绩管理系统</title>
</head>
<body class="hold-transition login-page">
<div class="login-box">
  <div class="login-logo">
    学生成绩管理系统
  </div>
  <!-- /.login-logo -->
  <div class="card">
    <div class="card-body login-card-body">
      <form action="Login" method="post" onsubmit="return verify()" id="form1">
        <div class="input-group mb-3">
          <input name="txtUserName" type="text" class="form-control" placeholder="请输入学工号" value="<%= session.getAttribute("user_num")==null?"":session.getAttribute("user_num")%>" >
          <div class="input-group-append">
            <div class="input-group-text">
              <span class="fas fa-user"></span>
            </div>
          </div>
        </div>
        <div class="input-group mb-3">
          <input name="txtPassword" type="password" class="form-control" placeholder="请输入密码" value="<%= session.getAttribute("password")==null?"":session.getAttribute("password")%>">
          <div class="input-group-append">
            <div class="input-group-text">
              <span class="fas fa-lock"></span>
            </div>
          </div>
        </div>
        <div class="row">
          <!-- /.col -->
          <div class="col-3"></div>
          <div class="col-6">
            <input type="submit" class="btn btn-primary btn-block" value="登录">
          </div>
          <div class="col-3"></div>
          <!-- /.col -->
        </div>
        <div class="row">
          <div class="col-3"></div>
          <div class="col-6" style="color: red">
            <%
              Object obj = request.getSession().getAttribute("errorMsg");
              if (obj==null)
              {
                obj="";
              }
            %>
            <%=obj%>
          </div>
          <div class="col-3"></div>
        </div>
      </form>
      <div class="row">
          <div class="col-9">
            <a href="forgot_password.jsp" class="text-center">忘记密码?</a>
          </div>
          <div class="col-3">
          <a href="register.jsp" class="text-center">注册账号</a>
          </div>
      </div>

      <!-- 测试账号提示 -->
      <div class="row mt-3">
        <div class="col-12">
          <div class="alert alert-info" style="font-size: 12px; padding: 8px;">
            <strong>测试账号：</strong><br>
            <span style="color: #dc3545;">管理员：</span> admin / 123456<br>
            <span style="color: #28a745;">教师：</span> T001 / 123456<br>
            <span style="color: #007bff;">学生：</span> 2024001 / 123456
          </div>
        </div>
      </div>
    </div>
    <!-- /.login-card-body -->
  </div>
</div>
<!-- /.login-box -->
</body>
</html>
<%@include file="include/foot_js.jsp"%>
<script type="text/javascript">
  //绑定按钮事件
  function verify() {
    console.log(`click`);
    //对数据进行检验
    let txtUserName=$(`input[name=txtUserName]`).val();
    if(txtUserName==='')
    {
      alert(`登录名称不能为空`);
      $(`input[name=txtUserName]`).focus();//光标选中
      return false;
    }
    let txtPassword=$(`input[name=txtPassword]`).val();
    if(txtPassword==='')
    {
      alert(`密码不能为空`);
      $(`input[name=txtPassword]`).focus();//光标选中
      return false;
    }
  }
</script>
