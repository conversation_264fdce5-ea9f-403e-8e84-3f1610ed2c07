import { createRouter, createWebHistory } from 'vue-router'
import LoginView from '../views/LoginView.vue'
import DashboardView from '../views/DashboardView.vue'
import MainLayout from '../components/Layout/MainLayout.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/login'
    },
    {
      path: '/login',
      name: 'login',
      component: LoginView,
    },
    {
      path: '/dashboard',
      component: MainLayout,
      children: [
        {
          path: '',
          name: 'dashboard',
          component: DashboardView,
        }
      ]
    },
    {
      path: '/students',
      component: MainLayout,
      children: [
        {
          path: '',
          name: 'students',
          component: () => import('../views/StudentsViewNew.vue'),
        }
      ]
    },
    {
      path: '/grades',
      component: MainLayout,
      children: [
        {
          path: '',
          name: 'grades',
          component: () => import('../views/GradesView.vue'),
        }
      ]
    },
    {
      path: '/analytics',
      component: MainLayout,
      children: [
        {
          path: '',
          name: 'analytics',
          component: () => import('../views/AnalyticsView.vue'),
        }
      ]
    },
    {
      path: '/courses',
      component: MainLayout,
      children: [
        {
          path: '',
          name: 'courses',
          component: () => import('../views/CoursesView.vue'),
        }
      ]
    },
    {
      path: '/users',
      component: MainLayout,
      children: [
        {
          path: '',
          name: 'users',
          component: () => import('../views/UsersView.vue'),
        }
      ]
    },
    {
      path: '/roles',
      component: MainLayout,
      children: [
        {
          path: '',
          name: 'roles',
          component: () => import('../views/RolesView.vue'),
        }
      ]
    },
    {
      path: '/settings',
      component: MainLayout,
      children: [
        {
          path: '',
          name: 'settings',
          component: () => import('../views/SettingsView.vue'),
        }
      ]
    },
    {
      path: '/logs',
      component: MainLayout,
      children: [
        {
          path: '',
          name: 'logs',
          component: () => import('../views/LogsView.vue'),
        }
      ]
    }
  ],
})

export default router
