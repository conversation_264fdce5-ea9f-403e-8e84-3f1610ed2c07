# StudentMIS V2 - 江苏海洋大学学生成绩管理系统

## 项目简介

StudentMIS V2 是一个基于 Spring Boot 3.2 + Vue 3 + TypeScript 构建的现代化学生成绩管理系统，专为江苏海洋大学设计，提供全面的学生信息管理、成绩管理、数据分析等功能。

## 技术栈

### 后端技术
- **框架**: Spring Boot 3.2.0
- **微服务**: Spring Cloud 2023.0.0
- **服务注册**: Nacos 2.3.0
- **数据库**: MySQL 8.0
- **缓存**: Redis 7.0
- **ORM**: MyBatis Plus 3.5.4
- **连接池**: Druid 1.2.20
- **安全**: JWT + Spring Security
- **API文档**: Knife4j 4.4.0
- **监控**: Spring Boot Actuator + Micrometer

### 前端技术
- **框架**: Vue 3.4.0
- **语言**: TypeScript 5.3.3
- **构建工具**: Vite 5.0.10
- **UI组件**: Element Plus 2.4.4
- **状态管理**: Pinia 2.1.7
- **路由**: Vue Router 4.2.5
- **HTTP客户端**: Axios 1.6.2
- **图表**: ECharts 5.4.3

### 数据分析
- **机器学习**: Weka 3.8.6
- **数学计算**: Apache Commons Math 3.6.1
- **统计分析**: Apache Commons Statistics 1.0

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用      │    │   API网关       │    │   微服务集群    │
│   Vue 3 + TS    │───▶│   Gateway       │───▶│   Auth/Student  │
│   Element Plus  │    │   路由/限流     │    │   Grade/Analytics│
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                ▲                       ▲
                                │                       │
                       ┌─────────────────┐    ┌─────────────────┐
                       │   服务注册      │    │   数据存储      │
                       │   Nacos         │    │   MySQL + Redis │
                       └─────────────────┘    └─────────────────┘
```

## 环境要求

### 开发环境
- **JDK**: 17 或更高版本
- **Node.js**: 18 或更高版本
- **Maven**: 3.8 或更高版本
- **MySQL**: 8.0 或更高版本
- **Redis**: 7.0 或更高版本

### 推荐IDE
- **后端**: IntelliJ IDEA 2023.3+
- **前端**: VS Code + Volar 插件

## 快速开始

### 1. 环境准备

#### 安装 JDK 17
```bash
# Windows (使用 Chocolatey)
choco install openjdk17

# macOS (使用 Homebrew)
brew install openjdk@17

# Linux (Ubuntu/Debian)
sudo apt update
sudo apt install openjdk-17-jdk
```

#### 安装 Node.js 18
```bash
# 下载并安装 Node.js 18 LTS
# https://nodejs.org/

# 验证安装
node --version
npm --version
```

#### 安装 MySQL 8.0
```bash
# Windows: 下载 MySQL Installer
# https://dev.mysql.com/downloads/installer/

# macOS
brew install mysql

# Linux (Ubuntu/Debian)
sudo apt update
sudo apt install mysql-server-8.0
```

#### 安装 Redis 7.0
```bash
# Windows: 下载 Redis for Windows
# https://github.com/microsoftarchive/redis/releases

# macOS
brew install redis

# Linux (Ubuntu/Debian)
sudo apt update
sudo apt install redis-server
```

### 2. 数据库初始化

#### 创建数据库
```sql
-- 连接到 MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE studentmis_v2 CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'studentmis'@'localhost' IDENTIFIED BY 'StudentMIS@2024';
GRANT ALL PRIVILEGES ON studentmis_v2.* TO 'studentmis'@'localhost';
FLUSH PRIVILEGES;
```

#### 导入数据库结构
```bash
# 进入项目目录
cd StudentMis-V2

# 导入数据库结构和初始数据
mysql -u studentmis -p studentmis_v2 < studentmis_v2_database.sql
```

### 3. 后端服务启动

#### 启动 Nacos (服务注册中心)
```bash
# 下载 Nacos 2.3.0
wget https://github.com/alibaba/nacos/releases/download/2.3.0/nacos-server-2.3.0.tar.gz

# 解压
tar -xzf nacos-server-2.3.0.tar.gz

# 启动 (单机模式)
cd nacos/bin
./startup.sh -m standalone

# Windows
startup.cmd -m standalone
```

#### 配置应用参数
```bash
# 复制配置文件模板
cp src/main/resources/application-dev.yml.template src/main/resources/application-dev.yml

# 编辑配置文件，修改数据库连接信息
vim src/main/resources/application-dev.yml
```

#### 启动微服务
```bash
# 1. 启动网关服务
cd studentmis-gateway
mvn spring-boot:run

# 2. 启动认证服务
cd ../studentmis-auth
mvn spring-boot:run

# 3. 启动学生服务
cd ../studentmis-student
mvn spring-boot:run

# 4. 启动成绩服务
cd ../studentmis-grade
mvn spring-boot:run

# 5. 启动数据分析服务
cd ../studentmis-analytics
mvn spring-boot:run
```

### 4. 前端应用启动

```bash
# 进入前端目录
cd StudentMis-V2-Frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 5. 访问系统

- **前端应用**: http://localhost:3000
- **API网关**: http://localhost:8080
- **Nacos控制台**: http://localhost:8848/nacos (用户名/密码: nacos/nacos)
- **API文档**: http://localhost:8080/doc.html

### 6. 默认账号

- **超级管理员**: admin / 123456
- **测试学生**: 2024001001 / 123456
- **测试教师**: T001 / 123456

## 项目结构

```
StudentMis-V2/
├── studentmis-common/          # 公共模块
├── studentmis-gateway/         # API网关
├── studentmis-auth/           # 认证服务
├── studentmis-student/        # 学生管理服务
├── studentmis-grade/          # 成绩管理服务
├── studentmis-analytics/      # 数据分析服务
├── studentmis-notification/   # 通知服务
├── pom.xml                    # 父级POM文件
└── studentmis_v2_database.sql # 数据库脚本

StudentMis-V2-Frontend/
├── src/
│   ├── api/                   # API接口
│   ├── components/            # 公共组件
│   ├── views/                 # 页面组件
│   ├── stores/                # 状态管理
│   ├── router/                # 路由配置
│   ├── utils/                 # 工具函数
│   └── styles/                # 样式文件
├── public/                    # 静态资源
└── package.json               # 依赖配置
```

## 功能特性

### 核心功能
- ✅ 用户认证与授权 (JWT + RBAC)
- ✅ 学生信息管理 (全生命周期)
- ✅ 成绩管理 (多元化评价)
- ✅ 课程管理 (选课、排课)
- ✅ 数据分析 (智能预测)
- ✅ 个性化推荐
- ✅ 系统监控

### 技术特性
- 🚀 微服务架构
- 🔐 安全认证体系
- 📊 智能数据分析
- 🎯 个性化推荐
- 📱 响应式设计
- 🌐 国际化支持
- 📈 性能监控

## 开发指南

### 代码规范
- 后端遵循阿里巴巴Java开发手册
- 前端遵循Vue官方风格指南
- 使用ESLint + Prettier进行代码格式化

### 提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

### API文档
访问 http://localhost:8080/doc.html 查看完整的API文档

## 部署指南

### 生产环境部署
1. 修改配置文件中的数据库连接信息
2. 构建项目: `mvn clean package`
3. 部署jar包到服务器
4. 配置nginx反向代理
5. 启动服务

### 性能优化建议
- 数据库连接池调优
- Redis缓存策略优化
- JVM参数调优
- 前端资源压缩

## 故障排除

### 常见问题

#### 1. 数据库连接失败
```bash
# 检查MySQL服务状态
systemctl status mysql

# 检查端口占用
netstat -tlnp | grep 3306

# 检查用户权限
mysql -u studentmis -p
```

#### 2. Nacos连接失败
```bash
# 检查Nacos服务状态
ps aux | grep nacos

# 检查端口占用
netstat -tlnp | grep 8848
```

#### 3. 前端构建失败
```bash
# 清除node_modules重新安装
rm -rf node_modules package-lock.json
npm install

# 检查Node.js版本
node --version
```

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

- 项目维护者: StudentMIS Team
- 邮箱: <EMAIL>
- 项目地址: https://github.com/tsinghua/studentmis-v2

## 更新日志

### v2.0.0 (2024-12-19)
- 🎉 全新架构设计
- ✨ 微服务化改造
- 🚀 Vue3 + TypeScript前端
- 🤖 AI智能分析功能
- 📊 数据可视化大屏
- 🔐 增强安全机制
