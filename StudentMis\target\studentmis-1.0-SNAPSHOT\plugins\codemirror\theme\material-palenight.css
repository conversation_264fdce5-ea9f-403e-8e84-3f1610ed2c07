/*
  Name:       material
  Author:     <PERSON><PERSON> (http://github.com/equinusocio)
  Website:    https://material-theme.site/
*/

.cm-s-material-palenight.CodeMirror {
  background-color: #292D3E;
  color: #A6ACCD;
}

.cm-s-material-palenight .CodeMirror-gutters {
  background: #292D3E;
  color: #676E95;
  border: none;
}

.cm-s-material-palenight .CodeMirror-guttermarker,
.cm-s-material-palenight .CodeMirror-guttermarker-subtle,
.cm-s-material-palenight .CodeMirror-linenumber {
  color: #676E95;
}

.cm-s-material-palenight .CodeMirror-cursor {
  border-left: 1px solid #FFCC00;
}
.cm-s-material-palenight.cm-fat-cursor .CodeMirror-cursor {
  background-color: #607c8b80 !important;
}
.cm-s-material-palenight .cm-animate-fat-cursor {
  background-color: #607c8b80 !important;
}

.cm-s-material-palenight div.CodeMirror-selected {
  background: rgba(113, 124, 180, 0.2);
}

.cm-s-material-palenight.CodeMirror-focused div.CodeMirror-selected {
  background: rgba(113, 124, 180, 0.2);
}

.cm-s-material-palenight .CodeMirror-line::selection,
.cm-s-material-palenight .CodeMirror-line>span::selection,
.cm-s-material-palenight .CodeMirror-line>span>span::selection {
  background: rgba(128, 203, 196, 0.2);
}

.cm-s-material-palenight .CodeMirror-line::-moz-selection,
.cm-s-material-palenight .CodeMirror-line>span::-moz-selection,
.cm-s-material-palenight .CodeMirror-line>span>span::-moz-selection {
  background: rgba(128, 203, 196, 0.2);
}

.cm-s-material-palenight .CodeMirror-activeline-background {
  background: rgba(0, 0, 0, 0.5);
}

.cm-s-material-palenight .cm-keyword {
  color: #C792EA;
}

.cm-s-material-palenight .cm-operator {
  color: #89DDFF;
}

.cm-s-material-palenight .cm-variable-2 {
  color: #EEFFFF;
}

.cm-s-material-palenight .cm-variable-3,
.cm-s-material-palenight .cm-type {
  color: #f07178;
}

.cm-s-material-palenight .cm-builtin {
  color: #FFCB6B;
}

.cm-s-material-palenight .cm-atom {
  color: #F78C6C;
}

.cm-s-material-palenight .cm-number {
  color: #FF5370;
}

.cm-s-material-palenight .cm-def {
  color: #82AAFF;
}

.cm-s-material-palenight .cm-string {
  color: #C3E88D;
}

.cm-s-material-palenight .cm-string-2 {
  color: #f07178;
}

.cm-s-material-palenight .cm-comment {
  color: #676E95;
}

.cm-s-material-palenight .cm-variable {
  color: #f07178;
}

.cm-s-material-palenight .cm-tag {
  color: #FF5370;
}

.cm-s-material-palenight .cm-meta {
  color: #FFCB6B;
}

.cm-s-material-palenight .cm-attribute {
  color: #C792EA;
}

.cm-s-material-palenight .cm-property {
  color: #C792EA;
}

.cm-s-material-palenight .cm-qualifier {
  color: #DECB6B;
}

.cm-s-material-palenight .cm-variable-3,
.cm-s-material-palenight .cm-type {
  color: #DECB6B;
}


.cm-s-material-palenight .cm-error {
  color: rgba(255, 255, 255, 1.0);
  background-color: #FF5370;
}

.cm-s-material-palenight .CodeMirror-matchingbracket {
  text-decoration: underline;
  color: white !important;
}
