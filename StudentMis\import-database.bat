@echo off
chcp 65001 >nul
echo ========================================
echo   学生成绩管理系统 - 数据库导入工具
echo ========================================
echo.
echo 正在导入完整数据库...
echo.
echo 请确保：
echo 1. MySQL服务已启动
echo 2. root密码是 xhxabc
echo.

mysql -u root -pxhxabc -e "CREATE DATABASE IF NOT EXISTS studentmis_db;"
mysql -u root -pxhxabc studentmis_db < "../studentmis_db.sql"

if %errorlevel% == 0 (
    echo.
    echo ✅ 数据库导入成功！
    echo.
    echo 可用的测试账号：
    echo 管理员：1000 / Lcd123
    echo 教师：  1123 / Lcd123
    echo 学生：  170340 / Lcd123
    echo.
    echo 现在可以启动服务器了！
) else (
    echo.
    echo ❌ 数据库导入失败！
    echo 请检查MySQL服务是否启动，密码是否正确。
)

echo.
pause
