

<%@ page import="com.ntvu.studentmis.entity.User" %>
<%@ page import="com.ntvu.studentmis.db.DBManager" %><%--
  Created by IntelliJ IDEA.
  User: Administrator
  Date: 2022/12/27
  Time: 17:12
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!-- Main Sidebar Container -->
<aside class="main-sidebar sidebar-dark-primary elevation-4">
    <!-- Brand Logo -->
    <!-- Sidebar -->
    <div class="sidebar">
        <!-- Sidebar user panel (optional) -->
        <div class="user-panel mt-3 pb-3 mb-3 d-flex">
            <%
                String login_name=(String)session.getAttribute("curUserName");
                User g_user=new DBManager().getDetails(login_name);
            %>
            <div class="image">
                <%--../../dist/img/user2-160x160.jpg--%>
                <img src="<%= request.getContextPath() + "/uploadPic/user2.jpg"%>" class="img-circle elevation-2" alt="User Image">
            </div>
            <div class="info">
                <a href="<%= request.getContextPath() + "/teacher/info.jsp"%>" class="d-block" style="color: white"><%= session.getAttribute("curUserName")%></a>
            </div>
        </div>

        <!-- Sidebar Menu -->
        <nav class="mt-2">
            <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">

                <li class="nav-item">
                    <a href="#" class="nav-link active">
                        <i class="nav-icon fas fa-user"></i>
                        <p>
                            学生成绩管理
                            <i class="right fas fa-angle-left"></i>
                        </p>
                    </a>
                    <ul class="nav nav-treeview">
                        <li class="nav-item">
                            <a href="<%= request.getContextPath() + "/teacher/score/list.jsp"%>" class="nav-link">
                                <i class="far fa-circle nav-icon"></i>
                                <p>学生成绩列表</p>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="<%= request.getContextPath() + "/teacher/score/add.jsp"%>" class="nav-link">
                                <i class="far fa-circle nav-icon"></i>
                                <p>添加学生成绩</p>
                            </a>
                        </li>
                    </ul>
                </li>

                <li class="nav-item">
                    <a href="#" class="nav-link active">
                        <i class="nav-icon fas fa-user"></i>
                        <p>
                            学生信息管理
                            <i class="right fas fa-angle-left"></i>
                        </p>
                    </a>
                    <ul class="nav nav-treeview">
                        <li class="nav-item">
                            <a href="<%= request.getContextPath() + "/teacher/student/list.jsp"%>" class="nav-link">
                                <i class="far fa-circle nav-icon"></i>
                                <p>学生信息列表</p>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="<%= request.getContextPath() + "/teacher/student/add.jsp"%>" class="nav-link">
                                <i class="far fa-circle nav-icon"></i>
                                <p>添加学生信息</p>
                            </a>
                        </li>
                    </ul>
                </li>
            </ul>
        </nav>
        <!-- /.sidebar-menu -->
    </div>
    <!-- /.sidebar -->
</aside>

