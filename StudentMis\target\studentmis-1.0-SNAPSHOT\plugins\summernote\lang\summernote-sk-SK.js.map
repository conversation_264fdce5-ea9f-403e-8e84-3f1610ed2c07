{"version": 3, "file": "lang/summernote-sk-SK.js", "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAT,EAAY;AACXA,EAAAA,CAAC,CAACC,MAAF,CAASD,CAAC,CAACE,UAAF,CAAaC,IAAtB,EAA4B;AAC1B,aAAS;AACPC,MAAAA,IAAI,EAAE;AACJC,QAAAA,IAAI,EAAE,OADF;AAEJC,QAAAA,MAAM,EAAE,SAFJ;AAGJC,QAAAA,SAAS,EAAE,eAHP;AAIJC,QAAAA,KAAK,EAAE,sBAJH;AAKJC,QAAAA,MAAM,EAAE,cALJ;AAMJC,QAAAA,IAAI,EAAE,OANF;AAOJC,QAAAA,aAAa,EAAE,cAPX;AAQJC,QAAAA,SAAS,EAAE,WARP;AASJC,QAAAA,WAAW,EAAE,aATT;AAUJC,QAAAA,IAAI,EAAE;AAVF,OADC;AAaPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,SADF;AAELC,QAAAA,MAAM,EAAE,gBAFH;AAGLC,QAAAA,UAAU,EAAE,iBAHP;AAILC,QAAAA,UAAU,EAAE,mBAJP;AAKLC,QAAAA,aAAa,EAAE,oBALV;AAMLC,QAAAA,SAAS,EAAE,kBANN;AAOLC,QAAAA,UAAU,EAAE,mBAPP;AAQLC,QAAAA,SAAS,EAAE,gBARN;AASLC,QAAAA,YAAY,EAAE,gBATT;AAULC,QAAAA,WAAW,EAAE,YAVR;AAWLC,QAAAA,cAAc,EAAE,cAXX;AAYLC,QAAAA,SAAS,EAAE,cAZN;AAaLC,QAAAA,aAAa,EAAE,wBAbV;AAcLC,QAAAA,SAAS,EAAE,mCAdN;AAeLC,QAAAA,eAAe,EAAE,cAfZ;AAgBLC,QAAAA,eAAe,EAAE,0BAhBZ;AAiBLC,QAAAA,oBAAoB,EAAE,2CAjBjB;AAkBLC,QAAAA,GAAG,EAAE,aAlBA;AAmBLC,QAAAA,WAAW,EAAE,mBAnBR;AAoBLC,QAAAA,QAAQ,EAAE;AApBL,OAbA;AAmCPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,SAAS,EAAE,aAFN;AAGLpB,QAAAA,MAAM,EAAE,cAHH;AAILgB,QAAAA,GAAG,EAAE,YAJA;AAKLK,QAAAA,SAAS,EAAE;AALN,OAnCA;AA0CPC,MAAAA,IAAI,EAAE;AACJA,QAAAA,IAAI,EAAE,OADF;AAEJtB,QAAAA,MAAM,EAAE,gBAFJ;AAGJuB,QAAAA,MAAM,EAAE,cAHJ;AAIJC,QAAAA,IAAI,EAAE,SAJF;AAKJC,QAAAA,aAAa,EAAE,kBALX;AAMJT,QAAAA,GAAG,EAAE,yCAND;AAOJU,QAAAA,eAAe,EAAE,sBAPb;AAQJC,QAAAA,WAAW,EAAE;AART,OA1CC;AAoDPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,SADF;AAELC,QAAAA,WAAW,EAAE,mBAFR;AAGLC,QAAAA,WAAW,EAAE,mBAHR;AAILC,QAAAA,UAAU,EAAE,qBAJP;AAKLC,QAAAA,WAAW,EAAE,sBALR;AAMLC,QAAAA,MAAM,EAAE,kBANH;AAOLC,QAAAA,MAAM,EAAE,kBAPH;AAQLC,QAAAA,QAAQ,EAAE;AARL,OApDA;AA8DPC,MAAAA,EAAE,EAAE;AACFpC,QAAAA,MAAM,EAAE;AADN,OA9DG;AAiEPqC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,MADF;AAELC,QAAAA,CAAC,EAAE,UAFE;AAGLC,QAAAA,UAAU,EAAE,SAHP;AAILC,QAAAA,GAAG,EAAE,KAJA;AAKLC,QAAAA,EAAE,EAAE,UALC;AAMLC,QAAAA,EAAE,EAAE,UANC;AAOLC,QAAAA,EAAE,EAAE,UAPC;AAQLC,QAAAA,EAAE,EAAE,UARC;AASLC,QAAAA,EAAE,EAAE,UATC;AAULC,QAAAA,EAAE,EAAE;AAVC,OAjEA;AA6EPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,SAAS,EAAE,kBADN;AAELC,QAAAA,OAAO,EAAE;AAFJ,OA7EA;AAiFPC,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,OADC;AAEPC,QAAAA,UAAU,EAAE,gBAFL;AAGPC,QAAAA,QAAQ,EAAE;AAHH,OAjFF;AAsFPC,MAAAA,SAAS,EAAE;AACTA,QAAAA,SAAS,EAAE,OADF;AAETC,QAAAA,OAAO,EAAE,mBAFA;AAGTC,QAAAA,MAAM,EAAE,mBAHC;AAITC,QAAAA,IAAI,EAAE,iBAJG;AAKTC,QAAAA,MAAM,EAAE,mBALC;AAMTC,QAAAA,KAAK,EAAE,kBANE;AAOTC,QAAAA,OAAO,EAAE;AAPA,OAtFJ;AA+FPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,MAAM,EAAE,gBADH;AAELC,QAAAA,IAAI,EAAE,cAFD;AAGLC,QAAAA,UAAU,EAAE,eAHP;AAILC,QAAAA,UAAU,EAAE,aAJP;AAKLC,QAAAA,WAAW,EAAE,cALR;AAMLC,QAAAA,cAAc,EAAE,uBANX;AAOLC,QAAAA,KAAK,EAAE,SAPF;AAQLC,QAAAA,cAAc,EAAE;AARX,OA/FA;AAyGPC,MAAAA,QAAQ,EAAE;AACRC,QAAAA,SAAS,EAAE,mBADH;AAERC,QAAAA,KAAK,EAAE,SAFC;AAGRC,QAAAA,cAAc,EAAE,oBAHR;AAIRC,QAAAA,MAAM,EAAE,OAJA;AAKRC,QAAAA,mBAAmB,EAAE,qBALb;AAMRC,QAAAA,aAAa,EAAE,gBANP;AAORC,QAAAA,SAAS,EAAE;AAPH,OAzGH;AAkHP1B,MAAAA,IAAI,EAAE;AACJ,2BAAmB,cADf;AAEJ,gBAAQ,sBAFJ;AAGJ,gBAAQ,uBAHJ;AAIJ,eAAO,SAJH;AAKJ,iBAAS,mBALL;AAMJ,gBAAQ,OANJ;AAOJ,kBAAU,SAPN;AAQJ,qBAAa,eART;AASJ,yBAAiB,kBATb;AAUJ,wBAAgB,wBAVZ;AAWJ,uBAAe,iBAXX;AAYJ,yBAAiB,aAZb;AAaJ,wBAAgB,kBAbZ;AAcJ,uBAAe,mBAdX;AAeJ,+BAAuB,kBAfnB;AAgBJ,6BAAqB,gBAhBjB;AAiBJ,mBAAW,oCAjBP;AAkBJ,kBAAU,wBAlBN;AAmBJ,sBAAc,sDAnBV;AAoBJ,oBAAY,sCApBR;AAqBJ,oBAAY,sCArBR;AAsBJ,oBAAY,sCAtBR;AAuBJ,oBAAY,sCAvBR;AAwBJ,oBAAY,sCAxBR;AAyBJ,oBAAY,sCAzBR;AA0BJ,gCAAwB,8BA1BpB;AA2BJ,2BAAmB;AA3Bf,OAlHC;AA+IP2B,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,WADC;AAEPC,QAAAA,IAAI,EAAE;AAFC,OA/IF;AAmJPC,MAAAA,WAAW,EAAE;AACXA,QAAAA,WAAW,EAAE,iBADF;AAEXC,QAAAA,MAAM,EAAE;AAFG;AAnJN;AADiB,GAA5B;AA0JD,CA3JD,EA2JGC,MA3JH", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-sk-SK.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, function() {\nreturn ", "(function($) {\n  $.extend($.summernote.lang, {\n    'sk-SK': {\n      font: {\n        bold: 'Tučné',\n        italic: '<PERSON><PERSON><PERSON><PERSON>',\n        underline: 'Podčiarknutie',\n        clear: 'Odstrániť štýl písma',\n        height: '<PERSON><PERSON><PERSON><PERSON> riadku',\n        name: '<PERSON><PERSON><PERSON><PERSON>',\n        strikethrough: 'Prečiarknuté',\n        subscript: 'Subscript',\n        superscript: 'Superscript',\n        size: 'Veľkosť písma',\n      },\n      image: {\n        image: 'Obr<PERSON>zo<PERSON>',\n        insert: 'Vloži<PERSON> obrázok',\n        resizeFull: 'Pôvodná veľkosť',\n        resizeHalf: 'Polovičná veľkosť',\n        resizeQuarter: 'Štv<PERSON><PERSON><PERSON><PERSON> veľkosť',\n        floatLeft: 'Umiestniť doľava',\n        floatRight: 'Umiestniť doprava',\n        floatNone: 'Bez zarovnania',\n        shapeRounded: 'Tvar: Zaoblené',\n        shapeCircle: 'Tvar: Kruh',\n        shapeThumbnail: 'Tvar: Náhľad',\n        shapeNone: 'Tvar: Žiadne',\n        dragImageHere: 'Pretiahnuť sem obrázok',\n        dropImage: 'Pretiahnuť sem obrázok alebo text',\n        selectFromFiles: 'Vybrať súbor',\n        maximumFileSize: 'Maximálna veľkosť súboru',\n        maximumFileSizeError: 'Maximálna veľkosť súboru bola prekročená.',\n        url: 'URL obrázku',\n        removeMedia: 'Odstrániť obrázok',\n        original: 'Originál',\n      },\n      video: {\n        video: 'Video',\n        videoLink: 'Odkaz videa',\n        insert: 'Vložiť video',\n        url: 'URL videa?',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion alebo Youku)',\n      },\n      link: {\n        link: 'Odkaz',\n        insert: 'Vytvoriť odkaz',\n        unlink: 'Zrušiť odkaz',\n        edit: 'Upraviť',\n        textToDisplay: 'Zobrazovaný text',\n        url: 'Na akú URL adresu má tento odkaz viesť?',\n        openInNewWindow: 'Otvoriť v novom okne',\n        useProtocol: 'Použiť predvolený protokol',\n      },\n      table: {\n        table: 'Tabuľka',\n        addRowAbove: 'Pridať riadok nad',\n        addRowBelow: 'Pridať riadok pod',\n        addColLeft: 'Pridať stĺpec vľavo',\n        addColRight: 'Pridať stĺpec vpravo',\n        delRow: 'Odstrániť riadok',\n        delCol: 'Odstrániť stĺpec',\n        delTable: 'Odstrániť tabuľku',\n      },\n      hr: {\n        insert: 'Vložit vodorovnú čiaru',\n      },\n      style: {\n        style: 'Štýl',\n        p: 'Normálny',\n        blockquote: 'Citácia',\n        pre: 'Kód',\n        h1: 'Nadpis 1',\n        h2: 'Nadpis 2',\n        h3: 'Nadpis 3',\n        h4: 'Nadpis 4',\n        h5: 'Nadpis 5',\n        h6: 'Nadpis 6',\n      },\n      lists: {\n        unordered: 'Odrážkový zoznam',\n        ordered: 'Číselný zoznam',\n      },\n      options: {\n        help: 'Pomoc',\n        fullscreen: 'Celá obrazovka',\n        codeview: 'HTML kód',\n      },\n      paragraph: {\n        paragraph: 'Odsek',\n        outdent: 'Zväčšiť odsadenie',\n        indent: 'Zmenšiť odsadenie',\n        left: 'Zarovnať doľava',\n        center: 'Zarovnať na stred',\n        right: 'Zarovnať doprava',\n        justify: 'Zarovnať obojstranne',\n      },\n      color: {\n        recent: 'Aktuálna farba',\n        more: 'Dalšie farby',\n        background: 'Farba pozadia',\n        foreground: 'Farba písma',\n        transparent: 'Priehľadnosť',\n        setTransparent: 'Nastaviť priehľadnosť',\n        reset: 'Obnoviť',\n        resetToDefault: 'Obnoviť prednastavené',\n      },\n      shortcut: {\n        shortcuts: 'Klávesové skratky',\n        close: 'Zavrieť',\n        textFormatting: 'Formátovanie textu',\n        action: 'Akcia',\n        paragraphFormatting: 'Formátovanie odseku',\n        documentStyle: 'Štýl dokumentu',\n        extraKeys: 'Ďalšie kombinácie',\n      },\n      help: {\n        'insertParagraph': 'Vložiť odsek',\n        'undo': 'Vrátiť posledný krok',\n        'redo': 'Obnoviť posledný krok',\n        'tab': 'Odsadiť',\n        'untab': 'Zmenšiť odsadenie',\n        'bold': 'Tučné',\n        'italic': 'Kurzívu',\n        'underline': 'Podčiarknutie',\n        'strikethrough': 'Preškrknutý text',\n        'removeFormat': 'Odstrániť formátovanie',\n        'justifyLeft': 'Odsadenie zľava',\n        'justifyCenter': 'Vycentrovať',\n        'justifyRight': 'Odsadenie zprava',\n        'justifyFull': 'Zarovnať do bloku',\n        'insertUnorderedList': 'Odrážkový zoznam',\n        'insertOrderedList': 'Číselný zoznam',\n        'outdent': 'Zrušiť odsadenie aktuálneho odseku',\n        'indent': 'Odsadiť aktuálny odsek',\n        'formatPara': 'Change current block\\'s format as a paragraph(P tag)',\n        'formatH1': 'Change current block\\'s format as H1',\n        'formatH2': 'Change current block\\'s format as H2',\n        'formatH3': 'Change current block\\'s format as H3',\n        'formatH4': 'Change current block\\'s format as H4',\n        'formatH5': 'Change current block\\'s format as H5',\n        'formatH6': 'Change current block\\'s format as H6',\n        'insertHorizontalRule': 'Vložiť horizontálne pravidlo',\n        'linkDialog.show': 'Dialóg na zadanie odkazu',\n      },\n      history: {\n        undo: 'Krok vzad',\n        redo: 'Krok dopredu',\n      },\n      specialChar: {\n        specialChar: 'ŠPECIÁLNE ZNAKY',\n        select: 'Vybrať špeciálne znaky',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "removeMedia", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "useProtocol", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}