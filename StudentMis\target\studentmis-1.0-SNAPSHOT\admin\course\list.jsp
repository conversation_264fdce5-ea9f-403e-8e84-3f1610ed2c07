<%@ page import="com.ntvu.studentmis.pager.PagerHelper" %>
<%@ page import="com.ntvu.studentmis.entity.Course" %>
<%@ page import="com.ntvu.studentmis.util.WebTools" %>
<%@ page import="com.ntvu.studentmis.db.DBCourse" %>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <jsp:include page="../../include/header_css.jsp" flush="true"/>
    <title>课程信息列表</title>
</head>
<body class="hold-transition sidebar-mini layout-fixed">
<div class="wrapper">
    <%@include file="../../include/header_nav.jsp"%>
    <%@include file="../../include/left_menu.jsp"%>
    <%
        request.setCharacterEncoding("utf-8");
        String course_name = request.getParameter("course_name");
        PagerHelper<Course> pager = new PagerHelper(request);
        if (course_name != null && !course_name.trim().equals("")) {
            pager.getQueryParams().put("course_name", course_name);
        }

        new DBCourse().getList(pager);
    %>
    <!-- Content Wrapper. Contains page content -->
    <form id="form1" name="form1" method="post"
          action="<%= request.getContextPath() + "/admin/course/CourseServlet?action=add"%>">
        <div class="content-wrapper">
            <section class="content">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <div class="row">
                                        <div class="col-lg-2">课程名：<input type="text" name="course_name"
                                                                          class="form-control" value="<%= WebTools.parseNullorEmpty(course_name)%>"></div>
                                        <div class="col-lg-2">
                                            <input type="button" class="btn btn-block btn-primary btn-xs;form-control"
                                                    style="margin-top: 25px;" name="btnFind" value="查询">
                                        </div>
                                        <div class="col-lg-2">
                                            <input type="button" class="btn btn-block btn-danger btn-xs;form-control"
                                                    style="margin-top: 25px;" name="btnDelSel" value="删除所勾选的">
                                        </div>
                                    </div>
                                    <!-- /.card-header -->
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-sm-12">
                                                <table id="example1"
                                                       class="table table-bordered table-striped dataTable dtr-inline"
                                                       aria-describedby="example1_info">
                                                    <thead>
                                                    <tr>
                                                        <th class="sorting sorting_asc" tabindex="0"
                                                            aria-controls="example1" rowspan="1" colspan="1"
                                                            aria-sort="ascending"
                                                            aria-label="Rendering engine: activate to sort column descending">
                                                            <input type="checkbox" class="form-check" title="全选" name="checkAll">
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="Platform(s): activate to sort column ascending">
                                                            序号
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="Course Code: activate to sort column ascending">
                                                            课程编码
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="Browser: activate to sort column ascending">
                                                            科目
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="Course Type: activate to sort column ascending">
                                                            课程性质
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="Platform(s): activate to sort column ascending">
                                                            学分
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="CSS grade: activate to sort column ascending">
                                                            理论学时
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="CSS grade: activate to sort column ascending">
                                                            实践学时
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="CSS grade: activate to sort column ascending">
                                                            总学时
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="CSS grade: activate to sort column ascending">
                                                            任课教师
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="CSS grade: activate to sort column ascending">
                                                            开课时间
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="CSS grade: activate to sort column ascending">
                                                            操作
                                                        </th>
                                                    </tr>
                                                    </thead>
                                                    <tbody>
                                                    <%
                                                        int index = 0;
                                                        for (Course course : pager.getData()) {
                                                            index++;
                                                    %>
                                                    <tr class="<%= index % 2 == 1 ? "odd" : "even"%>">
                                                        <td><input type="checkbox" class="form-check" name="checkItem" value="<%= course.getCourse_id()%>"></td>
                                                        <td><%= index%></td>
                                                        <td class="dtr-control sorting_1" tabindex="0">
                                                            <span class="course-code">COURSE_<%= course.getCourse_id()%></span>
                                                        </td>
                                                        <td class="dtr-control sorting_1"
                                                            tabindex="0"><%= course.getCourse_name()%>
                                                        </td>
                                                        <td>
                                                            <span class="course-type" data-course-id="<%= course.getCourse_id()%>">必修</span>
                                                        </td>
                                                        <td><%= course.getCourse_credit()%>
                                                        </td>
                                                        <td class="theory-hours">
                                                            <%= Math.round(course.getCourse_hours() * 0.7)%>
                                                        </td>
                                                        <td class="practice-hours">
                                                            <%= Math.round(course.getCourse_hours() * 0.3)%>
                                                        </td>
                                                        <td><%= course.getCourse_hours()%>
                                                        </td>
                                                        <td><%= course.getCourse_teacher()%>
                                                        </td>
                                                        <td><%= course.getCoursedate()%></td>
                                                        <td style="width: 200px">
                                                            <div class="row">
                                                                <div class="col-6">
                                                                    <button type="button"
                                                                            class="btn btn-block btn-primary btn-xs"
                                                                            style="width: 80px" onclick="window.location.href = '<%= contextPath +"/admin/course/edit.jsp?id=" + course.getCourse_id()%>';">编辑
                                                                    </button>
                                                                </div>
                                                                <div class="col-6">
                                                                    <button type="button"
                                                                            class="btn btn-block btn-danger btn-xs"
                                                                            style="width: 80px" onclick="if(confirm('当前操作不可恢复，确认删除吗？')){
                                                                            window.location.href='<%= contextPath +"/admin/course/CourseServlet?action=delete&id=" + course.getCourse_id()%>';}">删除
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    <%
                                                        }
                                                    %>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-5">
                                                <div class="dataTables_info" id="example1_info" role="status"
                                                     aria-live="polite">每页显示10条记录
                                                </div>
                                            </div>
                                            <div class="col-sm-12 col-md-7">
                                                <%@ include file="../../include/pager_footer.jsp"%>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- /.card-body -->
                            </div>
                            <!-- /.card -->
                        </div>
                        <!-- /.col -->
                    </div>
                    <!-- /.row -->
                </div>
                <!-- /.container-fluid -->
            </section>
        </div>
    </form>
</div>
<%@include file="../../include/foot_js.jsp"%>
<style>
    /* 课程管理页面样式优化 */
    .course-code {
        font-family: 'Courier New', monospace;
        font-weight: bold;
        color: #007bff;
        font-size: 0.9em;
    }

    .course-type {
        display: inline-block;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.8em;
        font-weight: bold;
        text-align: center;
        min-width: 50px;
    }

    .course-type[data-type="REQUIRED"] {
        background-color: #dc3545;
        color: white;
    }

    .course-type[data-type="ELECTIVE"] {
        background-color: #28a745;
        color: white;
    }

    .course-type[data-type="PUBLIC_REQUIRED"] {
        background-color: #ffc107;
        color: #212529;
    }

    .course-type[data-type="PUBLIC_ELECTIVE"] {
        background-color: #17a2b8;
        color: white;
    }

    .theory-hours, .practice-hours {
        font-weight: bold;
        color: #495057;
    }

    .table th {
        font-size: 0.9em;
        white-space: nowrap;
    }

    .table td {
        font-size: 0.85em;
        vertical-align: middle;
    }

    /* 响应式表格优化 */
    @media (max-width: 1200px) {
        .table-responsive {
            font-size: 0.8em;
        }
    }
</style>
<script>
    $(function (){
        //绑定勾选框按钮事件
        $('input[name=checkAll]').bind('change',function (){
            console.log('checkAll');
            let checked=$(this).prop('checked');
            //更改表格中所有chkItem
            $('input[name=checkItem]').each(function (){
                console.log('checkItem');
                $(this).prop('checked',checked);
            });
        });
        //绑定删除所有按钮事件
        $('input[name=btnDelSel]').bind('click',function (){
            let ids='';
            $('input[name=checkItem]').each(function (){
                if( $(this).prop('checked')===true)
                {
                    ids+=$(this).val()+',';
                }
            });
            if(ids.length>0)
            {
                if(confirm('当前操作不可恢复,确认要删除吗?'))
                {
                    console.log(ids);
                    window.location.href='<%=contextPath+"/admin/course/CourseServlet?action=deleteSelected&ids="%>'+ids;
                }
            }else {
                alert('请选择待删除项');
            }

        });
        $('input[name=btnFind]').bind('click',function (){
            $('#form1').attr('action','<%= request.getContextPath() + "/admin/course/list.jsp"%>');
            $(`#form1`).submit();
        });

        // 初始化课程性质显示
        initializeCourseTypes();
    });

    /**
     * 初始化课程性质显示
     */
    function initializeCourseTypes() {
        $('.course-type').each(function() {
            const courseId = $(this).data('course-id');
            const savedType = localStorage.getItem('courseType_' + courseId) || 'REQUIRED';
            const typeText = getCourseTypeText(savedType);
            $(this).text(typeText).attr('data-type', savedType);
        });
    }

    /**
     * 获取课程性质文本
     */
    function getCourseTypeText(type) {
        const typeMap = {
            'REQUIRED': '必修',
            'ELECTIVE': '选修',
            'PUBLIC_REQUIRED': '公共必修',
            'PUBLIC_ELECTIVE': '公共选修'
        };
        return typeMap[type] || '必修';
    });
    /**
     * 跳转到指定的页
     * @param toPageIndex
     */
        //本页地址
    let pageListUrl = '/admin/course/list.jsp';
    function doPager(toPageIndex)
    {
        $('#form1').attr('action','<%= request.getContextPath() %>' + pageListUrl + '?pageIndex=' + toPageIndex);
        $('#form1').submit();
    }
</script>

</body>
</html>
