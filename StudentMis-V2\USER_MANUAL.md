# StudentMIS V2 用户使用手册

## 目录
1. [系统介绍](#系统介绍)
2. [快速开始](#快速开始)
3. [用户登录](#用户登录)
4. [主要功能](#主要功能)
5. [常见问题](#常见问题)
6. [技术支持](#技术支持)

## 系统介绍

StudentMIS V2 是一个现代化的学生成绩管理系统，专为江苏海洋大学设计，为海洋特色高等教育机构提供全面的学生信息管理解决方案。

### 系统特色
- 🎯 **智能化**: AI驱动的成绩预测和个性化推荐
- 🔒 **安全性**: 企业级安全认证和权限控制
- 📊 **数据化**: 丰富的统计分析和可视化图表
- 🚀 **现代化**: 响应式设计，支持多设备访问
- 🎨 **易用性**: 直观的用户界面和操作流程

### 支持的用户角色
- **超级管理员**: 系统全局管理
- **系统管理员**: 用户和权限管理
- **教务管理员**: 课程和成绩管理
- **教师**: 成绩录入和学生管理
- **学生**: 查看个人信息和成绩

## 快速开始

### 系统要求
- **浏览器**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- **分辨率**: 1366x768 或更高
- **网络**: 稳定的互联网连接

### 访问地址
- **系统首页**: http://localhost:3000
- **API文档**: http://localhost:8080/doc.html

## 用户登录

### 登录步骤
1. 打开浏览器，访问系统首页
2. 在登录页面输入用户名和密码
3. 点击"登录"按钮
4. 系统验证成功后自动跳转到主页

### 默认账号
| 角色 | 用户名 | 密码 | 说明 |
|------|--------|------|------|
| 超级管理员 | admin | 123456 | 系统管理员账号 |
| 学生 | 2024001001 | 123456 | 测试学生账号 |
| 教师 | T001 | 123456 | 测试教师账号 |

### 登录注意事项
- 密码连续输错5次将被锁定30分钟
- 首次登录建议修改默认密码
- 支持"记住我"功能，可保持7天登录状态

## 主要功能

### 1. 仪表盘

#### 功能概述
仪表盘是系统的首页，提供系统概览和快捷操作入口。

#### 主要内容
- **个人信息卡片**: 显示当前用户基本信息
- **快捷功能**: 常用功能的快速访问入口
- **数据统计**: 关键指标的可视化展示
- **最新通知**: 系统公告和重要消息
- **待办事项**: 需要处理的任务提醒

#### 使用技巧
- 点击统计卡片可查看详细数据
- 使用快捷功能可快速访问常用页面
- 关注通知栏获取最新系统消息

### 2. 学生管理

#### 功能概述
学生管理模块提供完整的学生信息管理功能，支持学生档案的全生命周期管理。

#### 主要功能

##### 2.1 学生列表
- **查看学生**: 分页显示所有学生信息
- **搜索筛选**: 支持按姓名、学号、专业等条件搜索
- **批量操作**: 支持批量导入、导出、删除等操作
- **状态管理**: 查看和修改学生学籍状态

**操作步骤**:
1. 点击左侧菜单"学生管理" → "学生列表"
2. 使用搜索框输入关键词进行搜索
3. 点击"高级搜索"进行多条件筛选
4. 选择学生记录，点击操作按钮进行相应操作

##### 2.2 学生详情
- **基本信息**: 姓名、学号、性别、出生日期等
- **联系信息**: 手机号、邮箱、地址等
- **学籍信息**: 入学日期、专业、班级、学籍状态等
- **家庭信息**: 家庭成员、联系方式等

**操作步骤**:
1. 在学生列表中点击学生姓名或"查看详情"
2. 在详情页面查看完整的学生信息
3. 点击"编辑"按钮可修改学生信息
4. 点击"学籍变更"可处理转专业、休学等事务

##### 2.3 新增学生
**操作步骤**:
1. 在学生列表页面点击"新增学生"按钮
2. 填写学生基本信息（必填项用*标注）
3. 填写联系信息和家庭信息
4. 点击"保存"完成学生信息录入

**注意事项**:
- 学号必须唯一，系统会自动验证
- 身份证号格式必须正确
- 手机号和邮箱格式需要符合规范

### 3. 成绩管理

#### 功能概述
成绩管理模块提供完整的成绩录入、审核、查询和统计功能。

#### 主要功能

##### 3.1 成绩录入
**操作步骤**:
1. 点击"成绩管理" → "成绩录入"
2. 选择课程和学期
3. 选择成绩类型（平时成绩、期中成绩、期末成绩）
4. 录入学生成绩
5. 点击"保存"提交成绩

**录入规则**:
- 成绩范围：0-100分
- 支持批量录入和单个录入
- 系统自动计算总成绩和绩点
- 录入后需要审核才能生效

##### 3.2 成绩审核
**操作步骤**:
1. 点击"成绩管理" → "成绩审核"
2. 查看待审核的成绩记录
3. 点击"审核"按钮
4. 选择"通过"或"拒绝"
5. 填写审核意见（拒绝时必填）
6. 点击"确认"完成审核

##### 3.3 成绩查询
**查询方式**:
- **按学生查询**: 输入学号或姓名查询
- **按课程查询**: 选择课程查看所有学生成绩
- **按班级查询**: 选择班级查看班级成绩
- **按时间查询**: 选择学期或时间范围

**导出功能**:
- 支持Excel格式导出
- 可选择导出字段
- 支持批量导出多个班级

##### 3.4 成绩统计
**统计维度**:
- **班级统计**: 平均分、及格率、优秀率等
- **课程统计**: 各班级成绩对比
- **学生统计**: 个人成绩趋势分析
- **专业统计**: 专业整体成绩分析

### 4. 数据分析

#### 功能概述
数据分析模块利用AI技术提供智能化的数据分析和预测功能。

#### 主要功能

##### 4.1 成绩预测
**功能说明**:
- 基于历史成绩数据预测学生未来成绩
- 支持期中、期末、总成绩预测
- 提供预测准确度和置信区间

**使用方法**:
1. 点击"数据分析" → "成绩预测"
2. 选择学生和课程
3. 选择预测类型
4. 点击"开始预测"
5. 查看预测结果和分析报告

##### 4.2 学习行为分析
**分析维度**:
- 登录频次和学习时长
- 作业完成率和出勤率
- 课程参与度和互动情况
- 学习模式和风险评估

**报告内容**:
- 学习行为评分
- 风险等级评估
- 改进建议
- 对比分析

##### 4.3 个性化推荐
**推荐类型**:
- **课程推荐**: 基于学习能力和兴趣推荐适合的课程
- **学习方法**: 根据学习模式推荐有效的学习方法
- **学习资源**: 推荐相关的学习材料和资源
- **活动推荐**: 推荐适合的课外活动

### 5. 系统管理

#### 功能概述
系统管理模块提供用户管理、权限配置、系统设置等管理功能。

#### 主要功能

##### 5.1 用户管理
- **用户列表**: 查看所有系统用户
- **新增用户**: 创建新的系统用户
- **编辑用户**: 修改用户信息和权限
- **禁用用户**: 禁用或启用用户账号

##### 5.2 角色管理
- **角色列表**: 查看系统角色
- **权限配置**: 为角色分配功能权限
- **数据权限**: 配置数据访问范围

##### 5.3 系统设置
- **基础配置**: 系统名称、Logo等
- **安全设置**: 密码策略、登录限制等
- **通知设置**: 邮件、短信通知配置

### 6. 个人中心

#### 功能概述
个人中心提供个人信息管理和系统偏好设置。

#### 主要功能
- **个人信息**: 查看和修改个人基本信息
- **密码修改**: 修改登录密码
- **头像上传**: 上传和更换个人头像
- **系统设置**: 主题、语言等偏好设置

## 常见问题

### Q1: 忘记密码怎么办？
**A**: 请联系系统管理员重置密码，或使用"忘记密码"功能通过邮箱重置。

### Q2: 为什么无法录入成绩？
**A**: 请检查以下几点：
- 是否有成绩录入权限
- 课程是否已开课
- 学生是否已选课
- 成绩格式是否正确

### Q3: 成绩录入后为什么学生看不到？
**A**: 成绩录入后需要经过审核才能发布给学生查看。

### Q4: 如何批量导入学生信息？
**A**: 
1. 下载学生信息模板
2. 按模板格式填写学生信息
3. 在学生管理页面选择"批量导入"
4. 上传填写好的Excel文件

### Q5: 系统运行缓慢怎么办？
**A**: 
- 检查网络连接是否稳定
- 清除浏览器缓存
- 关闭不必要的浏览器标签页
- 联系技术支持检查服务器状态

### Q6: 如何查看操作日志？
**A**: 系统管理员可以在"系统管理" → "操作日志"中查看所有用户的操作记录。

### Q7: 数据如何备份？
**A**: 系统支持自动备份，管理员也可以在"系统管理" → "数据备份"中手动备份数据。

## 技术支持

### 联系方式
- **技术支持邮箱**: <EMAIL>
- **用户交流群**: StudentMIS用户群
- **在线文档**: http://docs.studentmis.edu.cn

### 支持时间
- **工作日**: 9:00-18:00
- **紧急问题**: 24小时响应

### 常用链接
- **系统首页**: http://localhost:3000
- **API文档**: http://localhost:8080/doc.html
- **技术文档**: 查看项目README.md
- **部署指南**: 查看DEPLOYMENT_GUIDE.md

### 版本信息
- **当前版本**: StudentMIS V2.0.0
- **发布日期**: 2024年12月19日
- **更新日志**: 查看CHANGELOG.md

---

**注意**: 本手册基于StudentMIS V2.0.0版本编写，如有功能更新，请以最新版本为准。
