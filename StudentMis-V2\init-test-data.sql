-- 清华大学学生成绩管理系统 V2.0 测试数据初始化脚本
-- 计算机科学与技术专业（专转本）2024级 计算机4241班

USE studentmis_v2;

-- 创建学生基本信息表
CREATE TABLE IF NOT EXISTS stu_basic_info (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    student_id VARCHAR(20) UNIQUE NOT NULL COMMENT '学号',
    name VARCHAR(50) NOT NULL COMMENT '姓名',
    name_en VARCHAR(100) COMMENT '英文姓名',
    gender ENUM('MALE', 'FEMALE', 'OTHER') NOT NULL COMMENT '性别',
    birth_date DATE COMMENT '出生日期',
    phone VARCHAR(20) COMMENT '手机号',
    email VARCHAR(100) COMMENT '邮箱',
    admission_date DATE COMMENT '入学日期',
    status ENUM('ACTIVE', 'SUSPENDED', 'GRADUATED', 'DROPPED', 'TRANSFERRED') DEFAULT 'ACTIVE' COMMENT '学籍状态',
    major_id BIGINT COMMENT '专业ID',
    class_id BIGINT COMMENT '班级ID',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='学生基本信息表';

-- 插入真实学生测试数据
INSERT IGNORE INTO stu_basic_info (student_id, name, name_en, gender, birth_date, phone, email, admission_date, status, major_id, class_id) VALUES
-- 徐浩翔（您本人）
('2024140520', '徐浩翔', 'Xu Haoxiang', 'MALE', '2002-05-20', '18626425051', '<EMAIL>', '2024-09-01', 'ACTIVE', 1, 4241),

-- 计算机4241班其他同学
('2024140518', '王梓齐', 'Wang Ziqi', 'MALE', '2002-05-18', '13800140518', '<EMAIL>', '2024-09-01', 'ACTIVE', 1, 4241),
('2024140525', '张庆祥', 'Zhang Qingxiang', 'MALE', '2002-05-25', '13800140525', '<EMAIL>', '2024-09-01', 'ACTIVE', 1, 4241),
('2024140485', '蔡家璇', 'Cai Jiaxuan', 'FEMALE', '2002-04-25', '13800140485', '<EMAIL>', '2024-09-01', 'ACTIVE', 1, 4241),
('2024140499', '刘川东', 'Liu Chuandong', 'MALE', '2002-04-29', '13800140499', '<EMAIL>', '2024-09-01', 'ACTIVE', 1, 4241),
('2024140508', '沈磊', 'Shen Lei', 'MALE', '2002-05-08', '13800140508', '<EMAIL>', '2024-09-01', 'ACTIVE', 1, 4241),
('2024140486', '程学峰', 'Cheng Xuefeng', 'MALE', '2002-04-26', '13800140486', '<EMAIL>', '2024-09-01', 'ACTIVE', 1, 4241);

-- 验证数据插入
SELECT 
    student_id AS '学号',
    name AS '姓名',
    name_en AS '英文姓名',
    gender AS '性别',
    phone AS '手机号',
    email AS '邮箱',
    admission_date AS '入学日期',
    status AS '学籍状态'
FROM stu_basic_info 
WHERE class_id = 4241 
ORDER BY student_id;

-- 显示统计信息
SELECT 
    COUNT(*) AS '总学生数',
    COUNT(CASE WHEN gender = 'MALE' THEN 1 END) AS '男生数',
    COUNT(CASE WHEN gender = 'FEMALE' THEN 1 END) AS '女生数',
    '计算机科学与技术专业（专转本）' AS '专业',
    '计算机4241班' AS '班级'
FROM stu_basic_info 
WHERE class_id = 4241;
