<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <jsp:include page="../../include/header_css.jsp" flush="true"/>
    <title>添加学生成绩</title>
</head>
<body class="hold-transition sidebar-mini layout-fixed">
<div class="wrapper">
    <%@include file="../../teacher/header_nav.jsp"%>
    <%@include file="../../teacher/left_menu.jsp"%>
    <div class="content-wrapper" style="min-height: 1345.6px;">
        <!-- Content Header (Page header) -->
        <section class="content-header">
            <div class="container-fluid">
                <div class="row mb-2">
                    <div class="col-sm-6">
                        <h1>添加学生成绩</h1>
                    </div>
                </div>
            </div><!-- /.container-fluid -->
        </section>

        <!-- Main content -->
        <section class="content">
            <div class="container-fluid">
                <div class="row">
                    <!-- left column -->
                    <div class="col-md-12">
                        <!-- jquery validation -->
                        <div class="card card-primary">
                            <div class="card-header">
                                <h3 class="card-title">请添加<small>学生成绩</small></h3>
                            </div>
                            <!-- /.card-header -->
                            <!-- form start -->
                            <form name="form1" id="form1" method="post" action="<%= request.getContextPath() + "/teacher/Score/TeacherScoreServlet?action=add"%>" onsubmit="return verify()">
                                <div class="card-body">
                                    <div class="form-group">
                                        <label >学号</label>
                                        <input type="text" name="txtNum" class="form-control" placeholder="请输入学号">
                                    </div>
                                    <div class="form-group">
                                        <label >姓名</label>
                                        <input type="text" name="txtName" class="form-control"  placeholder="请输入姓名">
                                    </div>
                                    <div class="form-group">
                                        <label>班级</label>
                                        <input type="text" name="txtClass" class="form-control"  placeholder="请输入班级">
                                    </div>
                                    <div class="form-group">
                                        <label>科目</label>
                                        <input type="text" name="txtCourse" class="form-control"  placeholder="请输入科目">
                                    </div>
                                    <div class="form-group">
                                        <label>成绩</label>
                                        <input type="text" name="txtScore" class="form-control"  placeholder="请输入成绩">
                                    </div>
                                    <div class="form-group">
                                        <label >专业</label>
                                        <input type="text" name="txtMajor" class="form-control" placeholder="请输入专业">
                                    </div>

                                </div>
                                <!-- /.card-body -->
                                <div class="card-footer">
                                    <input type="submit" class="btn btn-primary" name="btnSubmit" value="提交">
                                </div>
                            </form>
                        </div>
                        <!-- /.card -->
                    </div>
                    <!--/.col (left) -->
                    <!-- right column -->
                    <div class="col-md-6">

                    </div>
                    <!--/.col (right) -->
                </div>
                <!-- /.row -->
            </div><!-- /.container-fluid -->
        </section>
        <!-- /.content -->
    </div>
</div>
<%@include file="../../include/foot_js.jsp"%>
<script>
    function verify()
    {
        console.log(`click`);
        //对数据进行检验
        let txtNum=$(`input[name=txtNum]`).val();
        if(txtNum==='')
        {
            alert(`学号不能为空`);
            $(`input[name=txtNum]`).focus();//光标选中
            return false;
        }
        let txtName=$(`input[name=txtName]`).val();
        if(txtName==='')
        {
            alert(`姓名不能为空`);
            $(`input[name=txtName]`).focus();//光标选中
            return false;
        }
        let txtClass=$(`input[name=txtClass]`).val();
        if(txtClass==='')
        {
            alert(`班级不能为空`);
            $(`input[name=txtClass]`).focus();//光标选中
            return false;
        }
        let txtCourse=$(`input[name=txtCourse]`).val();
        if(txtCourse==='')
        {
            alert(`科目不能为空`);
            $(`input[name=txtCourse]`).focus();//光标选中
            return false;
        }
        let txtScore=$(`input[name=txtScore]`).val();
        if(txtScore==='')
        {
            alert(`成绩不能为空`);
            $(`input[name=txtScore]`).focus();//光标选中
            return false;
        }

        let txtMajor=$(`input[name=txtMajor]`).val();
        if(txtMajor==='')
        {
            alert(`专业不能为空`);
            $(`input[name=txtMajor]`).focus();//光标选中
            return false;
        }
    }
</script>

</body>
</html>
