#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
江苏海洋大学5000名学生数据生成脚本
按照真实学院规模和专业分布生成
"""

import random
import datetime

# 江苏海洋大学学院配置
colleges = {
    '01': {'name': '海洋生命与水产学院', 'students': 300, 'majors': ['海洋科学', '水产养殖学', '海洋渔业科学与技术', '生物技术']},
    '02': {'name': '机械与海洋工程学院', 'students': 450, 'majors': ['机械设计制造及其自动化', '船舶与海洋工程', '海洋工程与技术', '机械电子工程']},
    '03': {'name': '土木与港海工程学院', 'students': 350, 'majors': ['土木工程', '港口航道与海岸工程', '工程管理', '建筑环境与能源应用工程']},
    '04': {'name': '测绘与海洋信息学院', 'students': 200, 'majors': ['测绘工程', '地理信息科学', '海洋技术', '遥感科学与技术']},
    '05': {'name': '海洋资源与环境学院', 'students': 250, 'majors': ['环境工程', '海洋资源与环境', '环境科学', '给排水科学与工程']},
    '06': {'name': '电子工程学院', 'students': 400, 'majors': ['电子信息工程', '通信工程', '自动化', '电气工程及其自动化']},
    '07': {'name': '化学工程学院', 'students': 300, 'majors': ['化学工程与工艺', '应用化学', '材料科学与工程', '安全工程']},
    '08': {'name': '计算机工程学院', 'students': 500, 'majors': ['计算机科学与技术', '软件工程', '数据科学与大数据技术', '网络工程']},
    '09': {'name': '药学院', 'students': 180, 'majors': ['药学', '中药学', '药物制剂', '生物制药']},
    '10': {'name': '理学院', 'students': 280, 'majors': ['数学与应用数学', '应用物理学', '统计学', '信息与计算科学']},
    '11': {'name': '商学院', 'students': 450, 'majors': ['国际经济与贸易', '会计学', '市场营销', '财务管理']},
    '12': {'name': '马克思主义学院', 'students': 80, 'majors': ['思想政治教育']},
    '13': {'name': '文学院', 'students': 250, 'majors': ['汉语言文学', '秘书学', '广播电视学']},
    '14': {'name': '外国语学院', 'students': 300, 'majors': ['英语', '日语', '朝鲜语']},
    '15': {'name': '法律与公共管理学院', 'students': 200, 'majors': ['法学', '公共事业管理', '行政管理']},
    '16': {'name': '艺术学院', 'students': 180, 'majors': ['音乐学', '美术学', '视觉传达设计', '环境设计']},
    '17': {'name': '体育学院', 'students': 120, 'majors': ['体育教育', '社会体育指导与管理']},
    '18': {'name': '应用技术学院', 'students': 100, 'majors': ['应用技术综合', '职业技术教育']},
    '19': {'name': '继续教育学院', 'students': 80, 'majors': ['成人教育', '网络教育']}
}

# 常见中文姓氏
surnames = ['王', '李', '张', '刘', '陈', '杨', '赵', '黄', '周', '吴', '徐', '孙', '胡', '朱', '高', '林', '何', '郭', '马', '罗', 
           '梁', '宋', '郑', '谢', '韩', '唐', '冯', '于', '董', '萧', '程', '曹', '袁', '邓', '许', '傅', '沈', '曾', '彭', '吕',
           '苏', '卢', '蒋', '蔡', '贾', '丁', '魏', '薛', '叶', '阎', '余', '潘', '杜', '戴', '夏', '钟', '汪', '田', '任', '姜']

# 常见中文名字
male_names = ['伟', '强', '磊', '军', '勇', '涛', '明', '超', '亮', '华', '建', '峰', '杰', '波', '辉', '成', '龙', '文', '斌', '刚',
             '宇', '鹏', '飞', '宏', '志', '东', '海', '浩', '凯', '博', '威', '鸿', '俊', '豪', '阳', '天', '翔', '宁', '安', '康']

female_names = ['芳', '娜', '敏', '静', '丽', '强', '洁', '美', '娟', '英', '华', '慧', '巧', '雅', '玲', '红', '艳', '梅', '琳', '霞',
               '燕', '萍', '莉', '兰', '凤', '菊', '花', '桂', '月', '怡', '悦', '晶', '妍', '蕾', '薇', '雯', '琪', '珊', '婷', '欣']

def generate_name(gender):
    """生成中文姓名"""
    surname = random.choice(surnames)
    if gender == 'MALE':
        name = surname + random.choice(male_names)
        if random.random() < 0.3:  # 30%概率生成两字名
            name += random.choice(male_names)
    else:
        name = surname + random.choice(female_names)
        if random.random() < 0.3:
            name += random.choice(female_names)
    return name

def generate_english_name(chinese_name):
    """生成英文名"""
    # 简单的拼音转换（实际应用中可以使用更完善的拼音库）
    pinyin_map = {
        '王': 'Wang', '李': 'Li', '张': 'Zhang', '刘': 'Liu', '陈': 'Chen', '杨': 'Yang', '赵': 'Zhao', '黄': 'Huang',
        '周': 'Zhou', '吴': 'Wu', '徐': 'Xu', '孙': 'Sun', '胡': 'Hu', '朱': 'Zhu', '高': 'Gao', '林': 'Lin',
        '何': 'He', '郭': 'Guo', '马': 'Ma', '罗': 'Luo', '梁': 'Liang', '宋': 'Song', '郑': 'Zheng', '谢': 'Xie',
        '韩': 'Han', '唐': 'Tang', '冯': 'Feng', '于': 'Yu', '董': 'Dong', '萧': 'Xiao', '程': 'Cheng', '曹': 'Cao',
        '袁': 'Yuan', '邓': 'Deng', '许': 'Xu', '傅': 'Fu', '沈': 'Shen', '曾': 'Zeng', '彭': 'Peng', '吕': 'Lv',
        '苏': 'Su', '卢': 'Lu', '蒋': 'Jiang', '蔡': 'Cai', '贾': 'Jia', '丁': 'Ding', '魏': 'Wei', '薛': 'Xue',
        '叶': 'Ye', '阎': 'Yan', '余': 'Yu', '潘': 'Pan', '杜': 'Du', '戴': 'Dai', '夏': 'Xia', '钟': 'Zhong',
        '汪': 'Wang', '田': 'Tian', '任': 'Ren', '姜': 'Jiang'
    }
    
    surname = chinese_name[0]
    given_name = chinese_name[1:]
    
    surname_en = pinyin_map.get(surname, surname)
    # 简化处理，实际应用中需要更完善的拼音转换
    given_name_en = given_name.title()
    
    return f"{surname_en} {given_name_en}"

def generate_birth_date():
    """生成出生日期"""
    # 大学生年龄一般在18-22岁之间
    year = random.randint(2001, 2005)
    month = random.randint(1, 12)
    day = random.randint(1, 28)  # 简化处理，避免月份天数问题
    return f"{year:04d}-{month:02d}-{day:02d}"

def generate_phone():
    """生成手机号"""
    prefixes = ['138', '139', '150', '151', '152', '158', '159', '182', '183', '184', '187', '188']
    prefix = random.choice(prefixes)
    suffix = ''.join([str(random.randint(0, 9)) for _ in range(8)])
    return prefix + suffix

def generate_students_sql():
    """生成5000名学生的SQL插入语句"""
    sql_lines = []
    sql_lines.append("-- 江苏海洋大学5000名学生数据")
    sql_lines.append("-- 自动生成脚本")
    sql_lines.append("")
    sql_lines.append("USE studentmis_v2;")
    sql_lines.append("")
    sql_lines.append("-- 清空现有数据")
    sql_lines.append("DELETE FROM stu_basic_info;")
    sql_lines.append("")
    sql_lines.append("-- 插入学生数据")
    sql_lines.append("INSERT INTO stu_basic_info (student_id, name, name_en, gender, birth_date, phone, email, admission_date, status, major_id, class_id) VALUES")
    
    student_count = 0
    all_students = []
    
    for college_code, college_info in colleges.items():
        college_students = college_info['students']
        majors = college_info['majors']
        
        # 按专业分配学生
        students_per_major = college_students // len(majors)
        
        for major_idx, major in enumerate(majors):
            major_code = f"{major_idx + 1:02d}"
            
            # 每个专业的学生数
            if major_idx == len(majors) - 1:  # 最后一个专业分配剩余学生
                current_major_students = college_students - (students_per_major * major_idx)
            else:
                current_major_students = students_per_major
            
            # 按班级分配（每班约50人）
            classes_needed = (current_major_students + 49) // 50
            
            for class_idx in range(classes_needed):
                class_code = f"24{college_code}{major_code}{class_idx + 1:02d}"
                
                # 计算这个班的学生数
                if class_idx == classes_needed - 1:  # 最后一个班
                    students_in_class = current_major_students - (50 * class_idx)
                else:
                    students_in_class = min(50, current_major_students - (50 * class_idx))
                
                # 生成班级学生
                for student_idx in range(students_in_class):
                    student_count += 1
                    
                    # 生成学号
                    student_id = f"2024{college_code}{major_code}{student_idx + 1:02d}"
                    
                    # 生成性别（大致1:1比例）
                    gender = 'MALE' if random.random() < 0.5 else 'FEMALE'
                    
                    # 生成姓名
                    name = generate_name(gender)
                    name_en = generate_english_name(name)
                    
                    # 生成其他信息
                    birth_date = generate_birth_date()
                    phone = generate_phone()
                    email = f"{name.lower().replace(' ', '')}@jou.edu.cn"
                    
                    # 构造SQL
                    student_sql = f"('{student_id}', '{name}', '{name_en}', '{gender}', '{birth_date}', '{phone}', '{email}', '2024-09-01', 'ACTIVE', {major_idx + 1}, {int(class_code)})"
                    
                    all_students.append(student_sql)
                    
                    if student_count >= 5000:
                        break
                
                if student_count >= 5000:
                    break
            
            if student_count >= 5000:
                break
        
        if student_count >= 5000:
            break
    
    # 添加SQL语句
    for i, student in enumerate(all_students):
        if i == len(all_students) - 1:
            sql_lines.append(student + ";")
        else:
            sql_lines.append(student + ",")
    
    sql_lines.append("")
    sql_lines.append("-- 统计信息")
    sql_lines.append("SELECT COUNT(*) AS '总学生数' FROM stu_basic_info;")
    sql_lines.append("SELECT gender, COUNT(*) AS '人数' FROM stu_basic_info GROUP BY gender;")
    
    return "\n".join(sql_lines)

if __name__ == "__main__":
    sql_content = generate_students_sql()
    
    # 写入文件
    with open("jou_5000_students_complete.sql", "w", encoding="utf-8") as f:
        f.write(sql_content)
    
    print("已生成5000名学生数据文件：jou_5000_students_complete.sql")
    print("文件包含完整的SQL插入语句，可直接在MySQL中执行")
