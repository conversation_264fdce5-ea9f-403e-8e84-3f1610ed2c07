{"version": 3, "sources": ["../../src/css/bs-stepper.css"], "names": [], "mappings": "AAAA;;;;EAIE;;AAEF;EACE,2BAAoB;EAApB,oBAAoB;EACpB,mBAAe;MAAf,eAAe;EACf,sBAAmB;MAAnB,mBAAmB;EACnB,qBAAuB;MAAvB,uBAAuB;EACvB,aAAa;EACb,eAAe;EACf,gBAAgB;EAChB,gBAAgB;EAChB,cAAc;EACd,kBAAkB;EAClB,qBAAqB;EACrB,mBAAmB;EACnB,sBAAsB;EACtB,yBAAiB;KAAjB,sBAAiB;MAAjB,qBAAiB;UAAjB,iBAAiB;EACjB,6BAA6B;EAC7B,YAAY;EACZ,qBAAqB;EACrB,+DAA+D;AACjE;;AAEA;EACE,eAAe;AACjB;;AAEA;;EAEE,oBAAoB;EACpB,YAAY;AACd;;AAEA;EACE,cAAc;EACd,aAAa;AACf;;AAEA;EACE,qBAAqB;EACrB,oCAAoC;AACtC;;AAEA;EACE;IACE,0BAAsB;QAAtB,sBAAsB;IACtB,aAAa;EACf;AACF;;AAEA;EACE,qBAAqB;EACrB,cAAc;AAChB;;AAEA;EACE,oBAAa;EAAb,aAAa;EACb,sBAAmB;MAAnB,mBAAmB;AACrB;;AAEA;EACE;IACE,eAAe;IACf,kBAAkB;EACpB;AACF;;AAEA;;EAEE,kBAAc;MAAd,cAAc;EACd,cAAc;EACd,eAAe;EACf,YAAY;EACZ,oCAAoC;AACtC;;AAEA;EACE;;IAEE,6BAAgB;QAAhB,gBAAgB;EAClB;AACF;;AAEA;EACE,2BAAoB;EAApB,oBAAoB;EACpB,0BAAqB;MAArB,qBAAqB;EACrB,qBAAuB;MAAvB,uBAAuB;EACvB,UAAU;EACV,WAAW;EACX,eAAe;EACf,cAAc;EACd,gBAAgB;EAChB,WAAW;EACX,yBAAyB;EACzB,kBAAkB;AACpB;;AAEA;EACE,yBAAyB;AAC3B;;AAEA;EACE,oBAAoB;AACtB;;AAEA;EACE;IACE,UAAU;EACZ;AACF;;AAEA;EACE,oBAAa;EAAb,aAAa;AACf;;AAEA;EACE,0BAAsB;MAAtB,sBAAsB;EACtB,uBAAoB;MAApB,oBAAoB;EACpB,SAAS;AACX;;AAEA;;EAEE,cAAc;AAChB;;AAEA;;EAEE,cAAc;EACd,kBAAkB;AACpB;;AAEA;;EAEE,aAAa;AACf;;AAEA;;EAEE,kBAAkB;EAClB,wBAAwB;EACxB,4BAA4B;AAC9B;;AAEA;;EAEE,mBAAmB;EACnB,UAAU;AACZ;;AAEA;;EAEE,cAAc;EACd,mBAAmB;AACrB;;AAEA;;EAEE,cAAc;AAChB;;AAEA;;EAEE,aAAa;AACf;;AAEA;;EAEE,kBAAkB;AACpB", "file": "bs-stepper.css", "sourcesContent": ["/*!\r\n * bsStepper v1.7.0 (https://github.com/<PERSON>-<PERSON>/bs-stepper)\r\n * Copyright 2018 - 2019 <PERSON>-<PERSON> <<EMAIL>>\r\n * Licensed under MIT (https://github.com/<PERSON>-<PERSON>/bs-stepper/blob/master/LICENSE)\r\n */\r\n\r\n.bs-stepper .step-trigger {\r\n  display: inline-flex;\r\n  flex-wrap: wrap;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 20px;\r\n  font-size: 1rem;\r\n  font-weight: 700;\r\n  line-height: 1.5;\r\n  color: #6c757d;\r\n  text-align: center;\r\n  text-decoration: none;\r\n  white-space: nowrap;\r\n  vertical-align: middle;\r\n  user-select: none;\r\n  background-color: transparent;\r\n  border: none;\r\n  border-radius: .25rem;\r\n  transition: background-color .15s ease-out, color .15s ease-out;\r\n}\r\n\r\n.bs-stepper .step-trigger:not(:disabled):not(.disabled) {\r\n  cursor: pointer;\r\n}\r\n\r\n.bs-stepper .step-trigger:disabled,\r\n.bs-stepper .step-trigger.disabled {\r\n  pointer-events: none;\r\n  opacity: .65;\r\n}\r\n\r\n.bs-stepper .step-trigger:focus {\r\n  color: #007bff;\r\n  outline: none;\r\n}\r\n\r\n.bs-stepper .step-trigger:hover {\r\n  text-decoration: none;\r\n  background-color: rgba(0, 0, 0, .06);\r\n}\r\n\r\n@media (max-width: 520px) {\r\n  .bs-stepper .step-trigger {\r\n    flex-direction: column;\r\n    padding: 10px;\r\n  }\r\n}\r\n\r\n.bs-stepper-label {\r\n  display: inline-block;\r\n  margin: .25rem;\r\n}\r\n\r\n.bs-stepper-header {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n@media (max-width: 520px) {\r\n  .bs-stepper-header {\r\n    margin: 0 -10px;\r\n    text-align: center;\r\n  }\r\n}\r\n\r\n.bs-stepper-line,\r\n.bs-stepper .line {\r\n  flex: 1 0 32px;\r\n  min-width: 1px;\r\n  min-height: 1px;\r\n  margin: auto;\r\n  background-color: rgba(0, 0, 0, .12);\r\n}\r\n\r\n@media (max-width: 400px) {\r\n  .bs-stepper-line,\r\n  .bs-stepper .line {\r\n    flex-basis: 20px;\r\n  }\r\n}\r\n\r\n.bs-stepper-circle {\r\n  display: inline-flex;\r\n  align-content: center;\r\n  justify-content: center;\r\n  width: 2em;\r\n  height: 2em;\r\n  padding: .5em 0;\r\n  margin: .25rem;\r\n  line-height: 1em;\r\n  color: #fff;\r\n  background-color: #6c757d;\r\n  border-radius: 1em;\r\n}\r\n\r\n.active .bs-stepper-circle {\r\n  background-color: #007bff;\r\n}\r\n\r\n.bs-stepper-content {\r\n  padding: 0 20px 20px;\r\n}\r\n\r\n@media (max-width: 520px) {\r\n  .bs-stepper-content {\r\n    padding: 0;\r\n  }\r\n}\r\n\r\n.bs-stepper.vertical {\r\n  display: flex;\r\n}\r\n\r\n.bs-stepper.vertical .bs-stepper-header {\r\n  flex-direction: column;\r\n  align-items: stretch;\r\n  margin: 0;\r\n}\r\n\r\n.bs-stepper.vertical .bs-stepper-pane,\r\n.bs-stepper.vertical .content {\r\n  display: block;\r\n}\r\n\r\n.bs-stepper.vertical .bs-stepper-pane:not(.fade),\r\n.bs-stepper.vertical .content:not(.fade) {\r\n  display: block;\r\n  visibility: hidden;\r\n}\r\n\r\n.bs-stepper-pane:not(.fade),\r\n.bs-stepper .content:not(.fade) {\r\n  display: none;\r\n}\r\n\r\n.bs-stepper .content.fade,\r\n.bs-stepper-pane.fade {\r\n  visibility: hidden;\r\n  transition-duration: .3s;\r\n  transition-property: opacity;\r\n}\r\n\r\n.bs-stepper-pane.fade.active,\r\n.bs-stepper .content.fade.active {\r\n  visibility: visible;\r\n  opacity: 1;\r\n}\r\n\r\n.bs-stepper-pane.active:not(.fade),\r\n.bs-stepper .content.active:not(.fade) {\r\n  display: block;\r\n  visibility: visible;\r\n}\r\n\r\n.bs-stepper-pane.dstepper-block,\r\n.bs-stepper .content.dstepper-block {\r\n  display: block;\r\n}\r\n\r\n.bs-stepper:not(.vertical) .bs-stepper-pane.dstepper-none,\r\n.bs-stepper:not(.vertical) .content.dstepper-none {\r\n  display: none;\r\n}\r\n\r\n.vertical .bs-stepper-pane.fade.dstepper-none,\r\n.vertical .content.fade.dstepper-none {\r\n  visibility: hidden;\r\n}\r\n"]}